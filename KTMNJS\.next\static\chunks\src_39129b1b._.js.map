{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport Link, { LinkProps } from \"next/link\";\nimport React, { useState, createContext, useContext } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\n\ninterface Links {\n  label: string;\n  href: string;\n  icon: React.JSX.Element | React.ReactNode;\n}\n\ninterface SidebarContextProps {\n  open: boolean;\n  setOpen: React.Dispatch<React.SetStateAction<boolean>>;\n  animate: boolean;\n}\n\nconst SidebarContext = createContext<SidebarContextProps | undefined>(\n  undefined\n);\n\nexport const useSidebar = () => {\n  const context = useContext(SidebarContext);\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider\");\n  }\n  return context;\n};\n\nexport const SidebarProvider = ({\n  children,\n  open: openProp,\n  setOpen: setOpenProp,\n  animate = true,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  const [openState, setOpenState] = useState(false);\n\n  const open = openProp !== undefined ? openProp : openState;\n  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;\n\n  return (\n    <SidebarContext.Provider value={{ open, setOpen, animate }}>\n      {children}\n    </SidebarContext.Provider>\n  );\n};\n\nexport const Sidebar = ({\n  children,\n  open,\n  setOpen,\n  animate,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  return (\n    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>\n      {children}\n    </SidebarProvider>\n  );\n};\n\nexport const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {\n  return (\n    <>\n      <DesktopSidebar {...props} />\n      <MobileSidebar {...(props as React.ComponentProps<\"div\">)} />\n    </>\n  );\n};\n\nexport const DesktopSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof motion.div>) => {\n  const { open, setOpen, animate } = useSidebar();\n  return (\n    <motion.div\n      className={cn(\n        \"h-full px-4 py-4 hidden md:flex md:flex-col bg-neutral-100 dark:bg-neutral-800 w-[300px] flex-shrink-0\",\n        className\n      )}\n      animate={{\n        width: animate ? (open ? \"300px\" : \"60px\") : \"300px\",\n      }}\n      onMouseEnter={() => setOpen(true)}\n      onMouseLeave={() => setOpen(false)}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport const MobileSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\">) => {\n  const { open, setOpen } = useSidebar();\n  return (\n    <>\n      <div\n        className={cn(\n          \"h-10 px-4 py-4 flex flex-row md:hidden items-center justify-between bg-neutral-100 dark:bg-neutral-800 w-full\"\n        )}\n        {...props}\n      >\n        <div className=\"flex justify-end z-20 w-full\">\n          <Menu\n            className=\"text-neutral-800 dark:text-neutral-200 cursor-pointer\"\n            onClick={() => setOpen(!open)}\n          />\n        </div>\n        <AnimatePresence>\n          {open && (\n            <motion.div\n              initial={{ x: \"-100%\", opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: \"-100%\", opacity: 0 }}\n              transition={{\n                duration: 0.3,\n                ease: \"easeInOut\",\n              }}\n              className={cn(\n                \"fixed h-full w-full inset-0 bg-white dark:bg-neutral-900 p-10 z-[100] flex flex-col justify-between\",\n                className\n              )}\n            >\n              <div\n                className=\"absolute right-10 top-10 z-50 text-neutral-800 dark:text-neutral-200 cursor-pointer\"\n                onClick={() => setOpen(!open)}\n              >\n                <X />\n              </div>\n              {children}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </>\n  );\n};\n\nexport const SidebarLink = ({\n  link,\n  className,\n  ...props\n}: {\n  link: Links;\n  className?: string;\n  props?: LinkProps;\n}) => {\n  const { open, animate } = useSidebar();\n  return (\n    <Link\n      href={link.href}\n      className={cn(\n        \"flex items-center justify-start gap-2 group/sidebar py-2\",\n        className\n      )}\n      {...props}\n    >\n      {link.icon}\n      <motion.span\n        animate={{\n          display: animate ? (open ? \"inline-block\" : \"none\") : \"inline-block\",\n          opacity: animate ? (open ? 1 : 0) : 1,\n        }}\n        className=\"text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0\"\n      >\n        {link.label}\n      </motion.span>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;;;AANA;;;;;;AAoBA,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EACjC;AAGK,MAAM,aAAa;;IACxB,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANa;AAQN,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,MAAM,QAAQ,EACd,SAAS,WAAW,EACpB,UAAU,IAAI,EAMf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,aAAa,YAAY,WAAW;IACjD,MAAM,UAAU,gBAAgB,YAAY,cAAc;IAE1D,qBACE,6LAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;QAAQ;kBACtD;;;;;;AAGP;IArBa;KAAA;AAuBN,MAAM,UAAU,CAAC,EACtB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EAMR;IACC,qBACE,6LAAC;QAAgB,MAAM;QAAM,SAAS;QAAS,SAAS;kBACrD;;;;;;AAGP;MAhBa;AAkBN,MAAM,cAAc,CAAC;IAC1B,qBACE;;0BACE,6LAAC;gBAAgB,GAAG,KAAK;;;;;;0BACzB,6LAAC;gBAAe,GAAI,KAAK;;;;;;;;AAG/B;MAPa;AASN,MAAM,iBAAiB,CAAC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACqC;;IACxC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACnC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAEF,SAAS;YACP,OAAO,UAAW,OAAO,UAAU,SAAU;QAC/C;QACA,cAAc,IAAM,QAAQ;QAC5B,cAAc,IAAM,QAAQ;QAC3B,GAAG,KAAK;kBAER;;;;;;AAGP;IAtBa;;QAKwB;;;MALxB;AAwBN,MAAM,gBAAgB,CAAC,EAC5B,SAAS,EACT,QAAQ,EACR,GAAG,OACyB;;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,qBACE;kBACE,cAAA,6LAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV;YAED,GAAG,KAAK;;8BAET,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,qMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,SAAS,IAAM,QAAQ,CAAC;;;;;;;;;;;8BAG5B,6LAAC,4LAAA,CAAA,kBAAe;8BACb,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAS,SAAS;wBAAE;wBAClC,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAS,SAAS;wBAAE;wBAC/B,YAAY;4BACV,UAAU;4BACV,MAAM;wBACR;wBACA,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uGACA;;0CAGF,6LAAC;gCACC,WAAU;gCACV,SAAS,IAAM,QAAQ,CAAC;0CAExB,cAAA,6LAAC,+LAAA,CAAA,IAAC;;;;;;;;;;4BAEH;;;;;;;;;;;;;;;;;;;AAOf;IAhDa;;QAKe;;;MALf;AAkDN,MAAM,cAAc,CAAC,EAC1B,IAAI,EACJ,SAAS,EACT,GAAG,OAKJ;;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM,KAAK,IAAI;QACf,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;YAER,KAAK,IAAI;0BACV,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBACP,SAAS,UAAW,OAAO,iBAAiB,SAAU;oBACtD,SAAS,UAAW,OAAO,IAAI,IAAK;gBACtC;gBACA,WAAU;0BAET,KAAK,KAAK;;;;;;;;;;;;AAInB;IA/Ba;;QASe;;;MATf", "debugId": null}}, {"offset": {"line": 271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 338, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/preference-service.ts"], "sourcesContent": ["import { UserPreferences, Preference } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Gi<PERSON> trị mặc định cho tùy chọn người dùng\r\nconst DEFAULT_PREFERENCES: UserPreferences = {\r\n  theme: 'system',\r\n  language: 'vi',\r\n  startOfWeek: 'monday',\r\n  showCompletedTasks: true,\r\n  notifications: true,\r\n  soundEnabled: true,\r\n};\r\n\r\n// Cache cho preferences\r\nlet preferencesCache: UserPreferences | null = null;\r\n\r\n// Helper function to convert Preference to UserPreferences\r\nconst mapPreferenceToUserPreferences = (preference: Preference): UserPreferences => {\r\n  return {\r\n    theme: preference.theme,\r\n    language: (preference.language === 'en' ? 'en' : 'vi') as 'vi' | 'en',\r\n    startOfWeek: preference.startOfWeek === 1 ? 'monday' : 'sunday',\r\n    showCompletedTasks: true, // Default value\r\n    notifications: preference.notifications,\r\n    soundEnabled: true, // Default value\r\n  };\r\n};\r\n\r\n// Helper function to convert UserPreferences to Preference updates\r\nconst mapUserPreferencesToPreference = (userPrefs: Partial<UserPreferences>): Partial<Omit<Preference, 'id' | 'userId'>> => {\r\n  const updates: any = {};\r\n\r\n  if (userPrefs.theme) updates.theme = userPrefs.theme;\r\n  if (userPrefs.language) updates.language = userPrefs.language;\r\n  if (userPrefs.startOfWeek) updates.startOfWeek = userPrefs.startOfWeek === 'monday' ? 1 : 0;\r\n  if (userPrefs.notifications !== undefined) updates.notifications = userPrefs.notifications;\r\n\r\n  return updates;\r\n};\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const PreferenceService = {\r\n  getPreferences: async (): Promise<UserPreferences> => {\r\n    const now = Date.now();\r\n    \r\n    // Nếu có cache và chưa hết hạn, trả về cache\r\n    if (preferencesCache && now - lastFetchTime < CACHE_DURATION) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    try {\r\n      // Gọi API để lấy preferences\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = now;\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi lấy tùy chọn người dùng:', error);\r\n      \r\n      // Nếu có lỗi và đã có cache, trả về cache\r\n      if (preferencesCache) {\r\n        return preferencesCache;\r\n      }\r\n      \r\n      // Nếu không có cache, trả về giá trị mặc định\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Phương thức đồng bộ để lấy preferences từ cache (cho các component không thể đợi async)\r\n  getPreferencesSync: (): UserPreferences => {\r\n    if (preferencesCache) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    // Nếu chưa có cache, trả về giá trị mặc định\r\n    return DEFAULT_PREFERENCES;\r\n  },\r\n  \r\n  updatePreferences: async (updates: Partial<UserPreferences>): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert UserPreferences updates to Preference format\r\n      const preferenceUpdates = mapUserPreferencesToPreference(updates);\r\n\r\n      // Gọi API để cập nhật preferences\r\n      const updatedPreference = await ApiService.preferences.update(preferenceUpdates);\r\n      const updatedUserPreferences = mapPreferenceToUserPreferences(updatedPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = updatedUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return updatedUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi cập nhật tùy chọn người dùng:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  resetPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert default preferences to Preference format\r\n      const defaultPreferenceUpdates = mapUserPreferencesToPreference(DEFAULT_PREFERENCES);\r\n\r\n      // Gọi API để đặt lại preferences về mặc định\r\n      const resetPreference = await ApiService.preferences.update(defaultPreferenceUpdates);\r\n      const resetUserPreferences = mapPreferenceToUserPreferences(resetPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = resetUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return resetUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi đặt lại tùy chọn người dùng:', error);\r\n\r\n      // Nếu có lỗi, đặt cache về mặc định\r\n      preferencesCache = DEFAULT_PREFERENCES;\r\n      lastFetchTime = Date.now();\r\n\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    preferencesCache = null;\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = Date.now();\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới tùy chọn người dùng:', error);\r\n      return preferencesCache || DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,2CAA2C;AAC3C,MAAM,sBAAuC;IAC3C,OAAO;IACP,UAAU;IACV,aAAa;IACb,oBAAoB;IACpB,eAAe;IACf,cAAc;AAChB;AAEA,wBAAwB;AACxB,IAAI,mBAA2C;AAE/C,2DAA2D;AAC3D,MAAM,iCAAiC,CAAC;IACtC,OAAO;QACL,OAAO,WAAW,KAAK;QACvB,UAAW,WAAW,QAAQ,KAAK,OAAO,OAAO;QACjD,aAAa,WAAW,WAAW,KAAK,IAAI,WAAW;QACvD,oBAAoB;QACpB,eAAe,WAAW,aAAa;QACvC,cAAc;IAChB;AACF;AAEA,mEAAmE;AACnE,MAAM,iCAAiC,CAAC;IACtC,MAAM,UAAe,CAAC;IAEtB,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,QAAQ,EAAE,QAAQ,QAAQ,GAAG,UAAU,QAAQ;IAC7D,IAAI,UAAU,WAAW,EAAE,QAAQ,WAAW,GAAG,UAAU,WAAW,KAAK,WAAW,IAAI;IAC1F,IAAI,UAAU,aAAa,KAAK,WAAW,QAAQ,aAAa,GAAG,UAAU,aAAa;IAE1F,OAAO;AACT;AACA,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,oBAAoB;IAC/B,gBAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,6CAA6C;QAC7C,IAAI,oBAAoB,MAAM,gBAAgB,gBAAgB;YAC5D,OAAO;QACT;QAEA,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,MAAM,2IAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB;YAChB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,0CAA0C;YAC1C,IAAI,kBAAkB;gBACpB,OAAO;YACT;YAEA,8CAA8C;YAC9C,OAAO;QACT;IACF;IAEA,0FAA0F;IAC1F,oBAAoB;QAClB,IAAI,kBAAkB;YACpB,OAAO;QACT;QAEA,6CAA6C;QAC7C,OAAO;IACT;IAEA,mBAAmB,OAAO;QACxB,IAAI;YACF,uDAAuD;YACvD,MAAM,oBAAoB,+BAA+B;YAEzD,kCAAkC;YAClC,MAAM,oBAAoB,MAAM,2IAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9D,MAAM,yBAAyB,+BAA+B;YAE9D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;QAChB,IAAI;YACF,mDAAmD;YACnD,MAAM,2BAA2B,+BAA+B;YAEhE,6CAA6C;YAC7C,MAAM,kBAAkB,MAAM,2IAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC5D,MAAM,uBAAuB,+BAA+B;YAE5D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,oCAAoC;YACpC,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,YAAY;QACV,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,oBAAoB;QAClB,IAAI;YACF,MAAM,aAAa,MAAM,2IAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,oBAAoB;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/clear-data-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { PreferenceService } from \"@/lib/services/preference-service\";\nimport { Trash2 } from \"lucide-react\";\nimport { useSidebar } from \"@/components/ui/sidebar\";\nimport { motion } from \"framer-motion\";\n\nexport function ClearDataButton() {\n  const { open, animate } = useSidebar();\n  \n  const handleClearData = () => {\n    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu? Hành động này không thể hoàn tác.')) {\n      // Clear localStorage\n      localStorage.clear();\n      // Clear preferences cache\n      PreferenceService.clearCache();\n      // Reload page\n      window.location.reload();\n    }\n  };\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={handleClearData}\n      className=\"w-full flex items-center gap-2 justify-start px-2 text-destructive\"\n    >\n      <Trash2 className=\"h-5 w-5\" />\n      <motion.span\n        initial={{ opacity: 0, width: 0 }}\n        animate={{\n          opacity: animate ? (open ? 1 : 0) : 1,\n          width: animate ? (open ? \"auto\" : 0) : \"auto\",\n          marginLeft: animate ? (open ? \"0.5rem\" : 0) : \"0.5rem\",\n          display: \"inline-block\"\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"whitespace-pre overflow-hidden\"\n      >\n        Xóa dữ liệu\n      </motion.span>\n    </Button>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAQO,SAAS;;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,gFAAgF;YACjG,qBAAqB;YACrB,aAAa,KAAK;YAClB,0BAA0B;YAC1B,kJAAA,CAAA,oBAAiB,CAAC,UAAU;YAC5B,cAAc;YACd,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,qBACE,6LAAC,qIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,6LAAC,6MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,UAAW,OAAO,IAAI,IAAK;oBACpC,OAAO,UAAW,OAAO,SAAS,IAAK;oBACvC,YAAY,UAAW,OAAO,WAAW,IAAK;oBAC9C,SAAS;gBACX;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAKP;GArCgB;;QACY,sIAAA,CAAA,aAAU;;;KADtB", "debugId": null}}, {"offset": {"line": 559, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,6LAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 595, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,sKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,sKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,sKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,sKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,sKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 638, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,6LAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 715, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/footer-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  <PERSON><PERSON><PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Github, Send, CheckCircle, AlertCircle } from \"lucide-react\"\nimport { ApiService } from \"@/lib/services/api-service\"\n\nfunction Footerdemo() {\n  const [email, setEmail] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)\n\n  const handleEmailSubscription = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!email) {\n      setMessage({ type: 'error', text: '<PERSON><PERSON> lòng nhập địa chỉ email' })\n      return\n    }\n\n    // <PERSON><PERSON><PERSON> tra định dạng email\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(email)) {\n      setMessage({ type: 'error', text: 'Địa chỉ email không hợp lệ' })\n      return\n    }\n\n    setIsLoading(true)\n    setMessage(null)\n\n    try {\n      // Sử dụng API public - không cần đăng nhập\n      await ApiService.notifications.subscribeEmailPublic({\n        email: email,\n        name: '', // Có thể để trống hoặc thêm field name nếu muốn\n        taskReminders: true,\n        dailySummary: false,\n        weeklyReport: false,\n        reminderHours: 24,\n      })\n\n      setMessage({\n        type: 'success',\n        text: 'Đăng ký thành công! Bạn sẽ nhận được thông báo qua email. Kiểm tra hộp thư để xác nhận.'\n      })\n      setEmail('')\n    } catch (error: any) {\n      console.error('Lỗi đăng ký email:', error)\n      setMessage({\n        type: 'error',\n        text: error.message || 'Đăng ký thất bại. Vui lòng thử lại.'\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <footer className=\"relative border-t bg-background/80 backdrop-blur-sm text-foreground transition-colors duration-300\">\n      <div className=\"container mx-auto px-4 py-6 md:px-6 lg:px-8\">\n        <div className=\"grid gap-4 md:gap-6 md:grid-cols-2 lg:grid-cols-4\">\n          <div className=\"relative md:col-span-2 lg:col-span-1\">\n            <h2 className=\"mb-2 text-lg md:text-xl font-bold tracking-tight\">QLTime</h2>\n            <p className=\"mb-3 text-xs md:text-sm text-muted-foreground\">\n              Giải pháp quản lý thời gian thông minh cho cuộc sống hiện đại.\n            </p>\n            <form onSubmit={handleEmailSubscription} className=\"relative\">\n              <Input\n                type=\"email\"\n                placeholder=\"Đăng ký nhận thông báo\"\n                className=\"pr-10 backdrop-blur-sm text-sm h-8\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                disabled={isLoading}\n              />\n              <Button\n                type=\"submit\"\n                size=\"icon\"\n                className=\"absolute right-1 top-1 h-6 w-6 rounded-full bg-primary text-primary-foreground transition-transform hover:scale-105 disabled:opacity-50\"\n                disabled={isLoading}\n              >\n                {isLoading ? (\n                  <div className=\"h-3 w-3 animate-spin rounded-full border-2 border-white border-t-transparent\" />\n                ) : (\n                  <Send className=\"h-3 w-3\" />\n                )}\n                <span className=\"sr-only\">Đăng ký</span>\n              </Button>\n            </form>\n\n            {message && (\n              <Alert className={`mt-2 py-2 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>\n                <div className=\"flex items-center gap-2\">\n                  {message.type === 'success' ? (\n                    <CheckCircle className=\"h-3 w-3 text-green-600\" />\n                  ) : (\n                    <AlertCircle className=\"h-3 w-3 text-red-600\" />\n                  )}\n                  <AlertDescription className={`text-xs ${message.type === 'success' ? 'text-green-800' : 'text-red-800'}`}>\n                    {message.text}\n                  </AlertDescription>\n                </div>\n              </Alert>\n            )}\n            <div className=\"absolute -right-4 top-0 h-24 w-24 rounded-full bg-primary/10 blur-2xl\" />\n          </div>\n          <div className=\"hidden md:block\">\n            <h3 className=\"mb-2 text-base font-semibold\">Tính năng</h3>\n            <nav className=\"space-y-1 text-sm\">\n              <a href=\"/tasks\" className=\"block transition-colors hover:text-primary\">\n                Quản lý công việc\n              </a>\n              <a href=\"/calendar\" className=\"block transition-colors hover:text-primary\">\n                Lịch & Timeblocks\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Thống kê\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Tùy chỉnh\n              </a>\n            </nav>\n          </div>\n          <div className=\"hidden lg:block\">\n            <h3 className=\"mb-2 text-base font-semibold\">Liên hệ</h3>\n            <address className=\"space-y-1 text-sm not-italic\">\n              <p>Email: <EMAIL></p>\n              <p>SĐT: **********</p>\n            </address>\n          </div>\n          <div className=\"relative\">\n            <h3 className=\"mb-2 text-base font-semibold\">Theo dõi</h3>\n            <div className=\"mb-3 flex space-x-4\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button \n                      variant=\"outline\" \n                      size=\"icon\" \n                      className=\"rounded-full\"\n                      asChild\n                    >\n                      <a href=\"https://github.com/hungtvu113/WebsiteTimE\" target=\"_blank\" rel=\"noopener noreferrer\">\n                        <Github className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Github</span>\n                      </a>\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Xem mã nguồn trên Github</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-4 flex flex-col items-center justify-between gap-2 border-t pt-3 text-center md:flex-row\">\n          <p className=\"text-xs md:text-sm text-muted-foreground\">\n            © 2024 QLTime. Bản quyền thuộc về nhóm phát triển.\n          </p>\n          <nav className=\"hidden md:flex gap-4 text-sm\">\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Chính sách bảo mật\n            </a>\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Điều khoản sử dụng\n            </a>\n          </nav>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport { Footerdemo }"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;;;AAfA;;;;;;;;AAiBA,SAAS;;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsD;IAE3F,MAAM,0BAA0B,OAAO;QACrC,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA8B;YAChE;QACF;QAEA,2BAA2B;QAC3B,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA6B;YAC/D;QACF;QAEA,aAAa;QACb,WAAW;QAEX,IAAI;YACF,2CAA2C;YAC3C,MAAM,2IAAA,CAAA,aAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBAClD,OAAO;gBACP,MAAM;gBACN,eAAe;gBACf,cAAc;gBACd,cAAc;gBACd,eAAe;YACjB;YAEA,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,SAAS;QACX,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW;gBACT,MAAM;gBACN,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAmD;;;;;;8CACjE,6LAAC;oCAAE,WAAU;8CAAgD;;;;;;8CAG7D,6LAAC;oCAAK,UAAU;oCAAyB,WAAU;;sDACjD,6LAAC,oIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,UAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;;gDAET,0BACC,6LAAC;oDAAI,WAAU;;;;;yEAEf,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAElB,6LAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI7B,yBACC,6LAAC,oIAAA,CAAA,QAAK;oCAAC,WAAW,CAAC,UAAU,EAAE,QAAQ,IAAI,KAAK,YAAY,iCAAiC,4BAA4B;8CACvH,cAAA,6LAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,KAAK,0BAChB,6LAAC,8NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,6LAAC,oIAAA,CAAA,mBAAgB;gDAAC,WAAW,CAAC,QAAQ,EAAE,QAAQ,IAAI,KAAK,YAAY,mBAAmB,gBAAgB;0DACrG,QAAQ,IAAI;;;;;;;;;;;;;;;;;8CAKrB,6LAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,MAAK;4CAAS,WAAU;sDAA6C;;;;;;sDAGxE,6LAAC;4CAAE,MAAK;4CAAY,WAAU;sDAA6C;;;;;;sDAG3E,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;sDAGnE,6LAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAKvE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;sDAAE;;;;;;sDACH,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGP,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA+B;;;;;;8CAC7C,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,sIAAA,CAAA,kBAAe;kDACd,cAAA,6LAAC,sIAAA,CAAA,UAAO;;8DACN,6LAAC,sIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,OAAO;kEAEP,cAAA,6LAAC;4DAAE,MAAK;4DAA4C,QAAO;4DAAS,KAAI;;8EACtE,6LAAC,yMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,6LAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;8DAIhC,6LAAC,sIAAA,CAAA,iBAAc;8DACb,cAAA,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA2C;;;;;;sCAGxD,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;8CAG7D,6LAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE;GArKS;KAAA", "debugId": null}}, {"offset": {"line": 1193, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,+KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,+KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,+KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,+KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,+KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,+KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,6LAAC,yNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;;AAGb,uBAAuB,WAAW,GAChC,+KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,6LAAC,+KAAA,CAAA,SAA4B;kBAC3B,cAAA,6LAAC,+KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,+KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,iBAAiB,WAAW,GAAG,+KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,6LAAC,+KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,uMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;;AAGL,yBAAyB,WAAW,GAClC,+KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,6LAAC;gBAAK,WAAU;0BACd,cAAA,6LAAC,+KAAA,CAAA,gBAAmC;8BAClC,cAAA,6LAAC,yMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;;AAGL,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,6LAAC,+KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,+KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,+KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;;AAGb,sBAAsB,WAAW,GAAG,+KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;OAVM;AAWN,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1421, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;;AAGb,OAAO,WAAW,GAAG,qKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;;AAGb,eAAe,WAAW,GAAG,qKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1485, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { LogOut, User, Setting<PERSON>, BarChart3, UserCircle } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ApiService } from \"@/lib/services/api-service\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\nexport function Header() {\r\n  const router = useRouter();\r\n  const [user, setUser] = React.useState<any>(null);\r\n\r\n  React.useEffect(() => {\r\n    const fetchUser = async () => {\r\n      try {\r\n        const userData = await ApiService.auth.getCurrentUser();\r\n        setUser(userData);\r\n      } catch (error) {\r\n        console.error('Lỗi lấy thông tin user:', error);\r\n      }\r\n    };\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n\r\n\r\n  const handleLogout = () => {\r\n    ApiService.auth.logout();\r\n    router.push('/login');\r\n  };\r\n\r\n  const handleProfile = () => {\r\n    router.push('/profile');\r\n  };\r\n\r\n  const handleSettings = () => {\r\n    router.push('/settings');\r\n  };\r\n\r\n  const handleStatistics = () => {\r\n    router.push('/statistics');\r\n  };\r\n\r\n  // Tạo initials từ tên user\r\n  const getInitials = (name: string) => {\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-10 backdrop-blur-sm bg-background/80 border-b\">\r\n      <div className=\"container flex h-14 items-center justify-between\">\r\n        <div className=\"mr-4 hidden md:flex\">\r\n          {/* Có thể thêm các phần tử khác ở đây nếu cần */}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-2\">\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 w-8 rounded-full hover:bg-background/80 transition-colors\"\r\n              >\r\n                <Avatar className=\"h-8 w-8\">\r\n                  <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />\r\n                  <AvatarFallback className=\"bg-primary text-primary-foreground\">\r\n                    {user?.name ? getInitials(user.name) : <UserCircle className=\"h-4 w-4\" />}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent\r\n              className=\"w-56 backdrop-blur-sm border border-border/40 bg-background/90 shadow-lg animate-in fade-in-0 zoom-in-95\"\r\n              align=\"end\"\r\n              forceMount\r\n              sideOffset={5}\r\n            >\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-medium leading-none\">\r\n                    {user?.name || 'Người dùng'}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || '<EMAIL>'}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleProfile}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <User className=\"mr-2 h-4 w-4\" />\r\n                <span>Hồ sơ cá nhân</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleStatistics}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <BarChart3 className=\"mr-2 h-4 w-4\" />\r\n                <span>Thống kê</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleSettings}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                <span>Cài đặt</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleLogout}\r\n                className=\"cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors\"\r\n              >\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span>Đăng xuất</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAOA;AAQA;;;AArBA;;;;;;;;AAuBO,SAAS;;IACd,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAO;IAE5C,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4BAAE;YACd,MAAM;8CAAY;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,2IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,cAAc;wBACrD,QAAQ;oBACV,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,2BAA2B;oBAC3C;gBACF;;YAEA;QACF;2BAAG,EAAE;IAIL,MAAM,eAAe;QACnB,2IAAA,CAAA,aAAU,CAAC,IAAI,CAAC,MAAM;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;;;;;8BAIf,6LAAC;oBAAI,WAAU;8BAEb,cAAA,6LAAC,+IAAA,CAAA,eAAY;;0CACX,6LAAC,+IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,qIAAA,CAAA,cAAW;gDAAC,KAAK,MAAM;gDAAQ,KAAK,MAAM,QAAQ;;;;;;0DACnD,6LAAC,qIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,MAAM,OAAO,YAAY,KAAK,IAAI,kBAAI,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrE,6LAAC,+IAAA,CAAA,sBAAmB;gCAClB,WAAU;gCACV,OAAM;gCACN,UAAU;gCACV,YAAY;;kDAEZ,6LAAC,+IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;8DAEjB,6LAAC;oDAAE,WAAU;8DACV,MAAM,SAAS;;;;;;;;;;;;;;;;;kDAItB,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,6LAAC,qNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,+IAAA,CAAA,wBAAqB;;;;;kDACtB,6LAAC,+IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;GAzHgB;;QACC,qIAAA,CAAA,YAAS;;;KADV", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/ai-service.ts"], "sourcesContent": ["import { GoogleGenerativeAI } from \"@google/generative-ai\";\r\nimport { Task } from '../types';\r\n\r\n// Khởi tạo Gemini AI\r\nlet genAI: GoogleGenerativeAI | null = null;\r\nlet model: any = null;\r\n\r\n// Lấy API key từ API route\r\nconst getApiKeyFromServer = async (): Promise<string | null> => {\r\n  try {\r\n    const response = await fetch('/api/ai/api-key');\r\n    if (!response.ok) {\r\n      console.error('Failed to get API key from server');\r\n      return null;\r\n    }\r\n    const data = await response.json();\r\n    return data.apiKey || null;\r\n  } catch (error) {\r\n    console.error('Error fetching API key:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Khởi tạo AI service với API key từ server\r\nconst initializeAI = async () => {\r\n  if (model) return true; // Đã khởi tạo rồi\r\n\r\n  const apiKey = await getApiKeyFromServer();\r\n  if (apiKey && !genAI) {\r\n    genAI = new GoogleGenerativeAI(apiKey);\r\n    model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n  }\r\n  return !!model;\r\n};\r\n\r\n// Chat history interface\r\nexport interface ChatMessage {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport const AIService = {\r\n  // Phân tích nội dung công việc và đề xuất độ ưu tiên bằng AI\r\n  suggestPriority: async (title: string, description?: string): Promise<'high' | 'medium' | 'low'> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        // Fallback to keyword-based logic if AI not available\r\n        return AIService.fallbackSuggestPriority(title, description);\r\n      }\r\n\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất độ ưu tiên (high/medium/low):\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\n\r\nHãy trả về chỉ một từ: \"high\", \"medium\", hoặc \"low\" dựa trên:\r\n- Tính khẩn cấp của công việc\r\n- Tầm quan trọng\r\n- Deadline ngầm định\r\n- Từ khóa chỉ độ ưu tiên\r\n\r\nChỉ trả về một từ, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().toLowerCase().trim();\r\n\r\n      if (['high', 'medium', 'low'].includes(response)) {\r\n        return response as 'high' | 'medium' | 'low';\r\n      }\r\n\r\n      return 'medium';\r\n    } catch (error) {\r\n      console.error(\"Error suggesting priority with AI:\", error);\r\n      return AIService.fallbackSuggestPriority(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for priority suggestion\r\n  fallbackSuggestPriority: (title: string, description?: string): 'high' | 'medium' | 'low' => {\r\n    if (!title) return 'medium';\r\n\r\n    const lowerTitle = title.toLowerCase();\r\n    const lowerDesc = description?.toLowerCase() || '';\r\n\r\n    const highPriorityKeywords = ['gấp', 'khẩn', 'ngay', 'quan trọng', 'deadline', 'hạn chót'];\r\n    const lowPriorityKeywords = ['nhẹ nhàng', 'khi rảnh', 'không gấp', 'sau này', 'phụ'];\r\n\r\n    for (const keyword of highPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'high';\r\n      }\r\n    }\r\n\r\n    for (const keyword of lowPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'low';\r\n      }\r\n    }\r\n\r\n    return 'medium';\r\n  },\r\n  \r\n  // Đề xuất thời gian hoàn thành dựa trên nội dung bằng AI\r\n  suggestDueDate: async (title: string, description?: string): Promise<string | null> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const today = new Date();\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất số ngày cần để hoàn thành:\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\nNgày hiện tại: ${today.toLocaleDateString('vi-VN')}\r\n\r\nHãy trả về chỉ một số nguyên (1-365) đại diện cho số ngày cần để hoàn thành công việc này.\r\nXem xét:\r\n- Độ phức tạp của công việc\r\n- Thời gian thông thường cần thiết\r\n- Từ khóa về thời gian (gấp, khẩn, tuần này, tháng này, etc.)\r\n\r\nChỉ trả về số nguyên, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n      const daysToAdd = parseInt(response);\r\n\r\n      if (isNaN(daysToAdd) || daysToAdd < 1 || daysToAdd > 365) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error suggesting due date with AI:\", error);\r\n      return AIService.fallbackSuggestDueDate(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for due date suggestion\r\n  fallbackSuggestDueDate: (title: string, description?: string): string | null => {\r\n    try {\r\n      const lowerTitle = title.toLowerCase();\r\n      const lowerDesc = description?.toLowerCase() || '';\r\n\r\n      const today = new Date();\r\n      let daysToAdd = 7;\r\n\r\n      if (lowerTitle.includes('gấp') || lowerTitle.includes('khẩn') || lowerDesc.includes('gấp')) {\r\n        daysToAdd = 1;\r\n      } else if (lowerTitle.includes('tuần này') || lowerDesc.includes('tuần này')) {\r\n        daysToAdd = 5;\r\n      } else if (lowerTitle.includes('tháng') || lowerDesc.includes('tháng')) {\r\n        daysToAdd = 30;\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error in fallback due date suggestion:\", error);\r\n      return null;\r\n    }\r\n  },\r\n  \r\n  // Tóm tắt danh sách công việc bằng AI\r\n  summarizeTasks: async (tasks: Task[]): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSummarizeTasks(tasks);\r\n      }\r\n\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const pending = tasks.length - completed;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      // Tạo context cho AI\r\n      const taskSummary = tasks.map(task =>\r\n        `- ${task.title} (${task.priority} priority, ${task.completed ? 'completed' : 'pending'})`\r\n      ).join('\\n');\r\n\r\n      const prompt = `\r\nPhân tích danh sách công việc sau và tạo một bản tóm tắt thông minh bằng tiếng Việt với Markdown format:\r\n\r\n## Thống kê hiện tại:\r\n- **Tổng số công việc**: ${tasks.length}\r\n- **Đã hoàn thành**: ${completed}\r\n- **Chưa hoàn thành**: ${pending}\r\n- **Tỷ lệ hoàn thành**: ${completionRate}%\r\n- **Công việc ưu tiên cao chưa hoàn thành**: ${highPriorityPending}\r\n\r\n## Danh sách công việc:\r\n${taskSummary || 'Không có công việc nào'}\r\n\r\nHãy tạo một bản tóm tắt với Markdown format bao gồm:\r\n1. **Đánh giá hiện trạng** - sử dụng emoji và bold\r\n2. **Lời khuyên hoặc động viên** - sử dụng blockquote\r\n3. **Gợi ý hành động tiếp theo** - sử dụng danh sách có dấu đầu dòng\r\n\r\nSử dụng:\r\n- **Bold** cho các điểm quan trọng\r\n- > Blockquote cho lời khuyên\r\n- • Bullet points cho các bước hành động\r\n- 📊 📈 ✅ ⚡ 🎯 emoji phù hợp\r\n\r\nGiữ giọng điệu thân thiện và tích cực.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n\r\n      return response || AIService.fallbackSummarizeTasks(tasks);\r\n    } catch (error) {\r\n      console.error(\"Error summarizing tasks with AI:\", error);\r\n      return AIService.fallbackSummarizeTasks(tasks);\r\n    }\r\n  },\r\n\r\n  // Fallback method for task summarization\r\n  fallbackSummarizeTasks: (tasks: Task[]): string => {\r\n    try {\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      let summary = `Bạn đã hoàn thành ${completed}/${tasks.length} công việc (${completionRate}%). `;\r\n\r\n      if (highPriorityPending > 0) {\r\n        summary += `Hiện có ${highPriorityPending} công việc ưu tiên cao cần hoàn thành. `;\r\n      }\r\n\r\n      if (completionRate >= 80) {\r\n        summary += 'Bạn đang làm rất tốt! Hãy tiếp tục phát huy.';\r\n      } else if (completionRate >= 50) {\r\n        summary += 'Bạn đang tiến triển tốt. Cố gắng hoàn thành các công việc còn lại nhé.';\r\n      } else if (tasks.length > 0) {\r\n        summary += 'Bạn cần tập trung hơn để hoàn thành nhiều công việc hơn.';\r\n      } else {\r\n        summary = 'Bạn chưa có công việc nào. Hãy thêm một số công việc để bắt đầu.';\r\n      }\r\n\r\n      return summary;\r\n    } catch (error) {\r\n      console.error(\"Error in fallback task summarization:\", error);\r\n      return \"Không thể tạo bản tóm tắt tại thời điểm này.\";\r\n    }\r\n  },\r\n  \r\n  // Chat với AI (chỉ sử dụng client-side)\r\n  chat: async (message: string, chatHistory: ChatMessage[] = []): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n      }\r\n\r\n      // Tạo context từ lịch sử chat\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      return result.response.text().trim();\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat:\", error);\r\n      return \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Chat streaming với Gemini\r\n  chatStream: async function* (message: string, chatHistory: ChatMessage[] = []): AsyncGenerator<string, void, unknown> {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        yield \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n        return;\r\n      }\r\n\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContentStream(prompt);\r\n\r\n      for await (const chunk of result.stream) {\r\n        const chunkText = chunk.text();\r\n        if (chunkText) {\r\n          yield chunkText;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat stream:\", error);\r\n      yield \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Kích hoạt API Gemini khi có API key\r\n  initWithApiKey: (apiKey: string) => {\r\n    try {\r\n      genAI = new GoogleGenerativeAI(apiKey);\r\n      model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n      console.log(\"AI service initialized with API key\");\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error initializing AI service:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  // Kiểm tra trạng thái AI\r\n  isAvailable: (): boolean => {\r\n    return !!model;\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAGA,qBAAqB;AACrB,IAAI,QAAmC;AACvC,IAAI,QAAa;AAEjB,2BAA2B;AAC3B,MAAM,sBAAsB;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,MAAM,IAAI;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA,4CAA4C;AAC5C,MAAM,eAAe;IACnB,IAAI,OAAO,OAAO,MAAM,kBAAkB;IAE1C,MAAM,SAAS,MAAM;IACrB,IAAI,UAAU,CAAC,OAAO;QACpB,QAAQ,IAAI,iKAAA,CAAA,qBAAkB,CAAC;QAC/B,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAiC;IAC7E;IACA,OAAO,CAAC,CAAC;AACX;AASO,MAAM,YAAY;IACvB,6DAA6D;IAC7D,iBAAiB,OAAO,OAAe;QACrC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,sDAAsD;gBACtD,OAAO,UAAU,uBAAuB,CAAC,OAAO;YAClD;YAEA,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;;;;;;;;oCAQL,CAAC;YAE/B,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,WAAW,GAAG,IAAI;YAE1D,IAAI;gBAAC;gBAAQ;gBAAU;aAAM,CAAC,QAAQ,CAAC,WAAW;gBAChD,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,uBAAuB,CAAC,OAAO;QAClD;IACF;IAEA,0CAA0C;IAC1C,yBAAyB,CAAC,OAAe;QACvC,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,YAAY,aAAa,iBAAiB;QAEhD,MAAM,uBAAuB;YAAC;YAAO;YAAQ;YAAQ;YAAc;YAAY;SAAW;QAC1F,MAAM,sBAAsB;YAAC;YAAa;YAAY;YAAa;YAAW;SAAM;QAEpF,KAAK,MAAM,WAAW,qBAAsB;YAC1C,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,KAAK,MAAM,WAAW,oBAAqB;YACzC,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,yDAAyD;IACzD,gBAAgB,OAAO,OAAe;QACpC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,QAAQ,IAAI;YAClB,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;eAC1B,EAAE,MAAM,kBAAkB,CAAC,SAAS;;;;;;;;uCAQZ,CAAC;YAElC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAC5C,MAAM,YAAY,SAAS;YAE3B,IAAI,MAAM,cAAc,YAAY,KAAK,YAAY,KAAK;gBACxD,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,sBAAsB,CAAC,OAAO;QACjD;IACF;IAEA,0CAA0C;IAC1C,wBAAwB,CAAC,OAAe;QACtC,IAAI;YACF,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,YAAY,aAAa,iBAAiB;YAEhD,MAAM,QAAQ,IAAI;YAClB,IAAI,YAAY;YAEhB,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,QAAQ;gBAC1F,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,aAAa;gBAC5E,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBACtE,YAAY;YACd;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC;YAC1C;YAEA,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,UAAU,MAAM,MAAM,GAAG;YAC/B,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,qBAAqB;YACrB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAC5B,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,SAAS,GAAG,cAAc,UAAU,CAAC,CAAC,EAC1F,IAAI,CAAC;YAEP,MAAM,SAAS,CAAC;;;;yBAIG,EAAE,MAAM,MAAM,CAAC;qBACnB,EAAE,UAAU;uBACV,EAAE,QAAQ;wBACT,EAAE,eAAe;6CACI,EAAE,oBAAoB;;;AAGnE,EAAE,eAAe,yBAAyB;;;;;;;;;;;;;sCAaJ,CAAC;YAEjC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAE5C,OAAO,YAAY,UAAU,sBAAsB,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,UAAU,sBAAsB,CAAC;QAC1C;IACF;IAEA,yCAAyC;IACzC,wBAAwB,CAAC;QACvB,IAAI;YACF,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,IAAI,UAAU,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,MAAM,MAAM,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC;YAE/F,IAAI,sBAAsB,GAAG;gBAC3B,WAAW,CAAC,QAAQ,EAAE,oBAAoB,uCAAuC,CAAC;YACpF;YAEA,IAAI,kBAAkB,IAAI;gBACxB,WAAW;YACb,OAAO,IAAI,kBAAkB,IAAI;gBAC/B,WAAW;YACb,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG;gBAC3B,WAAW;YACb,OAAO;gBACL,UAAU;YACZ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,MAAM,OAAO,SAAiB,cAA6B,EAAE;QAC3D,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO;YACT;YAEA,8BAA8B;YAC9B,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,OAAO,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,YAAY,gBAAiB,OAAe,EAAE,cAA6B,EAAE;QAC3E,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,MAAM;gBACN;YACF;YAEA,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,qBAAqB,CAAC;YAEjD,WAAW,MAAM,SAAS,OAAO,MAAM,CAAE;gBACvC,MAAM,YAAY,MAAM,IAAI;gBAC5B,IAAI,WAAW;oBACb,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,gBAAgB,CAAC;QACf,IAAI;YACF,QAAQ,IAAI,iKAAA,CAAA,qBAAkB,CAAC;YAC/B,QAAQ,MAAM,kBAAkB,CAAC;gBAAE,OAAO;YAAiC;YAC3E,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,aAAa;QACX,OAAO,CAAC,CAAC;IACX;AACF", "debugId": null}}, {"offset": {"line": 2129, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/task-service.ts"], "sourcesContent": ["import { Task } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Tạo một lớp cache đơn giản để lưu trữ tạm thởi\r\nlet tasksCache: Task[] = [];\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const TaskService = {\r\n  getTasks: async (): Promise<Task[]> => {\r\n    const now = Date.now();\r\n    // Kiểm tra xem cache có hết hạn chưa\r\n    if (now - lastFetchTime > CACHE_DURATION || tasksCache.length === 0) {\r\n      try {\r\n        const tasks = await ApiService.tasks.getAll();\r\n        tasksCache = tasks;\r\n        lastFetchTime = now;\r\n        return tasks;\r\n      } catch (error) {\r\n        console.error('Lỗi khi lấy danh sách công việc:', error);\r\n        // Nếu có lỗi, trả về cache hiện tại hoặc mảng rỗng\r\n        return tasksCache.length > 0 ? tasksCache : [];\r\n      }\r\n    }\r\n    return tasksCache;\r\n  },\r\n  \r\n  getTask: async (id: string): Promise<Task | undefined> => {\r\n    try {\r\n      // Kiểm tra trong cache trước\r\n      const cachedTask = tasksCache.find(task => task.id === id);\r\n      if (cachedTask) return cachedTask;\r\n\r\n      // Nếu không có trong cache, gọi API\r\n      const task = await ApiService.tasks.getById(id);\r\n\r\n      // API trả về null cho 404, không throw error\r\n      if (task === null) {\r\n        console.debug(`Task với ID ${id} không tồn tại (đã bị xóa hoặc không hợp lệ)`);\r\n        return undefined;\r\n      }\r\n\r\n      return task;\r\n    } catch (error: any) {\r\n      // Chỉ log lỗi cho các lỗi khác (không phải 404)\r\n      console.error(`Lỗi khi lấy công việc với id ${id}:`, error);\r\n      return undefined;\r\n    }\r\n  },\r\n  \r\n  createTask: async (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> => {\r\n    try {\r\n      const newTask = await ApiService.tasks.create(task);\r\n      // Cập nhật cache\r\n      tasksCache = [...tasksCache, newTask];\r\n      return newTask;\r\n    } catch (error) {\r\n      console.error('Lỗi khi tạo công việc mới:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  updateTask: async (id: string, updates: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> => {\r\n    try {\r\n      console.log(`TaskService: Đang cập nhật task ${id} với data:`, updates);\r\n      const updatedTask = await ApiService.tasks.update(id, updates);\r\n      console.log(`TaskService: Task ${id} đã được cập nhật:`, updatedTask);\r\n\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`TaskService: Lỗi khi cập nhật công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  deleteTask: async (id: string): Promise<void> => {\r\n    try {\r\n      await ApiService.tasks.delete(id);\r\n      // Cập nhật cache\r\n      tasksCache = tasksCache.filter(task => task.id !== id);\r\n    } catch (error) {\r\n      console.error(`Lỗi khi xóa công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  toggleTaskCompletion: async (id: string): Promise<Task> => {\r\n    try {\r\n      const updatedTask = await ApiService.tasks.toggleCompletion(id);\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`Lỗi khi chuyển trạng thái hoàn thành của công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Phương thức để xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    tasksCache = [];\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshTasks: async (): Promise<Task[]> => {\r\n    try {\r\n      const tasks = await ApiService.tasks.getAll();\r\n      tasksCache = tasks;\r\n      lastFetchTime = Date.now();\r\n      return tasks;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới danh sách công việc:', error);\r\n      return tasksCache;\r\n    }\r\n  }\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,iDAAiD;AACjD,IAAI,aAAqB,EAAE;AAC3B,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,cAAc;IACzB,UAAU;QACR,MAAM,MAAM,KAAK,GAAG;QACpB,qCAAqC;QACrC,IAAI,MAAM,gBAAgB,kBAAkB,WAAW,MAAM,KAAK,GAAG;YACnE,IAAI;gBACF,MAAM,QAAQ,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,mDAAmD;gBACnD,OAAO,WAAW,MAAM,GAAG,IAAI,aAAa,EAAE;YAChD;QACF;QACA,OAAO;IACT;IAEA,SAAS,OAAO;QACd,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACvD,IAAI,YAAY,OAAO;YAEvB,oCAAoC;YACpC,MAAM,OAAO,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO,CAAC;YAE5C,6CAA6C;YAC7C,IAAI,SAAS,MAAM;gBACjB,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,GAAG,4CAA4C,CAAC;gBAC7E,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,gDAAgD;YAChD,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,OAAO;QACT;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,UAAU,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,iBAAiB;YACjB,aAAa;mBAAI;gBAAY;aAAQ;YACrC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,YAAY,OAAO,IAAY;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,GAAG,UAAU,CAAC,EAAE;YAC/D,MAAM,cAAc,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YACtD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,kBAAkB,CAAC,EAAE;YAEzD,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9B,iBAAiB;YACjB,aAAa,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,MAAM;QACR;IACF;IAEA,sBAAsB,OAAO;QAC3B,IAAI;YACF,MAAM,cAAc,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC;YAC5D,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,GAAG,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,YAAY;QACV,aAAa,EAAE;QACf,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,cAAc;QACZ,IAAI;YACF,MAAM,QAAQ,MAAM,2IAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YAC3C,aAAa;YACb,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 2256, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Cross2Icon } from \"@radix-ui/react-icons\";\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-[51] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-1/2 top-1/2 z-[51] grid max-h-[calc(100%-4rem)] w-full -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto border bg-background/90 backdrop-blur-md p-6 shadow-lg shadow-black/10 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:max-w-[400px] sm:rounded-xl\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"group absolute right-3 top-3 flex size-7 items-center justify-center rounded-lg outline-offset-2 transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none\">\n        <Cross2Icon\n          width={16}\n          height={16}\n          strokeWidth={2}\n          className=\"opacity-60 transition-opacity group-hover:opacity-100\"\n        />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)} {...props} />\n);\nDialogHeader.displayName = \"DialogHeader\";\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3\", className)}\n    {...props}\n  />\n);\nDialogFooter.displayName = \"DialogFooter\";\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold tracking-tight\", className)}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,SAAS,qKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,qKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,qKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,qKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gJACA;QAED,GAAG,KAAK;;;;;;KAVP;AAaN,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC;;0BACC,6LAAC;;;;;0BACD,6LAAC,qKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6kBACA;gBAED,GAAG,KAAK;;oBAER;kCACD,6LAAC,qKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,6LAAC,mLAAA,CAAA,aAAU;gCACT,OAAO;gCACP,QAAQ;gCACR,aAAa;gCACb,WAAU;;;;;;0CAEZ,6LAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,qKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QAAa,GAAG,KAAK;;;;;;MAD1F;AAGN,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;MAHP;AAMN,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,qKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,qKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,kBAAkB,WAAW,GAAG,qKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2410, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,mKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;;AAGb,SAAS,WAAW,GAAG,mKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC,mKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;;AAGb,YAAY,WAAW,GAAG,mKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2476, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/markdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport rehypeHighlight from 'rehype-highlight';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Import highlight.js styles\r\nimport 'highlight.js/styles/github-dark.css';\r\n\r\ninterface MarkdownProps {\r\n  content: string;\r\n  className?: string;\r\n}\r\n\r\nexport function Markdown({ content, className }: MarkdownProps) {\r\n  return (\r\n    <div className={cn(\"max-w-none\", className)}>\r\n      <ReactMarkdown\r\n        remarkPlugins={[remarkGfm]}\r\n        rehypePlugins={[rehypeHighlight]}\r\n        components={{\r\n          // Custom styling for different elements\r\n          h1: ({ children }) => (\r\n            <h1 className=\"text-lg font-bold mb-2 text-foreground\">{children}</h1>\r\n          ),\r\n          h2: ({ children }) => (\r\n            <h2 className=\"text-base font-semibold mb-2 text-foreground\">{children}</h2>\r\n          ),\r\n          h3: ({ children }) => (\r\n            <h3 className=\"text-sm font-medium mb-1 text-foreground\">{children}</h3>\r\n          ),\r\n          // Sử dụng div thay vì p để tránh nesting issues với code blocks\r\n          p: ({ children }) => (\r\n            <div className=\"mb-2 text-foreground leading-relaxed\">{children}</div>\r\n          ),\r\n          ul: ({ children }) => (\r\n            <ul className=\"list-disc list-inside mb-2 space-y-1 text-foreground\">{children}</ul>\r\n          ),\r\n          ol: ({ children }) => (\r\n            <ol className=\"list-decimal list-inside mb-2 space-y-1 text-foreground\">{children}</ol>\r\n          ),\r\n          li: ({ children }) => (\r\n            <li className=\"text-foreground\">{children}</li>\r\n          ),\r\n          blockquote: ({ children }) => (\r\n            <blockquote className=\"border-l-4 border-blue-500 pl-4 italic my-2 text-muted-foreground\">\r\n              {children}\r\n            </blockquote>\r\n          ),\r\n          code: ({ className, children, ...props }: any) => {\r\n            const match = /language-(\\w+)/.exec(className || '');\r\n            const inline = props.inline;\r\n            return !inline ? (\r\n              <div className=\"relative my-3\">\r\n                <pre className=\"bg-muted rounded-md p-3 overflow-x-auto text-sm\">\r\n                  <code className={className} {...props}>\r\n                    {children}\r\n                  </code>\r\n                </pre>\r\n                {match && (\r\n                  <span className=\"absolute top-2 right-2 text-xs text-muted-foreground bg-background px-2 py-1 rounded\">\r\n                    {match[1]}\r\n                  </span>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <code className=\"bg-muted px-1 py-0.5 rounded text-sm font-mono\" {...props}>\r\n                {children}\r\n              </code>\r\n            );\r\n          },\r\n          table: ({ children }) => (\r\n            <div className=\"overflow-x-auto my-2\">\r\n              <table className=\"min-w-full border-collapse border border-border\">\r\n                {children}\r\n              </table>\r\n            </div>\r\n          ),\r\n          th: ({ children }) => (\r\n            <th className=\"border border-border bg-muted px-3 py-2 text-left font-medium\">\r\n              {children}\r\n            </th>\r\n          ),\r\n          td: ({ children }) => (\r\n            <td className=\"border border-border px-3 py-2\">{children}</td>\r\n          ),\r\n          a: ({ children, href }) => (\r\n            <a \r\n              href={href} \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-blue-600 hover:text-blue-800 underline\"\r\n            >\r\n              {children}\r\n            </a>\r\n          ),\r\n          strong: ({ children }) => (\r\n            <strong className=\"font-semibold text-foreground\">{children}</strong>\r\n          ),\r\n          em: ({ children }) => (\r\n            <em className=\"italic text-foreground\">{children}</em>\r\n          ),\r\n          hr: () => (\r\n            <hr className=\"my-4 border-border\" />\r\n          ),\r\n        }}\r\n      >\r\n        {content}\r\n      </ReactMarkdown>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAiB;IAC5D,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,6LAAC,2LAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,gJAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,sJAAA,CAAA,UAAe;aAAC;YAChC,YAAY;gBACV,wCAAwC;gBACxC,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAA0C;;;;;;gBAE1D,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAgD;;;;;;gBAEhE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAA4C;;;;;;gBAE5D,gEAAgE;gBAChE,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,6LAAC;wBAAI,WAAU;kCAAwC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAwD;;;;;;gBAExE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAA2D;;;;;;gBAE3E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;gBAEnC,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,6LAAC;wBAAW,WAAU;kCACnB;;;;;;gBAGL,MAAM,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;oBAC3C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;oBACjD,MAAM,SAAS,MAAM,MAAM;oBAC3B,OAAO,CAAC,uBACN,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAW;oCAAY,GAAG,KAAK;8CAClC;;;;;;;;;;;4BAGJ,uBACC,6LAAC;gCAAK,WAAU;0CACb,KAAK,CAAC,EAAE;;;;;;;;;;;+CAKf,6LAAC;wBAAK,WAAU;wBAAkD,GAAG,KAAK;kCACvE;;;;;;gBAGP;gBACA,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAAkC;;;;;;gBAElD,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,6LAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;kCAET;;;;;;gBAGL,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,6LAAC;wBAAO,WAAU;kCAAiC;;;;;;gBAErD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;gBAE1C,IAAI,kBACF,6LAAC;wBAAG,WAAU;;;;;;YAElB;sBAEC;;;;;;;;;;;AAIT;KAjGgB", "debugId": null}}, {"offset": {"line": 2703, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-assistant.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { AIService } from '@/lib/services/ai-service';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Lightbulb, Loader2, MessageCircle, Brain } from 'lucide-react';\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogFooter\r\n} from '@/components/ui/dialog';\r\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Markdown } from '@/components/ui/markdown';\r\n\r\nexport function AIAssistant() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [summary, setSummary] = useState<string | null>(null);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('summary');\r\n\r\n  const generateSummary = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const tasks = await TaskService.getTasks();\r\n      const summaryText = await AIService.summarizeTasks(tasks);\r\n      setSummary(summaryText);\r\n    } catch (error) {\r\n      console.error(\"Error generating summary:\", error);\r\n      setSummary(\"Đã xảy ra lỗi khi tạo báo cáo. Vui lòng thử lại sau.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50\">\r\n      <Button\r\n        onClick={() => setIsOpen(true)}\r\n        size=\"icon\"\r\n        className=\"h-12 w-12 rounded-full shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\r\n      >\r\n        <Brain className=\"h-6 w-6\" />\r\n      </Button>\r\n\r\n      <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n        <DialogContent className=\"sm:max-w-[480px] p-0 overflow-hidden\">\r\n          <DialogHeader className=\"px-6 pt-6 pb-2\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <Brain className=\"h-5 w-5\" />\r\n              Dr.AITime\r\n            </DialogTitle>\r\n            <DialogDescription>Trợ lý AI thông minh cho quản lý thời gian</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"px-6\">\r\n            <TabsList className=\"grid w-full grid-cols-2\">\r\n              <TabsTrigger value=\"summary\" className=\"flex items-center gap-2\">\r\n                <Lightbulb className=\"h-4 w-4\" />\r\n                Phân tích\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"chat\" className=\"flex items-center gap-2\">\r\n                <MessageCircle className=\"h-4 w-4\" />\r\n                Chat AI\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"summary\" className=\"py-4 text-sm\">\r\n              {isLoading ? (\r\n                <div className=\"flex justify-center py-6\">\r\n                  <Loader2 className=\"h-6 w-6 animate-spin text-muted-foreground\" />\r\n                </div>\r\n              ) : summary ? (\r\n                <div className=\"space-y-2\">\r\n                  <Markdown content={summary} className=\"text-foreground\" />\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-muted-foreground\">\r\n                  AI có thể phân tích công việc của bạn và đưa ra đề xuất thông minh.\r\n                </p>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"chat\" className=\"py-4\">\r\n              <div className=\"text-center text-muted-foreground\">\r\n                <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                <p className=\"text-sm\">Tính năng chat AI sẽ có sẵn trong chatbox riêng biệt.</p>\r\n                <p className=\"text-xs mt-1\">Nhấn vào biểu tượng chat ở góc trái màn hình.</p>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n\r\n          <DialogFooter className=\"flex justify-between border-t p-4\">\r\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setIsOpen(false)}>\r\n              Đóng\r\n            </Button>\r\n            {activeTab === 'summary' && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={generateSummary}\r\n                disabled={isLoading}\r\n                className=\"gap-1\"\r\n              >\r\n                {isLoading && <Loader2 className=\"h-3 w-3 animate-spin\" />}\r\n                Phân tích ngay\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAQA;AACA;;;AAjBA;;;;;;;;;AAmBO,SAAS;;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,QAAQ,MAAM,4IAAA,CAAA,cAAW,CAAC,QAAQ;YACxC,MAAM,cAAc,MAAM,0IAAA,CAAA,YAAS,CAAC,cAAc,CAAC;YACnD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,6LAAC,qIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,6LAAC,qIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,6LAAC,qIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAGrB,6LAAC,mIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,6LAAC,mIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;;8DACrC,6LAAC,+MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGnC,6LAAC,mIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAO,WAAU;;8DAClC,6LAAC,2NAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAKzC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,0BACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;+CAEnB,wBACF,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAS,WAAU;;;;;;;;;;6DAGxC,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAMzC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAE,WAAU;0DAAU;;;;;;0DACvB,6LAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC,qIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,6LAAC,qIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM,UAAU;8CAAQ;;;;;;gCAGpE,cAAc,2BACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;wCAET,2BAAa,6LAAC,oNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E;GAjGgB;KAAA", "debugId": null}}, {"offset": {"line": 3015, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,SAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3118, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,OAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,6LAAC,6KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,6LAAC,6KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,6LAAC;;;;;0BACD,6LAAC,6KAAA,CAAA,SAA0B;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,6KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,6LAAC,6KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,6LAAC,6KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;MAjB7C;AAoBN,UAAU,WAAW,GAAG,6KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3195, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-chatbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { AIService, ChatMessage } from '@/lib/services/ai-service';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { MessageCircle, Send, Loader2, X, Minimize2, Maximize2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { Markdown } from '@/components/ui/markdown';\r\nimport { safeLocalStorageGet, safeLocalStorageSet, safeLocalStorageRemove, cleanupLocalStorage } from '@/lib/utils/json-utils';\r\n\r\ninterface AIChatboxProps {\r\n  className?: string;\r\n}\r\n\r\nconst CHAT_HISTORY_KEY = 'dr-aitime-chat-history';\r\nconst CHAT_SETTINGS_KEY = 'dr-aitime-chat-settings';\r\n\r\nexport function AIChatbox({ className }: AIChatboxProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\r\n  const [inputMessage, setInputMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [streamingMessage, setStreamingMessage] = useState('');\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Load chat history from localStorage\r\n  const loadChatHistory = (): ChatMessage[] => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_HISTORY_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        const parsed = JSON.parse(saved);\r\n        // Convert timestamp strings back to Date objects\r\n        return parsed.map((msg: any) => ({\r\n          ...msg,\r\n          timestamp: new Date(msg.timestamp)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat history:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    }\r\n    return [];\r\n  };\r\n\r\n  // Save chat history to localStorage\r\n  const saveChatHistory = (newMessages: ChatMessage[]) => {\r\n    try {\r\n      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(newMessages));\r\n    } catch (error) {\r\n      console.error('Error saving chat history:', error);\r\n    }\r\n  };\r\n\r\n  // Load settings from localStorage\r\n  const loadChatSettings = () => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_SETTINGS_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        return JSON.parse(saved);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat settings:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_SETTINGS_KEY);\r\n    }\r\n    return { isOpen: false, isMinimized: false };\r\n  };\r\n\r\n  // Initialize from localStorage\r\n  useEffect(() => {\r\n    // Clean up any invalid localStorage items first\r\n    cleanupLocalStorage();\r\n\r\n    const savedMessages = loadChatHistory();\r\n    const savedSettings = loadChatSettings();\r\n\r\n    setMessages(savedMessages);\r\n    setIsOpen(savedSettings.isOpen || false);\r\n    setIsMinimized(savedSettings.isMinimized || false);\r\n  }, []);\r\n\r\n  // Save settings whenever they change\r\n  useEffect(() => {\r\n    const settings = { isOpen, isMinimized };\r\n    try {\r\n      localStorage.setItem(CHAT_SETTINGS_KEY, JSON.stringify(settings));\r\n    } catch (error) {\r\n      console.error('Error saving chat settings:', error);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  // Save messages whenever they change\r\n  useEffect(() => {\r\n    if (messages.length > 0) {\r\n      saveChatHistory(messages);\r\n    }\r\n  }, [messages]);\r\n\r\n  // Auto scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [messages, streamingMessage]);\r\n\r\n  // Focus input when chat opens\r\n  useEffect(() => {\r\n    if (isOpen && !isMinimized) {\r\n      setTimeout(() => inputRef.current?.focus(), 100);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputMessage.trim() || isLoading) return;\r\n\r\n    const userMessage: ChatMessage = {\r\n      role: 'user',\r\n      content: inputMessage.trim(),\r\n      timestamp: new Date()\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputMessage('');\r\n    setIsLoading(true);\r\n    setStreamingMessage('');\r\n\r\n    try {\r\n      // Use streaming chat\r\n      const stream = AIService.chatStream(userMessage.content, messages);\r\n      let fullResponse = '';\r\n\r\n      for await (const chunk of stream) {\r\n        fullResponse += chunk;\r\n        setStreamingMessage(fullResponse);\r\n      }\r\n\r\n      // Add complete response to messages\r\n      const assistantMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: fullResponse,\r\n        timestamp: new Date()\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n      setStreamingMessage('');\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n      const errorMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',\r\n        timestamp: new Date()\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const clearChat = () => {\r\n    setMessages([]);\r\n    setStreamingMessage('');\r\n    // Clear from localStorage\r\n    try {\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    } catch (error) {\r\n      console.error('Error clearing chat history:', error);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n        <Button\r\n          onClick={() => setIsOpen(true)}\r\n          size=\"icon\"\r\n          className=\"h-12 w-12 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700\"\r\n        >\r\n          <MessageCircle className=\"h-6 w-6\" />\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n      <Card className={cn(\r\n        \"w-80 shadow-xl transition-all duration-200 relative overflow-hidden\",\r\n        isMinimized ? \"h-12\" : \"h-96\"\r\n      )}>\r\n        <CardHeader className=\"flex flex-row items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg\">\r\n          <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\r\n            <MessageCircle className=\"h-4 w-4\" />\r\n            Dr.AITime Chat\r\n            {messages.length > 0 && (\r\n              <span className=\"bg-blue-500 text-xs px-2 py-1 rounded-full\">\r\n                {messages.length}\r\n              </span>\r\n            )}\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsMinimized(!isMinimized)}\r\n            >\r\n              {isMinimized ? <Maximize2 className=\"h-3 w-3\" /> : <Minimize2 className=\"h-3 w-3\" />}\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsOpen(false)}\r\n            >\r\n              <X className=\"h-3 w-3\" />\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        {!isMinimized && (\r\n          <CardContent className=\"p-0 flex flex-col h-80 relative\">\r\n            {/* Backdrop blur overlay chỉ trong khung chat */}\r\n            <div className=\"absolute inset-0 bg-background/70 backdrop-blur-sm z-10 pointer-events-none\" />\r\n            {/* Messages Area */}\r\n            <ScrollArea className=\"flex-1 p-3 relative z-20\">\r\n              <div className=\"space-y-3\">\r\n                {messages.length === 0 && (\r\n                  <div className=\"text-center text-muted-foreground text-sm py-8\">\r\n                    <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                    <p className=\"font-medium\">Xin chào! Tôi là Dr.AITime 👋</p>\r\n                    <p className=\"mt-1\">Hãy hỏi tôi về quản lý thời gian nhé!</p>\r\n                    <div className=\"mt-3 text-xs opacity-70\">\r\n                      <p>💡 Lịch sử chat sẽ được lưu tự động</p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {messages.map((message, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={cn(\r\n                      \"flex\",\r\n                      message.role === 'user' ? \"justify-end\" : \"justify-start\"\r\n                    )}\r\n                  >\r\n                    <div\r\n                      className={cn(\r\n                        \"max-w-[80%] rounded-lg px-3 py-2 text-sm\",\r\n                        message.role === 'user'\r\n                          ? \"bg-blue-600 text-white\"\r\n                          : \"bg-muted\"\r\n                      )}\r\n                    >\r\n                      {message.role === 'user' ? (\r\n                        <p className=\"whitespace-pre-wrap text-white\">{message.content}</p>\r\n                      ) : (\r\n                        <Markdown content={message.content} className=\"text-foreground\" />\r\n                      )}\r\n                      <p className={cn(\r\n                        \"text-xs opacity-70 mt-1\",\r\n                        message.role === 'user' ? \"text-white\" : \"text-muted-foreground\"\r\n                      )}>\r\n                        {message.timestamp.toLocaleTimeString('vi-VN', {\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n\r\n                {/* Streaming message */}\r\n                {streamingMessage && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"max-w-[80%] rounded-lg px-3 py-2 text-sm bg-muted\">\r\n                      <Markdown content={streamingMessage} className=\"text-foreground\" />\r\n                      <div className=\"flex items-center gap-1 mt-1\">\r\n                        <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                        <span className=\"text-xs opacity-70 text-muted-foreground\">Đang trả lời...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div ref={messagesEndRef} />\r\n              </div>\r\n            </ScrollArea>\r\n\r\n            {/* Input Area */}\r\n            <div className=\"border-t p-3 relative z-20 bg-background\">\r\n              <div className=\"flex gap-2\">\r\n                <Input\r\n                  ref={inputRef}\r\n                  value={inputMessage}\r\n                  onChange={(e) => setInputMessage(e.target.value)}\r\n                  onKeyDown={handleKeyPress}\r\n                  placeholder=\"Nhập tin nhắn...\"\r\n                  disabled={isLoading}\r\n                  className=\"flex-1\"\r\n                />\r\n                <Button\r\n                  onClick={handleSendMessage}\r\n                  disabled={isLoading || !inputMessage.trim()}\r\n                  size=\"icon\"\r\n                  className=\"shrink-0\"\r\n                >\r\n                  {isLoading ? (\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                  ) : (\r\n                    <Send className=\"h-4 w-4\" />\r\n                  )}\r\n                </Button>\r\n              </div>\r\n              \r\n              {messages.length > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    if (window.confirm('Bạn có chắc chắn muốn xóa toàn bộ lịch sử chat? Hành động này không thể hoàn tác.')) {\r\n                      clearChat();\r\n                    }\r\n                  }}\r\n                  className=\"w-full mt-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  🗑️ Xóa lịch sử chat ({messages.length} tin nhắn)\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </CardContent>\r\n        )}\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;AAkBA,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAEnB,SAAS,UAAU,EAAE,SAAS,EAAkB;;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sCAAsC;IACtC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,iDAAiD;gBACjD,OAAO,OAAO,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC/B,GAAG,GAAG;wBACN,WAAW,IAAI,KAAK,IAAI,SAAS;oBACnC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO,EAAE;IACX;IAEA,oCAAoC;IACpC,MAAM,kBAAkB,CAAC;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,OAAO,KAAK,KAAK,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO;YAAE,QAAQ;YAAO,aAAa;QAAM;IAC7C;IAEA,+BAA+B;IAC/B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,gDAAgD;YAChD,CAAA,GAAA,uIAAA,CAAA,sBAAmB,AAAD;YAElB,MAAM,gBAAgB;YACtB,MAAM,gBAAgB;YAEtB,YAAY;YACZ,UAAU,cAAc,MAAM,IAAI;YAClC,eAAe,cAAc,WAAW,IAAI;QAC9C;8BAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,MAAM,WAAW;gBAAE;gBAAQ;YAAY;YACvC,IAAI;gBACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;YACzD,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,+BAA+B;YAC/C;QACF;8BAAG;QAAC;QAAQ;KAAY;IAExB,qCAAqC;IACrC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,SAAS,MAAM,GAAG,GAAG;gBACvB,gBAAgB;YAClB;QACF;8BAAG;QAAC;KAAS;IAEb,iDAAiD;IACjD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,eAAe,OAAO,EAAE,eAAe;gBAAE,UAAU;YAAS;QAC9D;8BAAG;QAAC;QAAU;KAAiB;IAE/B,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR,IAAI,UAAU,CAAC,aAAa;gBAC1B;2CAAW,IAAM,SAAS,OAAO,EAAE;0CAAS;YAC9C;QACF;8BAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QACb,oBAAoB;QAEpB,IAAI;YACF,qBAAqB;YACrB,MAAM,SAAS,0IAAA,CAAA,YAAS,CAAC,UAAU,CAAC,YAAY,OAAO,EAAE;YACzD,IAAI,eAAe;YAEnB,WAAW,MAAM,SAAS,OAAQ;gBAChC,gBAAgB;gBAChB,oBAAoB;YACtB;YAEA,oCAAoC;YACpC,MAAM,mBAAgC;gBACpC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAA4B;gBAChC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY,EAAE;QACd,oBAAoB;QACpB,0BAA0B;QAC1B,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBAC/C,cAAA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,6LAAC,2NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC/C,cAAA,6LAAC,mIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAChB,uEACA,cAAc,SAAS;;8BAEvB,6LAAC,mIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,2NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;gCAEpC,SAAS,MAAM,GAAG,mBACjB,6LAAC;oCAAK,WAAU;8CACb,SAAS,MAAM;;;;;;;;;;;;sCAItB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe,CAAC;8CAE9B,4BAAc,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAAe,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAEzB,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKlB,CAAC,6BACA,6LAAC,mIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC,6IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,6LAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,mBACnB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,2NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,6LAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,6LAAC;gDAAE,WAAU;0DAAO;;;;;;0DACpB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;8DAAE;;;;;;;;;;;;;;;;;oCAKR,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4CAEC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;sDAG5C,cAAA,6LAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,4CACA,QAAQ,IAAI,KAAK,SACb,2BACA;;oDAGL,QAAQ,IAAI,KAAK,uBAChB,6LAAC;wDAAE,WAAU;kEAAkC,QAAQ,OAAO;;;;;6EAE9D,6LAAC,uIAAA,CAAA,WAAQ;wDAAC,SAAS,QAAQ,OAAO;wDAAE,WAAU;;;;;;kEAEhD,6LAAC;wDAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACb,2BACA,QAAQ,IAAI,KAAK,SAAS,eAAe;kEAExC,QAAQ,SAAS,CAAC,kBAAkB,CAAC,SAAS;4DAC7C,MAAM;4DACN,QAAQ;wDACV;;;;;;;;;;;;2CA1BC;;;;;oCAiCR,kCACC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uIAAA,CAAA,WAAQ;oDAAC,SAAS;oDAAkB,WAAU;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,oNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,6LAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;kDAMnE,6LAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAKd,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,oIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAW;4CACX,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;sDAEZ,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC,aAAa,IAAI;4CACzC,MAAK;4CACL,WAAU;sDAET,0BACC,6LAAC,oNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,6LAAC,qMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAKrB,SAAS,MAAM,GAAG,mBACjB,6LAAC,qIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,IAAI,OAAO,OAAO,CAAC,sFAAsF;4CACvG;wCACF;oCACF;oCACA,WAAU;;wCACX;wCACwB,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD;GArUgB;KAAA", "debugId": null}}, {"offset": {"line": 3796, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React, { useState } from \"react\";\nimport { Sidebar, SidebarBody, SidebarLink } from \"@/components/ui/sidebar\";\nimport { Home, List, Calendar, BarChart2, Folder, Tag } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ClearDataButton } from \"@/components/clear-data-button\";\nimport { Footerdemo } from \"@/components/ui/footer-section\";\nimport { Header } from \"@/components/layout/header\";\nimport { AIAssistant } from '@/components/ai/ai-assistant';\nimport { AIChatbox } from '@/components/ai/ai-chatbox';\nimport { cn } from \"@/lib/utils\";\n\nconst links = [\n  {\n    label: \"Trang chủ\",\n    href: \"/\",\n    icon: <Home className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n  {\n    label: \"Công việc\",\n    href: \"/tasks\",\n    icon: <List className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n  {\n    label: \"Thống kê\",\n    href: \"/statistics\",\n    icon: <BarChart2 className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n  {\n    label: \"Lịch\",\n    href: \"/calendar\",\n    icon: <Calendar className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n  {\n    label: \"Dự án\",\n    href: \"/projects\",\n    icon: <Folder className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n  {\n    label: \"Danh mục\",\n    href: \"/categories\",\n    icon: <Tag className=\"text-neutral-700 dark:text-neutral-200 h-5 w-5 flex-shrink-0\" />,\n  },\n];\n\nexport function Layout({ children }: { children: React.ReactNode }) {\n  const [open, setOpen] = useState(false);\n\n  return (\n    <div className={cn(\n      \"flex flex-col md:flex-row bg-gray-100 dark:bg-neutral-800 w-full flex-1 border border-neutral-200 dark:border-neutral-700 overflow-hidden\",\n      \"h-screen\"\n    )}>\n      <Sidebar open={open} setOpen={setOpen}>\n        <SidebarBody className=\"justify-between gap-10\">\n          <div className=\"flex flex-col flex-1 overflow-y-auto overflow-x-hidden\">\n            {open ? <Logo /> : <LogoIcon />}\n            <div className=\"mt-8 flex flex-col gap-2\">\n              {links.map((link, idx) => (\n                <SidebarLink key={idx} link={link} />\n              ))}\n            </div>\n          </div>\n          <div className=\"flex flex-col gap-2\">\n            <ClearDataButton />\n          </div>\n        </SidebarBody>\n      </Sidebar>\n\n      <div className=\"flex flex-1\">\n        <div className=\"flex flex-col flex-1 bg-white dark:bg-neutral-900\">\n          <Header />\n          <main className=\"flex-grow px-4 py-6 md:px-8 lg:px-12 overflow-y-auto\">\n            {children}\n          </main>\n          <Footerdemo />\n        </div>\n      </div>\n\n      <AIAssistant />\n      <AIChatbox />\n    </div>\n  );\n}\n\nexport const Logo = () => {\n  return (\n    <Link\n      href=\"/\"\n      className=\"font-normal flex space-x-2 items-center text-sm text-black dark:text-white py-1 relative z-20\"\n    >\n      <div className=\"h-5 w-6 bg-primary rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0\" />\n      <motion.span\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        className=\"font-medium text-black dark:text-white whitespace-pre text-xl\"\n      >\n        QLTime\n      </motion.span>\n    </Link>\n  );\n};\n\nexport const LogoIcon = () => {\n  return (\n    <Link\n      href=\"/\"\n      className=\"font-normal flex space-x-2 items-center text-sm text-black dark:text-white py-1 relative z-20\"\n    >\n      <div className=\"h-5 w-6 bg-primary rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0\" />\n    </Link>\n  );\n};"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAZA;;;;;;;;;;;;AAcA,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,sMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,qMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,mOAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC7B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,6MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,yMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC1B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,6LAAC,mMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IACvB;CACD;AAEM,SAAS,OAAO,EAAE,QAAQ,EAAiC;;IAChE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEjC,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,6IACA;;0BAEA,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAM;gBAAM,SAAS;0BAC5B,cAAA,6LAAC,sIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6LAAC;4BAAI,WAAU;;gCACZ,qBAAO,6LAAC;;;;yDAAU,6LAAC;;;;;8CACpB,6LAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,MAAM,oBAChB,6LAAC,sIAAA,CAAA,cAAW;4CAAW,MAAM;2CAAX;;;;;;;;;;;;;;;;sCAIxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,gJAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;0BAKtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yIAAA,CAAA,SAAM;;;;;sCACP,6LAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,6LAAC,gJAAA,CAAA,aAAU;;;;;;;;;;;;;;;;0BAIf,6LAAC,8IAAA,CAAA,cAAW;;;;;0BACZ,6LAAC,4IAAA,CAAA,YAAS;;;;;;;;;;;AAGhB;GAtCgB;KAAA;AAwCT,MAAM,OAAO;IAClB,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAU;;0BAEV,6LAAC;gBAAI,WAAU;;;;;;0BACf,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,WAAU;0BACX;;;;;;;;;;;;AAKP;MAhBa;AAkBN,MAAM,WAAW;IACtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAU;kBAEV,cAAA,6LAAC;YAAI,WAAU;;;;;;;;;;;AAGrB;MATa", "debugId": null}}, {"offset": {"line": 4085, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/statistics-service.ts"], "sourcesContent": ["import { ApiService } from './api-service';\n\nexport interface TaskStatistics {\n  total: number;\n  completed: number;\n  pending: number;\n  overdue: number;\n  byPriority: {\n    low: number;\n    medium: number;\n    high: number;\n  };\n  byStatus: {\n    backlog: number;\n    todo: number;\n    doing: number;\n    done: number;\n  };\n  byCategory: Record<string, number>;\n}\n\nexport interface TimeBlockStatistics {\n  totalHours: number;\n  completedHours: number;\n  completionRate: number;\n  byDay: Record<string, {\n    total: number;\n    completed: number;\n  }>;\n}\n\nexport interface ProductivityStatistics {\n  productivityScore: number;\n  taskCompletionRate: number;\n  timeBlockCompletionRate: number;\n  dailyScores: Record<string, number>;\n}\n\nexport interface StatisticsQuery {\n  startDate?: string;\n  endDate?: string;\n}\n\nexport const StatisticsService = {\n  // Lấy thống kê công việc\n  getTaskStatistics: async (query?: StatisticsQuery): Promise<TaskStatistics> => {\n    try {\n      console.log('StatisticsService: Đang tải thống kê công việc...', query);\n      const data = await ApiService.statistics.getTaskStats(query?.startDate, query?.endDate);\n      console.log('StatisticsService: Đã tải thống kê công việc:', data);\n      return data;\n    } catch (error) {\n      console.error('StatisticsService: Lỗi khi tải thống kê công việc:', error);\n      throw error;\n    }\n  },\n\n  // Lấy thống kê khối thời gian\n  getTimeBlockStatistics: async (query?: StatisticsQuery): Promise<TimeBlockStatistics> => {\n    try {\n      console.log('StatisticsService: Đang tải thống kê khối thời gian...', query);\n      const data = await ApiService.statistics.getTimeBlockStats(query?.startDate, query?.endDate);\n      console.log('StatisticsService: Đã tải thống kê khối thời gian:', data);\n      return data;\n    } catch (error) {\n      console.error('StatisticsService: Lỗi khi tải thống kê khối thời gian:', error);\n      throw error;\n    }\n  },\n\n  // Lấy thống kê năng suất\n  getProductivityStatistics: async (query?: StatisticsQuery): Promise<ProductivityStatistics> => {\n    try {\n      console.log('StatisticsService: Đang tải thống kê năng suất...', query);\n      const data = await ApiService.statistics.getProductivityStats(query?.startDate, query?.endDate);\n      console.log('StatisticsService: Đã tải thống kê năng suất:', data);\n      return data;\n    } catch (error) {\n      console.error('StatisticsService: Lỗi khi tải thống kê năng suất:', error);\n      throw error;\n    }\n  },\n\n  // Lấy tất cả thống kê\n  getAllStatistics: async (query?: StatisticsQuery) => {\n    try {\n      console.log('StatisticsService: Đang tải tất cả thống kê...', query);\n      \n      const [taskStats, timeBlockStats, productivityStats] = await Promise.all([\n        StatisticsService.getTaskStatistics(query),\n        StatisticsService.getTimeBlockStatistics(query),\n        StatisticsService.getProductivityStatistics(query),\n      ]);\n\n      console.log('StatisticsService: Đã tải tất cả thống kê');\n      \n      return {\n        tasks: taskStats,\n        timeBlocks: timeBlockStats,\n        productivity: productivityStats,\n      };\n    } catch (error) {\n      console.error('StatisticsService: Lỗi khi tải tất cả thống kê:', error);\n      throw error;\n    }\n  },\n\n  // Tính toán thống kê cục bộ từ dữ liệu tasks (fallback)\n  calculateLocalTaskStatistics: (tasks: any[]): TaskStatistics => {\n    const total = tasks.length;\n    const completed = tasks.filter(task => task.completed).length;\n    const pending = total - completed;\n    const overdue = tasks.filter(task => {\n      if (!task.dueDate || task.completed) return false;\n      return new Date(task.dueDate) < new Date();\n    }).length;\n\n    const byPriority = {\n      low: tasks.filter(task => task.priority === 'low').length,\n      medium: tasks.filter(task => task.priority === 'medium').length,\n      high: tasks.filter(task => task.priority === 'high').length,\n    };\n\n    const byStatus = {\n      backlog: tasks.filter(task => task.status === 'backlog').length,\n      todo: tasks.filter(task => task.status === 'todo').length,\n      doing: tasks.filter(task => task.status === 'doing').length,\n      done: tasks.filter(task => task.status === 'done').length,\n    };\n\n    const byCategory: Record<string, number> = {};\n    tasks.forEach(task => {\n      const category = task.category || 'Không có danh mục';\n      byCategory[category] = (byCategory[category] || 0) + 1;\n    });\n\n    return {\n      total,\n      completed,\n      pending,\n      overdue,\n      byPriority,\n      byStatus,\n      byCategory,\n    };\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AA2CO,MAAM,oBAAoB;IAC/B,yBAAyB;IACzB,mBAAmB,OAAO;QACxB,IAAI;YACF,QAAQ,GAAG,CAAC,qDAAqD;YACjE,MAAM,OAAO,MAAM,2IAAA,CAAA,aAAU,CAAC,UAAU,CAAC,YAAY,CAAC,OAAO,WAAW,OAAO;YAC/E,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,wBAAwB,OAAO;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,0DAA0D;YACtE,MAAM,OAAO,MAAM,2IAAA,CAAA,aAAU,CAAC,UAAU,CAAC,iBAAiB,CAAC,OAAO,WAAW,OAAO;YACpF,QAAQ,GAAG,CAAC,sDAAsD;YAClE,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2DAA2D;YACzE,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,2BAA2B,OAAO;QAChC,IAAI;YACF,QAAQ,GAAG,CAAC,qDAAqD;YACjE,MAAM,OAAO,MAAM,2IAAA,CAAA,aAAU,CAAC,UAAU,CAAC,oBAAoB,CAAC,OAAO,WAAW,OAAO;YACvF,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sDAAsD;YACpE,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,kDAAkD;YAE9D,MAAM,CAAC,WAAW,gBAAgB,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACvE,kBAAkB,iBAAiB,CAAC;gBACpC,kBAAkB,sBAAsB,CAAC;gBACzC,kBAAkB,yBAAyB,CAAC;aAC7C;YAED,QAAQ,GAAG,CAAC;YAEZ,OAAO;gBACL,OAAO;gBACP,YAAY;gBACZ,cAAc;YAChB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mDAAmD;YACjE,MAAM;QACR;IACF;IAEA,wDAAwD;IACxD,8BAA8B,CAAC;QAC7B,MAAM,QAAQ,MAAM,MAAM;QAC1B,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;QAC7D,MAAM,UAAU,QAAQ;QACxB,MAAM,UAAU,MAAM,MAAM,CAAC,CAAA;YAC3B,IAAI,CAAC,KAAK,OAAO,IAAI,KAAK,SAAS,EAAE,OAAO;YAC5C,OAAO,IAAI,KAAK,KAAK,OAAO,IAAI,IAAI;QACtC,GAAG,MAAM;QAET,MAAM,aAAa;YACjB,KAAK,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,OAAO,MAAM;YACzD,QAAQ,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,MAAM;YAC/D,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,QAAQ,MAAM;QAC7D;QAEA,MAAM,WAAW;YACf,SAAS,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,WAAW,MAAM;YAC/D,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;YACzD,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,SAAS,MAAM;YAC3D,MAAM,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK,QAAQ,MAAM;QAC3D;QAEA,MAAM,aAAqC,CAAC;QAC5C,MAAM,OAAO,CAAC,CAAA;YACZ,MAAM,WAAW,KAAK,QAAQ,IAAI;YAClC,UAAU,CAAC,SAAS,GAAG,CAAC,UAAU,CAAC,SAAS,IAAI,CAAC,IAAI;QACvD;QAEA,OAAO;YACL;YACA;YACA;YACA;YACA;YACA;YACA;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 4192, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/statistics/statistics-card.tsx"], "sourcesContent": ["'use client';\n\nimport { Card, CardContent, CardDescription, CardH<PERSON>er, CardTitle } from '@/components/ui/card';\nimport { CheckCircle, Circle, AlertCircle, ArrowUpCircle, ArrowDownCircle, Clock, ListTodo } from 'lucide-react';\n\ntype StatisticsCardProps = {\n  title: string;\n  value: number;\n  description: string;\n  icon: 'task' | 'check' | 'pending' | 'priority';\n  trend?: 'up' | 'down' | 'warning' | 'neutral';\n};\n\nexport function StatisticsCard({ title, value, description, icon, trend }: StatisticsCardProps) {\n  const renderIcon = () => {\n    switch (icon) {\n      case 'task':\n        return <ListTodo className=\"h-5 w-5 text-muted-foreground\" />;\n      case 'check':\n        return <CheckCircle className=\"h-5 w-5 text-green-500\" />;\n      case 'pending':\n        return <Clock className=\"h-5 w-5 text-blue-500\" />;\n      case 'priority':\n        return <AlertCircle className=\"h-5 w-5 text-orange-500\" />;\n      default:\n        return <Circle className=\"h-5 w-5 text-muted-foreground\" />;\n    }\n  };\n\n  const renderTrend = () => {\n    if (!trend) return null;\n    \n    switch (trend) {\n      case 'up':\n        return <ArrowUpCircle className=\"h-4 w-4 text-green-500\" />;\n      case 'down':\n        return <ArrowDownCircle className=\"h-4 w-4 text-red-500\" />;\n      case 'warning':\n        return <AlertCircle className=\"h-4 w-4 text-orange-500\" />;\n      case 'neutral':\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <Card>\n      <CardHeader className=\"flex flex-row items-center justify-between space-y-0 pb-2\">\n        <CardTitle className=\"text-sm font-medium\">{title}</CardTitle>\n        {renderIcon()}\n      </CardHeader>\n      <CardContent>\n        <div className=\"text-2xl font-bold\">{value}</div>\n        <CardDescription className=\"flex items-center gap-1 mt-1\">\n          {renderTrend()}\n          {description}\n        </CardDescription>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAaO,SAAS,eAAe,EAAE,KAAK,EAAE,KAAK,EAAE,WAAW,EAAE,IAAI,EAAE,KAAK,EAAuB;IAC5F,MAAM,aAAa;QACjB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,iNAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;YAC7B,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,CAAC,OAAO,OAAO;QAEnB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,+NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,6LAAC,mOAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;YACpC,KAAK;gBACH,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;YACL;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;;kCACpB,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;kCAAuB;;;;;;oBAC3C;;;;;;;0BAEH,6LAAC,mIAAA,CAAA,cAAW;;kCACV,6LAAC;wBAAI,WAAU;kCAAsB;;;;;;kCACrC,6LAAC,mIAAA,CAAA,kBAAe;wBAAC,WAAU;;4BACxB;4BACA;;;;;;;;;;;;;;;;;;;AAKX;KA/CgB", "debugId": null}}, {"offset": {"line": 4351, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/statistics/statistics-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ntype StatisticsChartProps = {\n  completed: number;\n  pending: number;\n};\n\nexport function StatisticsChart({ completed, pending }: StatisticsChartProps) {\n  const chartRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    const ctx = chartRef.current.getContext('2d');\n    if (!ctx) return;\n\n    // Vẽ biểu đồ tròn\n    const total = completed + pending;\n    const completedAngle = (completed / total) * 2 * Math.PI;\n    \n    // Xóa canvas\n    ctx.clearRect(0, 0, chartRef.current.width, chartRef.current.height);\n    \n    const centerX = chartRef.current.width / 2;\n    const centerY = chartRef.current.height / 2;\n    const radius = Math.min(centerX, centerY) - 10;\n    \n    // Vẽ phần công việc đang thực hiện\n    if (pending > 0) {\n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY);\n      ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);\n      ctx.fillStyle = '#e11d48';\n      ctx.fill();\n    }\n    \n    // Vẽ phần công việc đã hoàn thành\n    if (completed > 0) {\n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY);\n      ctx.arc(centerX, centerY, radius, 0, completedAngle);\n      ctx.fillStyle = '#10b981';\n      ctx.fill();\n    }\n    \n    // Vẽ lỗ trung tâm để tạo biểu đồ dạng donut\n    ctx.beginPath();\n    ctx.moveTo(centerX, centerY);\n    ctx.arc(centerX, centerY, radius * 0.6, 0, 2 * Math.PI);\n    ctx.fillStyle = 'white';\n    ctx.fill();\n    \n    // Hiển thị tỷ lệ phần trăm ở giữa\n    const percentage = total > 0 ? Math.round((completed / total) * 100) : 0;\n    ctx.font = 'bold 24px Arial';\n    ctx.fillStyle = '#000';\n    ctx.textAlign = 'center';\n    ctx.textBaseline = 'middle';\n    ctx.fillText(`${percentage}%`, centerX, centerY);\n    \n    // Vẽ chú thích\n    const legendY = centerY + radius + 30;\n    \n    // Chú thích cho công việc đã hoàn thành\n    ctx.beginPath();\n    ctx.rect(centerX - 80, legendY, 15, 15);\n    ctx.fillStyle = '#10b981';\n    ctx.fill();\n    \n    ctx.font = '14px Arial';\n    ctx.fillStyle = '#000';\n    ctx.textAlign = 'left';\n    ctx.fillText('Đã hoàn thành', centerX - 60, legendY + 7);\n    \n    // Chú thích cho công việc đang thực hiện\n    ctx.beginPath();\n    ctx.rect(centerX + 20, legendY, 15, 15);\n    ctx.fillStyle = '#e11d48';\n    ctx.fill();\n    \n    ctx.font = '14px Arial';\n    ctx.fillStyle = '#000';\n    ctx.textAlign = 'left';\n    ctx.fillText('Đang thực hiện', centerX + 40, legendY + 7);\n    \n  }, [completed, pending]);\n\n  return (\n    <div className=\"w-full h-80 flex items-center justify-center\">\n      <canvas ref={chartRef} width={300} height={300}></canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AASO,SAAS,gBAAgB,EAAE,SAAS,EAAE,OAAO,EAAwB;;IAC1E,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,IAAI,CAAC,SAAS,OAAO,EAAE;YAEvB,MAAM,MAAM,SAAS,OAAO,CAAC,UAAU,CAAC;YACxC,IAAI,CAAC,KAAK;YAEV,kBAAkB;YAClB,MAAM,QAAQ,YAAY;YAC1B,MAAM,iBAAiB,AAAC,YAAY,QAAS,IAAI,KAAK,EAAE;YAExD,aAAa;YACb,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,MAAM;YAEnE,MAAM,UAAU,SAAS,OAAO,CAAC,KAAK,GAAG;YACzC,MAAM,UAAU,SAAS,OAAO,CAAC,MAAM,GAAG;YAC1C,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS,WAAW;YAE5C,mCAAmC;YACnC,IAAI,UAAU,GAAG;gBACf,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,SAAS;gBACpB,IAAI,GAAG,CAAC,SAAS,SAAS,QAAQ,GAAG,IAAI,KAAK,EAAE;gBAChD,IAAI,SAAS,GAAG;gBAChB,IAAI,IAAI;YACV;YAEA,kCAAkC;YAClC,IAAI,YAAY,GAAG;gBACjB,IAAI,SAAS;gBACb,IAAI,MAAM,CAAC,SAAS;gBACpB,IAAI,GAAG,CAAC,SAAS,SAAS,QAAQ,GAAG;gBACrC,IAAI,SAAS,GAAG;gBAChB,IAAI,IAAI;YACV;YAEA,4CAA4C;YAC5C,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,SAAS;YACpB,IAAI,GAAG,CAAC,SAAS,SAAS,SAAS,KAAK,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;YAER,kCAAkC;YAClC,MAAM,aAAa,QAAQ,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,QAAS,OAAO;YACvE,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS,GAAG;YAChB,IAAI,YAAY,GAAG;YACnB,IAAI,QAAQ,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,SAAS;YAExC,eAAe;YACf,MAAM,UAAU,UAAU,SAAS;YAEnC,wCAAwC;YACxC,IAAI,SAAS;YACb,IAAI,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI;YACpC,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;YAER,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,iBAAiB,UAAU,IAAI,UAAU;YAEtD,yCAAyC;YACzC,IAAI,SAAS;YACb,IAAI,IAAI,CAAC,UAAU,IAAI,SAAS,IAAI;YACpC,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;YAER,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS,GAAG;YAChB,IAAI,QAAQ,CAAC,kBAAkB,UAAU,IAAI,UAAU;QAEzD;oCAAG;QAAC;QAAW;KAAQ;IAEvB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAO,KAAK;YAAU,OAAO;YAAK,QAAQ;;;;;;;;;;;AAGjD;GArFgB;KAAA", "debugId": null}}, {"offset": {"line": 4460, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/statistics/statistics-priority-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ntype StatisticsPriorityChartProps = {\n  high: number;\n  medium: number;\n  low: number;\n};\n\nexport function StatisticsPriorityChart({ high, medium, low }: StatisticsPriorityChartProps) {\n  const chartRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    const ctx = chartRef.current.getContext('2d');\n    if (!ctx) return;\n\n    // Xóa canvas\n    ctx.clearRect(0, 0, chartRef.current.width, chartRef.current.height);\n    \n    const width = chartRef.current.width;\n    const height = chartRef.current.height;\n    \n    const total = high + medium + low;\n    if (total === 0) {\n      // Hiển thị thông báo khi không có dữ liệu\n      ctx.font = 'bold 16px Arial';\n      ctx.fillStyle = '#666';\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n      ctx.fillText('Không có dữ liệu để hiển thị', width / 2, height / 2);\n      return;\n    }\n    \n    // <PERSON><PERSON>u sắc cho từng mức độ ưu tiên\n    const colors = {\n      high: '#ef4444', // Đỏ\n      medium: '#f59e0b', // Cam\n      low: '#10b981', // Xanh lá\n    };\n    \n    // Kích thước của thanh\n    const barWidth = 60;\n    const spacing = 40;\n    const startX = (width - (3 * barWidth + 2 * spacing)) / 2;\n    const maxBarHeight = height - 100;\n    \n    // Tìm giá trị lớn nhất để tỷ lệ chiều cao\n    const maxValue = Math.max(high, medium, low);\n    \n    // Vẽ thanh cho mức độ ưu tiên cao\n    const highBarHeight = maxValue > 0 ? (high / maxValue) * maxBarHeight : 0;\n    ctx.fillStyle = colors.high;\n    ctx.fillRect(startX, height - 60 - highBarHeight, barWidth, highBarHeight);\n    \n    // Vẽ thanh cho mức độ ưu tiên trung bình\n    const mediumBarHeight = maxValue > 0 ? (medium / maxValue) * maxBarHeight : 0;\n    ctx.fillStyle = colors.medium;\n    ctx.fillRect(startX + barWidth + spacing, height - 60 - mediumBarHeight, barWidth, mediumBarHeight);\n    \n    // Vẽ thanh cho mức độ ưu tiên thấp\n    const lowBarHeight = maxValue > 0 ? (low / maxValue) * maxBarHeight : 0;\n    ctx.fillStyle = colors.low;\n    ctx.fillRect(startX + 2 * (barWidth + spacing), height - 60 - lowBarHeight, barWidth, lowBarHeight);\n    \n    // Vẽ nhãn và giá trị\n    ctx.font = 'bold 14px Arial';\n    ctx.fillStyle = '#000';\n    ctx.textAlign = 'center';\n    \n    // Nhãn và giá trị cho mức độ ưu tiên cao\n    ctx.fillText(high.toString(), startX + barWidth / 2, height - 70 - highBarHeight);\n    ctx.fillText('Cao', startX + barWidth / 2, height - 30);\n    \n    // Nhãn và giá trị cho mức độ ưu tiên trung bình\n    ctx.fillText(medium.toString(), startX + barWidth + spacing + barWidth / 2, height - 70 - mediumBarHeight);\n    ctx.fillText('Trung bình', startX + barWidth + spacing + barWidth / 2, height - 30);\n    \n    // Nhãn và giá trị cho mức độ ưu tiên thấp\n    ctx.fillText(low.toString(), startX + 2 * (barWidth + spacing) + barWidth / 2, height - 70 - lowBarHeight);\n    ctx.fillText('Thấp', startX + 2 * (barWidth + spacing) + barWidth / 2, height - 30);\n    \n  }, [high, medium, low]);\n\n  return (\n    <div className=\"w-full h-80 flex items-center justify-center\">\n      <canvas ref={chartRef} width={400} height={300}></canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAUO,SAAS,wBAAwB,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,EAAgC;;IACzF,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,SAAS,OAAO,EAAE;YAEvB,MAAM,MAAM,SAAS,OAAO,CAAC,UAAU,CAAC;YACxC,IAAI,CAAC,KAAK;YAEV,aAAa;YACb,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,MAAM;YAEnE,MAAM,QAAQ,SAAS,OAAO,CAAC,KAAK;YACpC,MAAM,SAAS,SAAS,OAAO,CAAC,MAAM;YAEtC,MAAM,QAAQ,OAAO,SAAS;YAC9B,IAAI,UAAU,GAAG;gBACf,0CAA0C;gBAC1C,IAAI,IAAI,GAAG;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS,GAAG;gBAChB,IAAI,YAAY,GAAG;gBACnB,IAAI,QAAQ,CAAC,gCAAgC,QAAQ,GAAG,SAAS;gBACjE;YACF;YAEA,kCAAkC;YAClC,MAAM,SAAS;gBACb,MAAM;gBACN,QAAQ;gBACR,KAAK;YACP;YAEA,uBAAuB;YACvB,MAAM,WAAW;YACjB,MAAM,UAAU;YAChB,MAAM,SAAS,CAAC,QAAQ,CAAC,IAAI,WAAW,IAAI,OAAO,CAAC,IAAI;YACxD,MAAM,eAAe,SAAS;YAE9B,0CAA0C;YAC1C,MAAM,WAAW,KAAK,GAAG,CAAC,MAAM,QAAQ;YAExC,kCAAkC;YAClC,MAAM,gBAAgB,WAAW,IAAI,AAAC,OAAO,WAAY,eAAe;YACxE,IAAI,SAAS,GAAG,OAAO,IAAI;YAC3B,IAAI,QAAQ,CAAC,QAAQ,SAAS,KAAK,eAAe,UAAU;YAE5D,yCAAyC;YACzC,MAAM,kBAAkB,WAAW,IAAI,AAAC,SAAS,WAAY,eAAe;YAC5E,IAAI,SAAS,GAAG,OAAO,MAAM;YAC7B,IAAI,QAAQ,CAAC,SAAS,WAAW,SAAS,SAAS,KAAK,iBAAiB,UAAU;YAEnF,mCAAmC;YACnC,MAAM,eAAe,WAAW,IAAI,AAAC,MAAM,WAAY,eAAe;YACtE,IAAI,SAAS,GAAG,OAAO,GAAG;YAC1B,IAAI,QAAQ,CAAC,SAAS,IAAI,CAAC,WAAW,OAAO,GAAG,SAAS,KAAK,cAAc,UAAU;YAEtF,qBAAqB;YACrB,IAAI,IAAI,GAAG;YACX,IAAI,SAAS,GAAG;YAChB,IAAI,SAAS,GAAG;YAEhB,yCAAyC;YACzC,IAAI,QAAQ,CAAC,KAAK,QAAQ,IAAI,SAAS,WAAW,GAAG,SAAS,KAAK;YACnE,IAAI,QAAQ,CAAC,OAAO,SAAS,WAAW,GAAG,SAAS;YAEpD,gDAAgD;YAChD,IAAI,QAAQ,CAAC,OAAO,QAAQ,IAAI,SAAS,WAAW,UAAU,WAAW,GAAG,SAAS,KAAK;YAC1F,IAAI,QAAQ,CAAC,cAAc,SAAS,WAAW,UAAU,WAAW,GAAG,SAAS;YAEhF,0CAA0C;YAC1C,IAAI,QAAQ,CAAC,IAAI,QAAQ,IAAI,SAAS,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,GAAG,SAAS,KAAK;YAC7F,IAAI,QAAQ,CAAC,QAAQ,SAAS,IAAI,CAAC,WAAW,OAAO,IAAI,WAAW,GAAG,SAAS;QAElF;4CAAG;QAAC;QAAM;QAAQ;KAAI;IAEtB,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAO,KAAK;YAAU,OAAO;YAAK,QAAQ;;;;;;;;;;;AAGjD;GAjFgB;KAAA", "debugId": null}}, {"offset": {"line": 4565, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/statistics/statistics-category-chart.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useRef } from 'react';\n\ntype StatisticsCategoryChartProps = {\n  categories: Record<string, number>;\n};\n\nexport function StatisticsCategoryChart({ categories }: StatisticsCategoryChartProps) {\n  const chartRef = useRef<HTMLCanvasElement>(null);\n\n  useEffect(() => {\n    if (!chartRef.current) return;\n\n    const ctx = chartRef.current.getContext('2d');\n    if (!ctx) return;\n\n    // Xóa canvas\n    ctx.clearRect(0, 0, chartRef.current.width, chartRef.current.height);\n    \n    const width = chartRef.current.width;\n    const height = chartRef.current.height;\n    \n    const categoryEntries = Object.entries(categories);\n    \n    if (categoryEntries.length === 0) {\n      // Hiển thị thông báo khi không có dữ liệu\n      ctx.font = 'bold 16px Arial';\n      ctx.fillStyle = '#666';\n      ctx.textAlign = 'center';\n      ctx.textBaseline = 'middle';\n      ctx.fillText('Không có dữ liệu danh mục để hiển thị', width / 2, height / 2);\n      return;\n    }\n    \n    // <PERSON><PERSON>u sắc cho các danh mục\n    const colors = [\n      '#3b82f6', // Xanh dương\n      '#10b981', // Xanh lá\n      '#f59e0b', // Cam\n      '#8b5cf6', // Tím\n      '#ec4899', // Hồng\n      '#06b6d4', // Xanh ngọc\n      '#f43f5e', // Đỏ\n      '#84cc16', // Xanh lá nhạt\n      '#6366f1', // Indigo\n      '#d946ef', // Tím hồng\n    ];\n    \n    const centerX = width / 2;\n    const centerY = height / 2;\n    const radius = Math.min(centerX, centerY) - 60;\n    \n    let startAngle = 0;\n    const total = categoryEntries.reduce((sum, [_, count]) => sum + count, 0);\n    \n    // Vẽ biểu đồ tròn\n    categoryEntries.forEach(([category, count], index) => {\n      const sliceAngle = (count / total) * 2 * Math.PI;\n      const endAngle = startAngle + sliceAngle;\n      \n      ctx.beginPath();\n      ctx.moveTo(centerX, centerY);\n      ctx.arc(centerX, centerY, radius, startAngle, endAngle);\n      ctx.fillStyle = colors[index % colors.length];\n      ctx.fill();\n      \n      // Tính toán vị trí cho nhãn\n      const midAngle = startAngle + sliceAngle / 2;\n      const labelRadius = radius * 0.7;\n      const labelX = centerX + Math.cos(midAngle) * labelRadius;\n      const labelY = centerY + Math.sin(midAngle) * labelRadius;\n      \n      // Hiển thị phần trăm trong biểu đồ nếu góc đủ lớn\n      if (sliceAngle > 0.2) {\n        const percentage = Math.round((count / total) * 100);\n        ctx.font = 'bold 14px Arial';\n        ctx.fillStyle = 'white';\n        ctx.textAlign = 'center';\n        ctx.textBaseline = 'middle';\n        ctx.fillText(`${percentage}%`, labelX, labelY);\n      }\n      \n      startAngle = endAngle;\n    });\n    \n    // Vẽ lỗ trung tâm để tạo biểu đồ dạng donut\n    ctx.beginPath();\n    ctx.moveTo(centerX, centerY);\n    ctx.arc(centerX, centerY, radius * 0.5, 0, 2 * Math.PI);\n    ctx.fillStyle = 'white';\n    ctx.fill();\n    \n    // Vẽ chú thích\n    const legendStartY = height - 20 - categoryEntries.length * 25;\n    const legendStartX = width - 150;\n    \n    categoryEntries.forEach(([category, count], index) => {\n      const legendY = legendStartY + index * 25;\n      \n      // Vẽ ô màu\n      ctx.beginPath();\n      ctx.rect(legendStartX, legendY, 15, 15);\n      ctx.fillStyle = colors[index % colors.length];\n      ctx.fill();\n      \n      // Vẽ tên danh mục\n      ctx.font = '12px Arial';\n      ctx.fillStyle = '#000';\n      ctx.textAlign = 'left';\n      ctx.textBaseline = 'middle';\n      \n      // Cắt ngắn tên danh mục nếu quá dài\n      let displayCategory = category;\n      if (displayCategory.length > 15) {\n        displayCategory = displayCategory.substring(0, 12) + '...';\n      }\n      \n      ctx.fillText(`${displayCategory} (${count})`, legendStartX + 20, legendY + 7);\n    });\n    \n  }, [categories]);\n\n  return (\n    <div className=\"w-full h-80 flex items-center justify-center\">\n      <canvas ref={chartRef} width={500} height={300}></canvas>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQO,SAAS,wBAAwB,EAAE,UAAU,EAAgC;;IAClF,MAAM,WAAW,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAqB;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;6CAAE;YACR,IAAI,CAAC,SAAS,OAAO,EAAE;YAEvB,MAAM,MAAM,SAAS,OAAO,CAAC,UAAU,CAAC;YACxC,IAAI,CAAC,KAAK;YAEV,aAAa;YACb,IAAI,SAAS,CAAC,GAAG,GAAG,SAAS,OAAO,CAAC,KAAK,EAAE,SAAS,OAAO,CAAC,MAAM;YAEnE,MAAM,QAAQ,SAAS,OAAO,CAAC,KAAK;YACpC,MAAM,SAAS,SAAS,OAAO,CAAC,MAAM;YAEtC,MAAM,kBAAkB,OAAO,OAAO,CAAC;YAEvC,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBAChC,0CAA0C;gBAC1C,IAAI,IAAI,GAAG;gBACX,IAAI,SAAS,GAAG;gBAChB,IAAI,SAAS,GAAG;gBAChB,IAAI,YAAY,GAAG;gBACnB,IAAI,QAAQ,CAAC,yCAAyC,QAAQ,GAAG,SAAS;gBAC1E;YACF;YAEA,2BAA2B;YAC3B,MAAM,SAAS;gBACb;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,MAAM,UAAU,QAAQ;YACxB,MAAM,UAAU,SAAS;YACzB,MAAM,SAAS,KAAK,GAAG,CAAC,SAAS,WAAW;YAE5C,IAAI,aAAa;YACjB,MAAM,QAAQ,gBAAgB,MAAM;2DAAC,CAAC,KAAK,CAAC,GAAG,MAAM,GAAK,MAAM;0DAAO;YAEvE,kBAAkB;YAClB,gBAAgB,OAAO;qDAAC,CAAC,CAAC,UAAU,MAAM,EAAE;oBAC1C,MAAM,aAAa,AAAC,QAAQ,QAAS,IAAI,KAAK,EAAE;oBAChD,MAAM,WAAW,aAAa;oBAE9B,IAAI,SAAS;oBACb,IAAI,MAAM,CAAC,SAAS;oBACpB,IAAI,GAAG,CAAC,SAAS,SAAS,QAAQ,YAAY;oBAC9C,IAAI,SAAS,GAAG,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;oBAC7C,IAAI,IAAI;oBAER,4BAA4B;oBAC5B,MAAM,WAAW,aAAa,aAAa;oBAC3C,MAAM,cAAc,SAAS;oBAC7B,MAAM,SAAS,UAAU,KAAK,GAAG,CAAC,YAAY;oBAC9C,MAAM,SAAS,UAAU,KAAK,GAAG,CAAC,YAAY;oBAE9C,kDAAkD;oBAClD,IAAI,aAAa,KAAK;wBACpB,MAAM,aAAa,KAAK,KAAK,CAAC,AAAC,QAAQ,QAAS;wBAChD,IAAI,IAAI,GAAG;wBACX,IAAI,SAAS,GAAG;wBAChB,IAAI,SAAS,GAAG;wBAChB,IAAI,YAAY,GAAG;wBACnB,IAAI,QAAQ,CAAC,GAAG,WAAW,CAAC,CAAC,EAAE,QAAQ;oBACzC;oBAEA,aAAa;gBACf;;YAEA,4CAA4C;YAC5C,IAAI,SAAS;YACb,IAAI,MAAM,CAAC,SAAS;YACpB,IAAI,GAAG,CAAC,SAAS,SAAS,SAAS,KAAK,GAAG,IAAI,KAAK,EAAE;YACtD,IAAI,SAAS,GAAG;YAChB,IAAI,IAAI;YAER,eAAe;YACf,MAAM,eAAe,SAAS,KAAK,gBAAgB,MAAM,GAAG;YAC5D,MAAM,eAAe,QAAQ;YAE7B,gBAAgB,OAAO;qDAAC,CAAC,CAAC,UAAU,MAAM,EAAE;oBAC1C,MAAM,UAAU,eAAe,QAAQ;oBAEvC,WAAW;oBACX,IAAI,SAAS;oBACb,IAAI,IAAI,CAAC,cAAc,SAAS,IAAI;oBACpC,IAAI,SAAS,GAAG,MAAM,CAAC,QAAQ,OAAO,MAAM,CAAC;oBAC7C,IAAI,IAAI;oBAER,kBAAkB;oBAClB,IAAI,IAAI,GAAG;oBACX,IAAI,SAAS,GAAG;oBAChB,IAAI,SAAS,GAAG;oBAChB,IAAI,YAAY,GAAG;oBAEnB,oCAAoC;oBACpC,IAAI,kBAAkB;oBACtB,IAAI,gBAAgB,MAAM,GAAG,IAAI;wBAC/B,kBAAkB,gBAAgB,SAAS,CAAC,GAAG,MAAM;oBACvD;oBAEA,IAAI,QAAQ,CAAC,GAAG,gBAAgB,EAAE,EAAE,MAAM,CAAC,CAAC,EAAE,eAAe,IAAI,UAAU;gBAC7E;;QAEF;4CAAG;QAAC;KAAW;IAEf,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAO,KAAK;YAAU,OAAO;YAAK,QAAQ;;;;;;;;;;;AAGjD;GAxHgB;KAAA", "debugId": null}}, {"offset": {"line": 4707, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf;KAVS", "debugId": null}}, {"offset": {"line": 4737, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/app/statistics/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Layout } from '@/components/layout/layout';\nimport { StatisticsService, TaskStatistics, TimeBlockStatistics, ProductivityStatistics } from '@/lib/services/statistics-service';\nimport { useStatistics } from '@/lib/contexts/statistics-context';\nimport { Task, Project } from '@/lib/types';\nimport ProjectSelector from '@/components/project/ProjectSelector';\nimport { StatisticsCard } from '@/components/statistics/statistics-card';\nimport { StatisticsChart } from '@/components/statistics/statistics-chart';\nimport { StatisticsPriorityChart } from '@/components/statistics/statistics-priority-chart';\nimport { StatisticsCategoryChart } from '@/components/statistics/statistics-category-chart';\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Button } from '@/components/ui/button';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { RefreshCw, Calendar } from 'lucide-react';\n\nexport default function StatisticsPage() {\n  const { refreshTrigger } = useStatistics();\n  const [taskStats, setTaskStats] = useState<TaskStatistics | null>(null);\n  const [timeBlockStats, setTimeBlockStats] = useState<TimeBlockStatistics | null>(null);\n  const [productivityStats, setProductivityStats] = useState<ProductivityStatistics | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [dateRange, setDateRange] = useState({\n    startDate: '',\n    endDate: '',\n  });\n\n  // Load statistics từ API\n  const loadStatistics = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      console.log('StatisticsPage: Đang tải thống kê...', dateRange);\n\n      const query = {\n        startDate: dateRange.startDate || undefined,\n        endDate: dateRange.endDate || undefined,\n      };\n\n      const [tasks, timeBlocks, productivity] = await Promise.all([\n        StatisticsService.getTaskStatistics(query),\n        StatisticsService.getTimeBlockStatistics(query),\n        StatisticsService.getProductivityStatistics(query),\n      ]);\n\n      setTaskStats(tasks);\n      setTimeBlockStats(timeBlocks);\n      setProductivityStats(productivity);\n\n      console.log('StatisticsPage: Đã tải thống kê thành công');\n    } catch (err: any) {\n      console.error('StatisticsPage: Lỗi tải thống kê:', err);\n      setError(err.message || 'Không thể tải dữ liệu thống kê');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadStatistics();\n  }, []);\n\n  // Listen for refresh trigger từ context\n  useEffect(() => {\n    if (refreshTrigger > 0) {\n      console.log('StatisticsPage: Received refresh trigger, reloading statistics');\n      loadStatistics();\n    }\n  }, [refreshTrigger]);\n  \n  // Tính toán thống kê từ API data\n  const totalTasks = taskStats?.total || 0;\n  const completedTasks = taskStats?.completed || 0;\n  const pendingTasks = taskStats?.pending || 0;\n  const overdueTasks = taskStats?.overdue || 0;\n  const completionRate = totalTasks > 0 ? Math.round((completedTasks / totalTasks) * 100) : 0;\n\n  // Thống kê theo mức độ ưu tiên\n  const highPriorityTasks = taskStats?.byPriority?.high || 0;\n  const mediumPriorityTasks = taskStats?.byPriority?.medium || 0;\n  const lowPriorityTasks = taskStats?.byPriority?.low || 0;\n\n  // Thống kê theo danh mục\n  const categoryCounts = taskStats?.byCategory || {};\n\n  // Thống kê năng suất\n  const productivityScore = productivityStats?.productivityScore || 0;\n  const taskCompletionRate = productivityStats?.taskCompletionRate || 0;\n  const timeBlockCompletionRate = productivityStats?.timeBlockCompletionRate || 0;\n  \n  if (loading) {\n    return (\n      <Layout>\n        <div className=\"max-w-6xl mx-auto p-4 space-y-8\">\n          <div className=\"flex flex-col\">\n            <h1 className=\"text-3xl font-bold tracking-tight\">Thống kê công việc</h1>\n            <p className=\"text-muted-foreground\">Đang tải dữ liệu thống kê...</p>\n          </div>\n\n          {/* Loading skeleton */}\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n            {[1, 2, 3, 4].map((i) => (\n              <Card key={i}>\n                <CardContent className=\"p-6\">\n                  <Skeleton className=\"h-4 w-20 mb-2\" />\n                  <Skeleton className=\"h-8 w-16 mb-2\" />\n                  <Skeleton className=\"h-3 w-32\" />\n                </CardContent>\n              </Card>\n            ))}\n          </div>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <Skeleton className=\"h-64 w-full\" />\n            </CardContent>\n          </Card>\n        </div>\n      </Layout>\n    );\n  }\n  \n  return (\n    <Layout>\n      <div className=\"max-w-6xl mx-auto p-4 space-y-8\">\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between\">\n          <div>\n            <h1 className=\"text-3xl font-bold tracking-tight\">Thống kê công việc</h1>\n            <p className=\"text-muted-foreground\">\n              Tổng quan về tình trạng công việc và hiệu suất của bạn\n            </p>\n          </div>\n          <div className=\"flex gap-2 mt-4 sm:mt-0\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={loadStatistics}\n              disabled={loading}\n              className=\"flex items-center gap-2\"\n            >\n              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\n              Làm mới\n            </Button>\n          </div>\n        </div>\n\n        {/* Hiển thị lỗi nếu có */}\n        {error && (\n          <Alert variant=\"destructive\">\n            <AlertDescription className=\"flex items-center justify-between\">\n              <span>{error}</span>\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                onClick={loadStatistics}\n                className=\"flex items-center gap-1\"\n              >\n                <RefreshCw className=\"h-3 w-3\" />\n                Thử lại\n              </Button>\n            </AlertDescription>\n          </Alert>\n        )}\n        \n        {/* Thống kê tổng quan */}\n        <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n          <StatisticsCard\n            title=\"Tổng số công việc\"\n            value={totalTasks}\n            description=\"Tổng số công việc đã tạo\"\n            icon=\"task\"\n          />\n          <StatisticsCard\n            title=\"Đã hoàn thành\"\n            value={completedTasks}\n            description={`${completionRate}% công việc đã hoàn thành`}\n            icon=\"check\"\n            trend={completionRate > 50 ? 'up' : 'down'}\n          />\n          <StatisticsCard\n            title=\"Đang thực hiện\"\n            value={pendingTasks}\n            description=\"Công việc chưa hoàn thành\"\n            icon=\"pending\"\n          />\n          <StatisticsCard\n            title=\"Quá hạn\"\n            value={overdueTasks}\n            description=\"Công việc đã quá hạn\"\n            icon=\"priority\"\n            trend={overdueTasks > 0 ? 'warning' : 'neutral'}\n          />\n        </div>\n        \n        {/* Biểu đồ và phân tích chi tiết */}\n        <Tabs defaultValue=\"overview\" className=\"w-full\">\n          <TabsList className=\"grid w-full grid-cols-5 md:w-auto md:grid-cols-5\">\n            <TabsTrigger value=\"overview\">Tổng quan</TabsTrigger>\n            <TabsTrigger value=\"priority\">Mức độ ưu tiên</TabsTrigger>\n            <TabsTrigger value=\"category\">Danh mục</TabsTrigger>\n            <TabsTrigger value=\"productivity\">Năng suất</TabsTrigger>\n            <TabsTrigger value=\"timeblocks\">Khối thời gian</TabsTrigger>\n          </TabsList>\n          \n          <TabsContent value=\"overview\" className=\"mt-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Tổng quan công việc</CardTitle>\n                <CardDescription>\n                  Biểu đồ thể hiện tỷ lệ công việc đã hoàn thành và đang thực hiện\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <StatisticsChart \n                  completed={completedTasks} \n                  pending={pendingTasks} \n                />\n              </CardContent>\n            </Card>\n          </TabsContent>\n          \n          <TabsContent value=\"priority\" className=\"mt-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Phân loại theo mức độ ưu tiên</CardTitle>\n                <CardDescription>\n                  Biểu đồ thể hiện số lượng công việc theo mức độ ưu tiên\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <StatisticsPriorityChart \n                  high={highPriorityTasks} \n                  medium={mediumPriorityTasks} \n                  low={lowPriorityTasks} \n                />\n              </CardContent>\n            </Card>\n          </TabsContent>\n          \n          <TabsContent value=\"category\" className=\"mt-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Phân loại theo danh mục</CardTitle>\n                <CardDescription>\n                  Biểu đồ thể hiện số lượng công việc theo từng danh mục\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <StatisticsCategoryChart categories={categoryCounts} />\n              </CardContent>\n            </Card>\n          </TabsContent>\n          \n          <TabsContent value=\"productivity\" className=\"mt-4\">\n            <div className=\"grid gap-4 md:grid-cols-3\">\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Điểm năng suất</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{productivityScore.toFixed(1)}%</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    điểm năng suất tổng thể\n                  </p>\n                </CardContent>\n              </Card>\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Tỷ lệ hoàn thành công việc</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{taskCompletionRate.toFixed(1)}%</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    công việc đã hoàn thành\n                  </p>\n                </CardContent>\n              </Card>\n              <Card>\n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-sm font-medium\">Tỷ lệ hoàn thành khối thời gian</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <div className=\"text-2xl font-bold\">{timeBlockCompletionRate.toFixed(1)}%</div>\n                  <p className=\"text-xs text-muted-foreground\">\n                    khối thời gian đã hoàn thành\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          </TabsContent>\n\n          <TabsContent value=\"timeblocks\" className=\"mt-4\">\n            <Card>\n              <CardHeader>\n                <CardTitle>Thống kê khối thời gian</CardTitle>\n                <CardDescription>\n                  Tổng quan về việc sử dụng thời gian và hiệu quả\n                </CardDescription>\n              </CardHeader>\n              <CardContent>\n                <div className=\"grid gap-4 md:grid-cols-3\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">{timeBlockStats?.totalHours?.toFixed(1) || 0}h</div>\n                    <p className=\"text-sm text-muted-foreground\">Tổng thời gian</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">{timeBlockStats?.completedHours?.toFixed(1) || 0}h</div>\n                    <p className=\"text-sm text-muted-foreground\">Đã hoàn thành</p>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold\">{timeBlockStats?.completionRate?.toFixed(1) || 0}%</div>\n                    <p className=\"text-sm text-muted-foreground\">Tỷ lệ hoàn thành</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </TabsContent>\n        </Tabs>\n      </div>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;AAmBe,SAAS;;IACtB,MAAM,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAClE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA8B;IACjF,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiC;IAC1F,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,WAAW;QACX,SAAS;IACX;IAEA,yBAAyB;IACzB,MAAM,iBAAiB;QACrB,IAAI;YACF,WAAW;YACX,SAAS;YACT,QAAQ,GAAG,CAAC,wCAAwC;YAEpD,MAAM,QAAQ;gBACZ,WAAW,UAAU,SAAS,IAAI;gBAClC,SAAS,UAAU,OAAO,IAAI;YAChC;YAEA,MAAM,CAAC,OAAO,YAAY,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,kJAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gBACpC,kJAAA,CAAA,oBAAiB,CAAC,sBAAsB,CAAC;gBACzC,kJAAA,CAAA,oBAAiB,CAAC,yBAAyB,CAAC;aAC7C;YAED,aAAa;YACb,kBAAkB;YAClB,qBAAqB;YAErB,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,qCAAqC;YACnD,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR;QACF;mCAAG,EAAE;IAEL,wCAAwC;IACxC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,iBAAiB,GAAG;gBACtB,QAAQ,GAAG,CAAC;gBACZ;YACF;QACF;mCAAG;QAAC;KAAe;IAEnB,iCAAiC;IACjC,MAAM,aAAa,WAAW,SAAS;IACvC,MAAM,iBAAiB,WAAW,aAAa;IAC/C,MAAM,eAAe,WAAW,WAAW;IAC3C,MAAM,eAAe,WAAW,WAAW;IAC3C,MAAM,iBAAiB,aAAa,IAAI,KAAK,KAAK,CAAC,AAAC,iBAAiB,aAAc,OAAO;IAE1F,+BAA+B;IAC/B,MAAM,oBAAoB,WAAW,YAAY,QAAQ;IACzD,MAAM,sBAAsB,WAAW,YAAY,UAAU;IAC7D,MAAM,mBAAmB,WAAW,YAAY,OAAO;IAEvD,yBAAyB;IACzB,MAAM,iBAAiB,WAAW,cAAc,CAAC;IAEjD,qBAAqB;IACrB,MAAM,oBAAoB,mBAAmB,qBAAqB;IAClE,MAAM,qBAAqB,mBAAmB,sBAAsB;IACpE,MAAM,0BAA0B,mBAAmB,2BAA2B;IAE9E,IAAI,SAAS;QACX,qBACE,6LAAC,yIAAA,CAAA,SAAM;sBACL,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,6LAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAIvC,6LAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE,CAAC,GAAG,CAAC,CAAC,kBACjB,6LAAC,mIAAA,CAAA,OAAI;0CACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC,uIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;;+BAJb;;;;;;;;;;kCAUf,6LAAC,mIAAA,CAAA,OAAI;kCACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMhC;IAEA,qBACE,6LAAC,yIAAA,CAAA,SAAM;kBACL,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU;gCACV,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;oCAAI;;;;;;;;;;;;;;;;;;gBAOzE,uBACC,6LAAC,oIAAA,CAAA,QAAK;oBAAC,SAAQ;8BACb,cAAA,6LAAC,oIAAA,CAAA,mBAAgB;wBAAC,WAAU;;0CAC1B,6LAAC;0CAAM;;;;;;0CACP,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;8BAQzC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,yJAAA,CAAA,iBAAc;4BACb,OAAM;4BACN,OAAO;4BACP,aAAY;4BACZ,MAAK;;;;;;sCAEP,6LAAC,yJAAA,CAAA,iBAAc;4BACb,OAAM;4BACN,OAAO;4BACP,aAAa,GAAG,eAAe,yBAAyB,CAAC;4BACzD,MAAK;4BACL,OAAO,iBAAiB,KAAK,OAAO;;;;;;sCAEtC,6LAAC,yJAAA,CAAA,iBAAc;4BACb,OAAM;4BACN,OAAO;4BACP,aAAY;4BACZ,MAAK;;;;;;sCAEP,6LAAC,yJAAA,CAAA,iBAAc;4BACb,OAAM;4BACN,OAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,OAAO,eAAe,IAAI,YAAY;;;;;;;;;;;;8BAK1C,6LAAC,mIAAA,CAAA,OAAI;oBAAC,cAAa;oBAAW,WAAU;;sCACtC,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,WAAU;;8CAClB,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAW;;;;;;8CAC9B,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAe;;;;;;8CAClC,6LAAC,mIAAA,CAAA,cAAW;oCAAC,OAAM;8CAAa;;;;;;;;;;;;sCAGlC,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,0JAAA,CAAA,kBAAe;4CACd,WAAW;4CACX,SAAS;;;;;;;;;;;;;;;;;;;;;;sCAMjB,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,sKAAA,CAAA,0BAAuB;4CACtB,MAAM;4CACN,QAAQ;4CACR,KAAK;;;;;;;;;;;;;;;;;;;;;;sCAMb,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAW,WAAU;sCACtC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC,sKAAA,CAAA,0BAAuB;4CAAC,YAAY;;;;;;;;;;;;;;;;;;;;;;sCAK3C,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAe,WAAU;sCAC1C,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;;;;;;0DAE7C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;4DAAsB,kBAAkB,OAAO,CAAC;4DAAG;;;;;;;kEAClE,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAKjD,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;;;;;;0DAE7C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;4DAAsB,mBAAmB,OAAO,CAAC;4DAAG;;;;;;;kEACnE,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;kDAKjD,6LAAC,mIAAA,CAAA,OAAI;;0DACH,6LAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;0DACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oDAAC,WAAU;8DAAsB;;;;;;;;;;;0DAE7C,6LAAC,mIAAA,CAAA,cAAW;;kEACV,6LAAC;wDAAI,WAAU;;4DAAsB,wBAAwB,OAAO,CAAC;4DAAG;;;;;;;kEACxE,6LAAC;wDAAE,WAAU;kEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQrD,6LAAC,mIAAA,CAAA,cAAW;4BAAC,OAAM;4BAAa,WAAU;sCACxC,cAAA,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;;0DACT,6LAAC,mIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,6LAAC,mIAAA,CAAA,kBAAe;0DAAC;;;;;;;;;;;;kDAInB,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAsB,gBAAgB,YAAY,QAAQ,MAAM;gEAAE;;;;;;;sEACjF,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAE/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAsB,gBAAgB,gBAAgB,QAAQ,MAAM;gEAAE;;;;;;;sEACrF,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;8DAE/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;gEAAsB,gBAAgB,gBAAgB,QAAQ,MAAM;gEAAE;;;;;;;sEACrF,6LAAC;4DAAE,WAAU;sEAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjE;GAlTwB;;QACK,mJAAA,CAAA,gBAAa;;;KADlB", "debugId": null}}]}