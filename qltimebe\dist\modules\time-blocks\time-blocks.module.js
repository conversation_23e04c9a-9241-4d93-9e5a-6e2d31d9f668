"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeBlocksModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const time_blocks_controller_1 = require("./time-blocks.controller");
const time_blocks_service_1 = require("./time-blocks.service");
const time_block_schema_1 = require("./schemas/time-block.schema");
let TimeBlocksModule = class TimeBlocksModule {
};
exports.TimeBlocksModule = TimeBlocksModule;
exports.TimeBlocksModule = TimeBlocksModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([{ name: time_block_schema_1.TimeBlock.name, schema: time_block_schema_1.TimeBlockSchema }]),
        ],
        controllers: [time_blocks_controller_1.TimeBlocksController],
        providers: [time_blocks_service_1.TimeBlocksService],
        exports: [time_blocks_service_1.TimeBlocksService],
    })
], TimeBlocksModule);
//# sourceMappingURL=time-blocks.module.js.map