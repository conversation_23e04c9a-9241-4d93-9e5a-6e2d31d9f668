import { TasksService } from './tasks.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class TasksController {
    private readonly tasksService;
    constructor(tasksService: TasksService);
    create(createTaskDto: CreateTaskDto, user: UserDocument): Promise<import("./schemas/task.schema").Task>;
    findAll(user: UserDocument, query: any): Promise<import("./schemas/task.schema").Task[]>;
    findOne(id: string, user: UserDocument): Promise<import("./schemas/task.schema").Task>;
    update(id: string, updateTaskDto: UpdateTaskDto, user: UserDocument): Promise<import("./schemas/task.schema").Task>;
    remove(id: string, user: UserDocument): Promise<{
        success: boolean;
    }>;
    toggleComplete(id: string, user: UserDocument): Promise<import("./schemas/task.schema").Task>;
    updateStatus(id: string, status: string, user: UserDocument): Promise<import("./schemas/task.schema").Task>;
}
