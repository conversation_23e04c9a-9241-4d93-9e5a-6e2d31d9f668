import { AuthService } from './auth.service';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class AuthController {
    private authService;
    constructor(authService: AuthService);
    register(registerDto: RegisterDto): Promise<{
        user: {
            id: import("mongoose").Types.ObjectId;
            email: string;
            name: string;
        };
        token: string;
    }>;
    login(loginDto: LoginDto): Promise<{
        user: {
            id: import("mongoose").Types.ObjectId;
            email: string;
            name: string;
        };
        token: string;
    }>;
    getMe(user: UserDocument): Promise<{
        id: import("mongoose").Types.ObjectId;
        email: string;
        name: string;
        avatar: string | undefined;
    }>;
    logout(): {
        success: boolean;
        message: string;
    };
}
