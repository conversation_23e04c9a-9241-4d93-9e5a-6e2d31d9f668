"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const app_module_1 = require("./app.module");
const platform_fastify_1 = require("@nestjs/platform-fastify");
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
async function bootstrap() {
    try {
        console.log('Đang khởi động ứng dụng...');
        const app = await core_1.NestFactory.create(app_module_1.AppModule, new platform_fastify_1.FastifyAdapter());
        console.log('Đã tạo ứng dụng NestJS');
        app.enableCors({
            origin: [
                'http://localhost:3000',
                'http://127.0.0.1:3000',
                'http://frontend:3000',
                process.env.FRONTEND_URL || 'http://localhost:3000'
            ],
            methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
            allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
            credentials: true,
            preflightContinue: false,
            optionsSuccessStatus: 204,
        });
        console.log('Đã cấu hình CORS');
        app.useGlobalPipes(new common_1.ValidationPipe({
            whitelist: true,
            transform: true,
            forbidNonWhitelisted: true,
        }));
        console.log('Đã cấu hình validation pipe');
        const config = new swagger_1.DocumentBuilder()
            .setTitle('QLTime API')
            .setDescription('API cho ứng dụng quản lý thời gian QLTime')
            .setVersion('1.0')
            .addBearerAuth()
            .build();
        const document = swagger_1.SwaggerModule.createDocument(app, config);
        swagger_1.SwaggerModule.setup('api/docs', app, document);
        console.log('Đã cấu hình Swagger');
        const port = process.env.PORT || 3001;
        console.log(`Đang khởi động server trên port ${port}...`);
        await app.listen(port, '0.0.0.0');
        console.log(`Ứng dụng đang chạy tại: http://0.0.0.0:${port}`);
        console.log(`Swagger API docs: http://0.0.0.0:${port}/api/docs`);
    }
    catch (error) {
        console.error('Lỗi khi khởi động ứng dụng:', error);
        process.exit(1);
    }
}
bootstrap();
//# sourceMappingURL=main.js.map