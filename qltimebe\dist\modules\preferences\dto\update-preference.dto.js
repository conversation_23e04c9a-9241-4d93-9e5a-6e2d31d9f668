"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdatePreferenceDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdatePreferenceDto {
    theme;
    language;
    notifications;
    calendarView;
    startOfWeek;
    showCompletedTasks;
}
exports.UpdatePreferenceDto = UpdatePreferenceDto;
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chủ đề',
        enum: ['light', 'dark', 'system'],
        example: 'dark',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['light', 'dark', 'system'], { message: 'Chủ đề không hợp lệ' }),
    __metadata("design:type", String)
], UpdatePreferenceDto.prototype, "theme", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngôn ngữ',
        example: 'en',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Ngôn ngữ phải là chuỗi' }),
    __metadata("design:type", String)
], UpdatePreferenceDto.prototype, "language", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Bật/tắt thông báo',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Trạng thái thông báo phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdatePreferenceDto.prototype, "notifications", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Chế độ xem lịch mặc định',
        enum: ['day', 'week', 'month'],
        example: 'month',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['day', 'week', 'month'], { message: 'Chế độ xem lịch không hợp lệ' }),
    __metadata("design:type", String)
], UpdatePreferenceDto.prototype, "calendarView", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày bắt đầu tuần',
        enum: [0, 1, 6],
        example: 0,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)([0, 1, 6], { message: 'Ngày bắt đầu tuần không hợp lệ' }),
    __metadata("design:type", Number)
], UpdatePreferenceDto.prototype, "startOfWeek", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Hiển thị công việc đã hoàn thành',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Trạng thái hiển thị công việc đã hoàn thành phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdatePreferenceDto.prototype, "showCompletedTasks", void 0);
//# sourceMappingURL=update-preference.dto.js.map