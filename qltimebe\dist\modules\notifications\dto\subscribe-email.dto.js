"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateEmailSubscriptionDto = exports.SubscribeEmailDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class SubscribeEmailDto {
    email;
    taskReminders;
    dailySummary;
    weeklyReport;
    reminderHours;
}
exports.SubscribeEmailDto = SubscribeEmailDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Địa chỉ email để nhận thông báo',
        example: '<EMAIL>',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Email không được để trống' }),
    (0, class_validator_1.IsEmail)({}, { message: 'Email không hợp lệ' }),
    __metadata("design:type", String)
], SubscribeEmailDto.prototype, "email", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận thông báo công việc sắp hết hạn',
        example: true,
        default: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'taskReminders phải là boolean' }),
    __metadata("design:type", Boolean)
], SubscribeEmailDto.prototype, "taskReminders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận tóm tắt hàng ngày',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'dailySummary phải là boolean' }),
    __metadata("design:type", Boolean)
], SubscribeEmailDto.prototype, "dailySummary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận báo cáo hàng tuần',
        example: false,
        default: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'weeklyReport phải là boolean' }),
    __metadata("design:type", Boolean)
], SubscribeEmailDto.prototype, "weeklyReport", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số giờ trước khi nhắc nhở (1-168 giờ)',
        example: 24,
        default: 24,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'reminderHours phải là số' }),
    (0, class_validator_1.Min)(1, { message: 'reminderHours phải ít nhất 1 giờ' }),
    (0, class_validator_1.Max)(168, { message: 'reminderHours không được quá 168 giờ (7 ngày)' }),
    __metadata("design:type", Number)
], SubscribeEmailDto.prototype, "reminderHours", void 0);
class UpdateEmailSubscriptionDto {
    isActive;
    taskReminders;
    dailySummary;
    weeklyReport;
    reminderHours;
}
exports.UpdateEmailSubscriptionDto = UpdateEmailSubscriptionDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Trạng thái đăng ký',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'isActive phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateEmailSubscriptionDto.prototype, "isActive", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận thông báo công việc sắp hết hạn',
        example: true,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'taskReminders phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateEmailSubscriptionDto.prototype, "taskReminders", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận tóm tắt hàng ngày',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'dailySummary phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateEmailSubscriptionDto.prototype, "dailySummary", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Nhận báo cáo hàng tuần',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'weeklyReport phải là boolean' }),
    __metadata("design:type", Boolean)
], UpdateEmailSubscriptionDto.prototype, "weeklyReport", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Số giờ trước khi nhắc nhở (1-168 giờ)',
        example: 24,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsNumber)({}, { message: 'reminderHours phải là số' }),
    (0, class_validator_1.Min)(1, { message: 'reminderHours phải ít nhất 1 giờ' }),
    (0, class_validator_1.Max)(168, { message: 'reminderHours không được quá 168 giờ (7 ngày)' }),
    __metadata("design:type", Number)
], UpdateEmailSubscriptionDto.prototype, "reminderHours", void 0);
//# sourceMappingURL=subscribe-email.dto.js.map