import { Model } from 'mongoose';
import { EmailSubscription, EmailSubscriptionDocument } from '../schemas/email-subscription.schema';
import { PublicEmailSubscription, PublicEmailSubscriptionDocument } from '../schemas/public-email-subscription.schema';
import { SubscribeEmailDto, UpdateEmailSubscriptionDto } from '../dto/subscribe-email.dto';
import { PublicSubscribeEmailDto } from '../dto/public-subscribe-email.dto';
import { EmailService } from './email.service';
import { TasksService } from '../../tasks/tasks.service';
import { UsersService } from '../../users/users.service';
export declare class NotificationsService {
    private emailSubscriptionModel;
    private publicEmailSubscriptionModel;
    private emailService;
    private tasksService;
    private usersService;
    constructor(emailSubscriptionModel: Model<EmailSubscriptionDocument>, publicEmailSubscriptionModel: Model<PublicEmailSubscriptionDocument>, emailService: EmailService, tasksService: TasksService, usersService: UsersService);
    subscribeEmail(userId: string, subscribeDto: SubscribeEmailDto): Promise<EmailSubscription>;
    subscribeEmailPublic(subscribeDto: PublicSubscribeEmailDto): Promise<PublicEmailSubscription>;
    getSubscription(userId: string): Promise<EmailSubscription[]>;
    updateSubscription(userId: string, subscriptionId: string, updateDto: UpdateEmailSubscriptionDto): Promise<EmailSubscription>;
    unsubscribe(token: string): Promise<{
        message: string;
    }>;
    sendTaskReminders(): Promise<{
        sent: number;
        failed: number;
    }>;
    deleteSubscription(userId: string, subscriptionId: string): Promise<{
        message: string;
    }>;
}
