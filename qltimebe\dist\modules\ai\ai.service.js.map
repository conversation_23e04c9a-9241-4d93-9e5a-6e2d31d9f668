{"version": 3, "file": "ai.service.js", "sourceRoot": "", "sources": ["../../../src/modules/ai/ai.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,2CAA+C;AAC/C,+CAA+C;AAC/C,uCAAiC;AACjC,mCAAgC;AAChC,kDAAyE;AACzE,uEAAiF;AAEjF,+BAAoC;AAG7B,IAAM,SAAS,iBAAf,MAAM,SAAS;IAMV;IACA;IANO,MAAM,GAAG,IAAI,eAAM,CAAC,WAAS,CAAC,IAAI,CAAC,CAAC;IAC7C,EAAE,CAAM;IAEhB,YAEU,gBAA4C,EAC5C,aAA4B;QAD5B,qBAAgB,GAAhB,gBAAgB,CAA4B;QAC5C,kBAAa,GAAb,aAAa,CAAe;QAEpC,IAAI,CAAC,YAAY,EAAE,CAAC;IACtB,CAAC;IAEO,YAAY;QAClB,IAAI,CAAC;YACH,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,gBAAgB,CAAC,CAAC;YAChE,IAAI,CAAC,MAAM,EAAE,CAAC;gBACZ,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,wDAAwD,CAAC,CAAC;gBAC3E,OAAO;YACT,CAAC;YAED,IAAI,CAAC,EAAE,GAAG,IAAA,eAAM,EAAC;gBACf,OAAO,EAAE,CAAC,IAAA,mBAAQ,GAAE,CAAC;gBACrB,KAAK,EAAE,mCAAwB;aAChC,CAAC,CAAC;YAEH,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;QAC/D,CAAC;IACH,CAAC;IAED,KAAK,CAAC,IAAI,CAAC,MAAc,EAAE,WAA2B;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,MAAM,IAAI,KAAK,CAAC,0BAA0B,CAAC,CAAC;YAC9C,CAAC;YAED,MAAM,SAAS,GAAG,WAAW,CAAC,SAAS,IAAI,IAAA,SAAM,GAAE,CAAC;YAGpD,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,MAAM,EAAE,WAAW,CAAC,OAAO,EAAE,SAAS,CAAC,CAAC;YAG3E,MAAM,WAAW,GAAG,WAAW,CAAC,WAAW,IAAI,EAAE,CAAC;YAClD,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,GAAG,CAAC;gBAC3C,CAAC,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,CAAC,IAAI,KAAK,MAAM,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,IAAI,KAAK,GAAG,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;gBACnG,CAAC,CAAC,EAAE,CAAC;YAEP,MAAM,MAAM,GAAG;;;EAGnB,cAAc,CAAC,CAAC,CAAC,6BAA6B,cAAc,IAAI,CAAC,CAAC,CAAC,EAAE;;cAEzD,WAAW,CAAC,OAAO;;;;;;;;yBAQR,CAAC;YAEpB,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAGhD,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,WAAW,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC;YAEjE,OAAO;gBACL,QAAQ,EAAE,IAAI;gBACd,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;YAC3C,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,cAAiC;QACrD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;YACtD,CAAC;YAED,MAAM,MAAM,GAAG;;;WAGV,cAAc,CAAC,KAAK;SACtB,cAAc,CAAC,WAAW,IAAI,gBAAgB;;;;;;;;qCAQlB,CAAC;YAEhC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,EAAE,CAAC;YAE3C,IAAI,CAAC,MAAM,EAAE,QAAQ,EAAE,KAAK,CAAC,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE,CAAC;gBACjD,OAAO,QAAqC,CAAC;YAC/C,CAAC;YAED,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC,uBAAuB,CAAC,cAAc,CAAC,CAAC;QACtD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,cAAiC;QACpD,IAAI,CAAC;YACH,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE,CAAC;gBACb,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,MAAM,MAAM,GAAG;;;WAGV,cAAc,CAAC,KAAK;SACtB,cAAc,CAAC,WAAW,IAAI,gBAAgB;iBACtC,KAAK,CAAC,kBAAkB,CAAC,OAAO,CAAC;;;;;;;;wCAQV,CAAC;YAEnC,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC;YAChD,MAAM,SAAS,GAAG,QAAQ,CAAC,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC;YAExC,IAAI,KAAK,CAAC,SAAS,CAAC,IAAI,SAAS,GAAG,CAAC,IAAI,SAAS,GAAG,GAAG,EAAE,CAAC;gBACzD,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;YACrD,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,CAAC;YAC7C,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;YACvD,OAAO,IAAI,CAAC,sBAAsB,CAAC,cAAc,CAAC,CAAC;QACrD,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,MAAc,EAAE,SAAkB,EAAE,QAAgB,EAAE;QACzE,MAAM,KAAK,GAAQ,EAAE,MAAM,EAAE,CAAC;QAC9B,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,OAAO,IAAI,CAAC,gBAAgB;aACzB,IAAI,CAAC,KAAK,CAAC;aACX,IAAI,CAAC,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,CAAC;aACvB,KAAK,CAAC,KAAK,CAAC;aACZ,IAAI,EAAE,CAAC;IACZ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,SAAkB;QACvD,MAAM,KAAK,GAAQ,EAAE,MAAM,EAAE,CAAC;QAC9B,IAAI,SAAS,EAAE,CAAC;YACd,KAAK,CAAC,SAAS,GAAG,SAAS,CAAC;QAC9B,CAAC;QAED,MAAM,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,CAAC;IACvD,CAAC;IAEO,KAAK,CAAC,eAAe,CAC3B,MAAc,EACd,IAA0B,EAC1B,OAAe,EACf,SAAiB;QAEjB,IAAI,CAAC;YACH,MAAM,WAAW,GAAG,IAAI,IAAI,CAAC,gBAAgB,CAAC;gBAC5C,MAAM;gBACN,IAAI;gBACJ,OAAO;gBACP,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE;aACtB,CAAC,CAAC;YAEH,MAAM,WAAW,CAAC,IAAI,EAAE,CAAC;QAC3B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;QACzD,CAAC;IACH,CAAC;IAEO,uBAAuB,CAAC,cAAiC;QAC/D,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;QACjD,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;QAEpE,MAAM,oBAAoB,GAAG,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAC3F,MAAM,mBAAmB,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,WAAW,EAAE,SAAS,EAAE,KAAK,CAAC,CAAC;QAErF,KAAK,MAAM,OAAO,IAAI,oBAAoB,EAAE,CAAC;YAC3C,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7D,OAAO,MAAM,CAAC;YAChB,CAAC;QACH,CAAC;QAED,KAAK,MAAM,OAAO,IAAI,mBAAmB,EAAE,CAAC;YAC1C,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBAC7D,OAAO,KAAK,CAAC;YACf,CAAC;QACH,CAAC;QAED,OAAO,QAAQ,CAAC;IAClB,CAAC;IAEO,sBAAsB,CAAC,cAAiC;QAC9D,IAAI,CAAC;YACH,MAAM,KAAK,GAAG,cAAc,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACjD,MAAM,WAAW,GAAG,cAAc,CAAC,WAAW,EAAE,WAAW,EAAE,IAAI,EAAE,CAAC;YAEpE,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,IAAI,SAAS,GAAG,CAAC,CAAC;YAElB,IAAI,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,IAAI,KAAK,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE,CAAC;gBACnF,SAAS,GAAG,CAAC,CAAC;YAChB,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,UAAU,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE,CAAC;gBAC1E,SAAS,GAAG,CAAC,CAAC;YAChB,CAAC;iBAAM,IAAI,KAAK,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,WAAW,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE,CAAC;gBACpE,SAAS,GAAG,EAAE,CAAC;YACjB,CAAC;YAED,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;YAChC,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,CAAC;YAC7C,OAAO,OAAO,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;CACF,CAAA;AAhPY,8BAAS;oBAAT,SAAS;IADrB,IAAA,mBAAU,GAAE;IAMR,WAAA,IAAA,sBAAW,EAAC,iCAAW,CAAC,IAAI,CAAC,CAAA;qCACJ,gBAAK;QACR,sBAAa;GAP3B,SAAS,CAgPrB"}