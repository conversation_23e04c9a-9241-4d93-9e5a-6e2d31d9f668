"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.PublicEmailSubscriptionSchema = exports.PublicEmailSubscription = void 0;
const mongoose_1 = require("@nestjs/mongoose");
let PublicEmailSubscription = class PublicEmailSubscription {
    email;
    isActive;
    taskReminders;
    dailySummary;
    weeklyReport;
    reminderHours;
    unsubscribeToken;
    lastNotificationSent;
    name;
    emailVerified;
    verificationToken;
};
exports.PublicEmailSubscription = PublicEmailSubscription;
__decorate([
    (0, mongoose_1.Prop)({ required: true, unique: true }),
    __metadata("design:type", String)
], PublicEmailSubscription.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], PublicEmailSubscription.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], PublicEmailSubscription.prototype, "taskReminders", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], PublicEmailSubscription.prototype, "dailySummary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], PublicEmailSubscription.prototype, "weeklyReport", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 24 }),
    __metadata("design:type", Number)
], PublicEmailSubscription.prototype, "reminderHours", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], PublicEmailSubscription.prototype, "unsubscribeToken", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], PublicEmailSubscription.prototype, "lastNotificationSent", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PublicEmailSubscription.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], PublicEmailSubscription.prototype, "emailVerified", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], PublicEmailSubscription.prototype, "verificationToken", void 0);
exports.PublicEmailSubscription = PublicEmailSubscription = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], PublicEmailSubscription);
exports.PublicEmailSubscriptionSchema = mongoose_1.SchemaFactory.createForClass(PublicEmailSubscription);
exports.PublicEmailSubscriptionSchema.index({ email: 1 });
exports.PublicEmailSubscriptionSchema.index({ isActive: 1 });
exports.PublicEmailSubscriptionSchema.index({ unsubscribeToken: 1 });
exports.PublicEmailSubscriptionSchema.index({ verificationToken: 1 });
//# sourceMappingURL=public-email-subscription.schema.js.map