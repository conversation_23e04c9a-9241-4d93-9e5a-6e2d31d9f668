# Environment Variables cho Docker Setup
# Copy file này thành .env và điền các giá trị thực tế

# Google Gemini AI API Key
# Lấy từ: https://ai.google.dev/
GEMINI_API_KEY=your_gemini_api_key_here

# MongoDB Configuration (cho development local)
MONGODB_URI=mongodb://localhost:27017/qltime

# JWT Secret (thay đổi trong production)
JWT_SECRET=your_super_secret_jwt_key_here_change_in_production

# Application Ports
BACKEND_PORT=3001
FRONTEND_PORT=3000
MONGODB_PORT=27017

# MongoDB Root Credentials (cho Docker)
MONGO_INITDB_ROOT_USERNAME=admin
MONGO_INITDB_ROOT_PASSWORD=password123

# Application URLs
NEXT_PUBLIC_API_URL=http://localhost:3001
