"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateTaskDto = void 0;
const class_validator_1 = require("class-validator");
const class_transformer_1 = require("class-transformer");
const swagger_1 = require("@nestjs/swagger");
class CreateTaskDto {
    title;
    description;
    completed;
    dueDate;
    priority;
    category;
    tags;
    status;
    project;
}
exports.CreateTaskDto = CreateTaskDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Tiêu đề công việc',
        example: 'Hoàn thành báo cáo',
    }),
    (0, class_validator_1.IsNotEmpty)({ message: 'Tiêu đề không được để trống' }),
    (0, class_validator_1.IsString)({ message: 'Tiêu đề phải là chuỗi' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "title", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mô tả công việc',
        example: 'Chi tiết về các nội dung cần có trong báo cáo',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsString)({ message: 'Mô tả phải là chuỗi' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "description", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái hoàn thành',
        example: false,
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)({ message: 'Trạng thái hoàn thành phải là boolean' }),
    __metadata("design:type", Boolean)
], CreateTaskDto.prototype, "completed", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Ngày đến hạn',
        example: '2025-06-15T00:00:00.000Z',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsDate)({ message: 'Ngày đến hạn phải là ngày hợp lệ' }),
    (0, class_transformer_1.Type)(() => Date),
    __metadata("design:type", Date)
], CreateTaskDto.prototype, "dueDate", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Mức độ ưu tiên',
        enum: ['low', 'medium', 'high'],
        example: 'medium',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['low', 'medium', 'high'], { message: 'Mức độ ưu tiên không hợp lệ' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "priority", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID danh mục',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)({ message: 'ID danh mục không hợp lệ' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "category", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Danh sách thẻ',
        example: ['công việc', 'báo cáo'],
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsArray)({ message: 'Tags phải là mảng' }),
    (0, class_validator_1.IsString)({ each: true, message: 'Mỗi thẻ phải là chuỗi' }),
    __metadata("design:type", Array)
], CreateTaskDto.prototype, "tags", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'Trạng thái Scrum',
        enum: ['backlog', 'todo', 'doing', 'done'],
        example: 'todo',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsEnum)(['backlog', 'todo', 'doing', 'done'], { message: 'Trạng thái Scrum không hợp lệ' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "status", void 0);
__decorate([
    (0, swagger_1.ApiPropertyOptional)({
        description: 'ID dự án',
        example: '60d21b4667d0d8992e610c85',
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsMongoId)({ message: 'ID dự án không hợp lệ' }),
    __metadata("design:type", String)
], CreateTaskDto.prototype, "project", void 0);
//# sourceMappingURL=create-task.dto.js.map