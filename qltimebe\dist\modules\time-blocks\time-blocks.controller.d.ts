import { TimeBlocksService } from './time-blocks.service';
import { CreateTimeBlockDto } from './dto/create-time-block.dto';
import { UpdateTimeBlockDto } from './dto/update-time-block.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class TimeBlocksController {
    private readonly timeBlocksService;
    constructor(timeBlocksService: TimeBlocksService);
    create(createTimeBlockDto: CreateTimeBlockDto, user: UserDocument): Promise<import("./schemas/time-block.schema").TimeBlock>;
    findAll(user: UserDocument, date?: string): Promise<import("./schemas/time-block.schema").TimeBlock[]>;
    findByDate(date: string, user: UserDocument): Promise<import("./schemas/time-block.schema").TimeBlock[]>;
    findOne(id: string, user: UserDocument): Promise<import("./schemas/time-block.schema").TimeBlock>;
    update(id: string, updateTimeBlockDto: UpdateTimeBlockDto, user: UserDocument): Promise<import("./schemas/time-block.schema").TimeBlock>;
    remove(id: string, user: UserDocument): Promise<{
        success: boolean;
    }>;
    toggleComplete(id: string, user: UserDocument): Promise<import("./schemas/time-block.schema").TimeBlock>;
}
