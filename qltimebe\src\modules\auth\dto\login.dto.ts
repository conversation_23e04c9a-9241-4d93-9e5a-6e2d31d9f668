import { IsEmail, IsNotEmpty } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

export class LoginDto {
  @ApiProperty({
    description: 'Địa chỉ email',
    example: '<EMAIL>',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON> không được để trống' })
  @IsEmail({}, { message: 'Email không hợp lệ' })
  email: string;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> khẩu',
    example: 'password123',
  })
  @IsNotEmpty({ message: '<PERSON><PERSON><PERSON> khẩu không được để trống' })
  password: string;
}
