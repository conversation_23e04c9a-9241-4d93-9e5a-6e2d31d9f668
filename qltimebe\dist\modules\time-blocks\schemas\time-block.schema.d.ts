import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class Time<PERSON>lock extends Document {
    title: string;
    startTime: Date;
    endTime: Date;
    isCompleted: boolean;
    taskId?: MongooseSchema.Types.ObjectId;
    user: MongooseSchema.Types.ObjectId;
}
export declare const TimeBlockSchema: MongooseSchema<TimeBlock, import("mongoose").Model<TimeBlock, any, any, any, Document<unknown, any, TimeBlock, any> & TimeBlock & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, TimeBlock, Document<unknown, {}, import("mongoose").FlatRecord<TimeBlock>, {}> & import("mongoose").FlatRecord<TimeBlock> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;
