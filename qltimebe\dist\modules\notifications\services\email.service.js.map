{"version": 3, "file": "email.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/notifications/services/email.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yCAAyC;AACzC,2CAA+C;AAUxC,IAAM,YAAY,oBAAlB,MAAM,YAAY;IAIH;IAHH,MAAM,GAAG,IAAI,eAAM,CAAC,cAAY,CAAC,IAAI,CAAC,CAAC;IAChD,WAAW,CAAyB;IAE5C,YAAoB,aAA4B;QAA5B,kBAAa,GAAb,aAAa,CAAe;QAC9C,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEO,iBAAiB;QAEvB,IAAI,CAAC,WAAW,GAAG,UAAU,CAAC,eAAe,CAAC;YAC5C,OAAO,EAAE,OAAO;YAChB,IAAI,EAAE;gBACJ,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,sBAAsB;gBACpE,IAAI,EAAE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,mBAAmB;aACtE;SACF,CAAC,CAAC;QAGH,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC,KAAK,EAAE,OAAO,EAAE,EAAE;YACzC,IAAI,KAAK,EAAE,CAAC;gBACV,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE,KAAK,CAAC,CAAC;YAC/D,CAAC;iBAAM,CAAC;gBACN,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAC3D,CAAC;QACH,CAAC,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,OAAqB;QACnC,IAAI,CAAC;YAEH,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAS,YAAY,CAAC,EAAE,CAAC;gBAClD,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,0CAA0C,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;gBACxE,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC;gBAC9D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,2BAA2B,OAAO,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,IAAI,YAAY,KAAK,CAAC,CAAC;gBACjG,OAAO,IAAI,CAAC;YACd,CAAC;YAED,MAAM,WAAW,GAAG;gBAClB,IAAI,EAAE,aAAa,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,YAAY,CAAC,GAAG;gBAC1D,EAAE,EAAE,OAAO,CAAC,EAAE;gBACd,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,IAAI,EAAE,OAAO,CAAC,IAAI;gBAClB,IAAI,EAAE,OAAO,CAAC,IAAI;aACnB,CAAC;YAEF,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;YAC5D,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,8BAA8B,OAAO,CAAC,EAAE,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC,CAAC;YACjF,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,2BAA2B,OAAO,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC,CAAC;YACnE,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,KAAa,EAAE,KAAY;QACrD,MAAM,OAAO,GAAG,sBAAsB,KAAK,CAAC,MAAM,wBAAwB,CAAC;QAE3E,MAAM,IAAI,GAAG;;;4BAGW,KAAK,CAAC,MAAM;;;YAG5B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC;;uDAEyB,IAAI,CAAC,KAAK;;8BAEnC,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC,kBAAkB,CAAC,OAAO,CAAC;;gBAEhE,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,6CAA6C,IAAI,CAAC,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE;;WAEhG,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;qBAIA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;;;;;;qBASjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;KAIjF,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;YACJ,IAAI,EAAE,mBAAmB,KAAK,CAAC,MAAM,oCAAoC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,yBAAyB;SACpK,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,KAAa,EAAE,IAAY;QAChD,MAAM,OAAO,GAAG,+BAA+B,CAAC;QAEhD,MAAM,IAAI,GAAG;;mDAEkC,IAAI;;;;;;;;;;;;;qBAalC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;;;;;;qBASjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;KAIjF,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;YACJ,IAAI,EAAE,aAAa,IAAI,6BAA6B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,cAAc;SACpI,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,sBAAsB,CAAC,KAAa,EAAE,IAAY,EAAE,gBAAwB;QAChF,MAAM,OAAO,GAAG,oDAAoD,CAAC;QAErE,MAAM,IAAI,GAAG;;mDAEkC,IAAI;;;;;;;;;;;;;;;;;;;qBAmBlC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;qBAIjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;;;;;;qBASjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,sBAAsB,gBAAgB;;;;KAIvH,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;YACJ,IAAI,EAAE,aAAa,IAAI,uEAAuE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,eAAe;SAC/K,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,2BAA2B,CAAC,KAAa,EAAE,IAAY,EAAE,KAAY,EAAE,gBAAwB;QACnG,MAAM,OAAO,GAAG,sBAAsB,KAAK,CAAC,MAAM,wBAAwB,CAAC;QAE3E,MAAM,IAAI,GAAG;;;8BAGa,IAAI;4BACN,KAAK,CAAC,MAAM;;;YAG5B,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvC,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;YACpF,MAAM,YAAY,GAAG,SAAS,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,CAAC;YAE1F,OAAO;iDAC8B,YAAY;+DACE,IAAI,CAAC,KAAK;;8BAE3C,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;kBAClI,SAAS,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,SAAS,OAAO,CAAC,CAAC,CAAC,eAAe;;gBAE5D,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,kEAAkE,IAAI,CAAC,WAAW,MAAM,CAAC,CAAC,CAAC,EAAE;gBAChH,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,4BAA4B,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,YAAY,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS;kBACxO,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,MAAM;sBACjF,CAAC,CAAC,CAAC,EAAE;;WAEhB,CAAA;QAAA,CAAC,CAAC,CAAC,IAAI,CAAC,EAAE,CAAC;;;;qBAID,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;qBAIjE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;;;;;;;;;;;;;;qBAejE,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,sBAAsB,gBAAgB;;;;KAIvH,CAAC;QAEF,MAAM,WAAW,GAAG;qBACH,KAAK,CAAC,MAAM;;WAEtB,IAAI;;SAEN,KAAK,CAAC,MAAM;;EAEnB,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;YACjB,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YACvC,OAAO,KAAK,IAAI,CAAC,KAAK,gBAAgB,OAAO,CAAC,kBAAkB,CAAC,OAAO,CAAC,QAAQ,OAAO,CAAC,kBAAkB,CAAC,OAAO,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;QACjK,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC;;WAEJ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB;;eAE7D,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,uBAAuB,sBAAsB,gBAAgB;KACjH,CAAC;QAEF,OAAO,IAAI,CAAC,SAAS,CAAC;YACpB,EAAE,EAAE,KAAK;YACT,OAAO;YACP,IAAI;YACJ,IAAI,EAAE,WAAW,CAAC,IAAI,EAAE;SACzB,CAAC,CAAC;IACL,CAAC;CACF,CAAA;AAhRY,oCAAY;uBAAZ,YAAY;IADxB,IAAA,mBAAU,GAAE;qCAKwB,sBAAa;GAJrC,YAAY,CAgRxB"}