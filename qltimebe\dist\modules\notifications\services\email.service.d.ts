import { ConfigService } from '@nestjs/config';
export interface EmailOptions {
    to: string;
    subject: string;
    html: string;
    text?: string;
}
export declare class EmailService {
    private configService;
    private readonly logger;
    private transporter;
    constructor(configService: ConfigService);
    private createTransporter;
    sendEmail(options: EmailOptions): Promise<boolean>;
    sendTaskReminderEmail(email: string, tasks: any[]): Promise<boolean>;
    sendWelcomeEmail(email: string, name: string): Promise<boolean>;
    sendWelcomeEmailPublic(email: string, name: string, unsubscribeToken: string): Promise<boolean>;
    sendTaskReminderEmailPublic(email: string, name: string, tasks: any[], unsubscribeToken: string): Promise<boolean>;
}
