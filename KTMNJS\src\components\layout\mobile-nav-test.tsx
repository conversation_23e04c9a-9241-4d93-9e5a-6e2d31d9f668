"use client";

import React from 'react';
import { useIsMobile } from '@/lib/hooks/use-mobile';
import { useSidebar } from '@/components/ui/sidebar';

export function MobileNavTest() {
  const isMobile = useIsMobile();
  const { open, setOpen } = useSidebar();

  if (!isMobile) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 bg-primary text-primary-foreground p-2 rounded-lg text-xs">
      <div>Mobile: {isMobile ? 'Yes' : 'No'}</div>
      <div>Sidebar: {open ? 'Open' : 'Closed'}</div>
      <button 
        onClick={() => setOpen(!open)}
        className="mt-1 bg-white text-black px-2 py-1 rounded text-xs"
      >
        Toggle
      </button>
    </div>
  );
}
