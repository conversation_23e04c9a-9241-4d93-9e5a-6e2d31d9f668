import { Model } from 'mongoose';
import { TimeBlock } from './schemas/time-block.schema';
import { CreateTimeBlockDto } from './dto/create-time-block.dto';
import { UpdateTimeBlockDto } from './dto/update-time-block.dto';
export declare class TimeBlocksService {
    private timeBlockModel;
    constructor(timeBlockModel: Model<TimeBlock>);
    create(createTimeBlockDto: CreateTimeBlockDto, userId: string): Promise<TimeBlock>;
    findAll(userId: string, date?: string): Promise<TimeBlock[]>;
    findById(id: string, userId: string): Promise<TimeBlock>;
    update(id: string, updateTimeBlockDto: UpdateTimeBlockDto, userId: string): Promise<TimeBlock>;
    remove(id: string, userId: string): Promise<void>;
    toggleCompletion(id: string, userId: string): Promise<TimeBlock>;
}
