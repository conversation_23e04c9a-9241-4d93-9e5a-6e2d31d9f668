"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmailSubscriptionSchema = exports.EmailSubscription = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let EmailSubscription = class EmailSubscription {
    email;
    user;
    isActive;
    taskReminders;
    dailySummary;
    weeklyReport;
    reminderHours;
    unsubscribeToken;
    lastNotificationSent;
};
exports.EmailSubscription = EmailSubscription;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], EmailSubscription.prototype, "email", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Types.ObjectId, ref: 'User', required: true }),
    __metadata("design:type", mongoose_2.Types.ObjectId)
], EmailSubscription.prototype, "user", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], EmailSubscription.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], EmailSubscription.prototype, "taskReminders", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], EmailSubscription.prototype, "dailySummary", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: false }),
    __metadata("design:type", Boolean)
], EmailSubscription.prototype, "weeklyReport", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: 24 }),
    __metadata("design:type", Number)
], EmailSubscription.prototype, "reminderHours", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], EmailSubscription.prototype, "unsubscribeToken", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Date)
], EmailSubscription.prototype, "lastNotificationSent", void 0);
exports.EmailSubscription = EmailSubscription = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], EmailSubscription);
exports.EmailSubscriptionSchema = mongoose_1.SchemaFactory.createForClass(EmailSubscription);
exports.EmailSubscriptionSchema.index({ email: 1 });
exports.EmailSubscriptionSchema.index({ user: 1 });
exports.EmailSubscriptionSchema.index({ isActive: 1 });
exports.EmailSubscriptionSchema.index({ unsubscribeToken: 1 });
//# sourceMappingURL=email-subscription.schema.js.map