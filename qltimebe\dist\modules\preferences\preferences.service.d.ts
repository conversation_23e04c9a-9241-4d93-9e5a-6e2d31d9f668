import { Model } from 'mongoose';
import { PreferenceDocument } from './schemas/preference.schema';
import { UpdatePreferenceDto } from './dto/update-preference.dto';
export declare class PreferencesService {
    private preferenceModel;
    constructor(preferenceModel: Model<PreferenceDocument>);
    getPreferences(userId: string): Promise<PreferenceDocument>;
    updatePreferences(userId: string, updatePreferenceDto: UpdatePreferenceDto): Promise<PreferenceDocument>;
    private createDefaultPreferences;
}
