version: '3.8'

services:
  # MongoDB Database
  mongodb:
    image: mongo:6.0
    container_name: qltime-mongodb
    restart: unless-stopped
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password123
      MONGO_INITDB_DATABASE: qltime
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
      - ./mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - qltime-network

  # Backend API (NestJS)
  backend:
    build:
      context: ./qltimebe
      dockerfile: Dockerfile
    container_name: qltime-backend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - PORT=3001
      - MONGODB_URI=*****************************************************************
      - JWT_SECRET=your_super_secret_jwt_key_here_change_in_production
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    ports:
      - "3001:3001"
    depends_on:
      - mongodb
    networks:
      - qltime-network
    volumes:
      - ./qltimebe/uploads:/app/uploads

  # Frontend (Next.js)
  frontend:
    build:
      context: ./KTMNJS
      dockerfile: Dockerfile
    container_name: qltime-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:3001
      - NEXT_PUBLIC_GEMINI_API_KEY=${GEMINI_API_KEY}
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - qltime-network

volumes:
  mongodb_data:
    driver: local

networks:
  qltime-network:
    driver: bridge
