{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport Link, { LinkProps } from \"next/link\";\nimport React, { useState, createContext, useContext } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\n\ninterface Links {\n  label: string;\n  href: string;\n  icon: React.JSX.Element | React.ReactNode;\n}\n\ninterface SidebarContextProps {\n  open: boolean;\n  setOpen: React.Dispatch<React.SetStateAction<boolean>>;\n  animate: boolean;\n}\n\nconst SidebarContext = createContext<SidebarContextProps | undefined>(\n  undefined\n);\n\nexport const useSidebar = () => {\n  const context = useContext(SidebarContext);\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider\");\n  }\n  return context;\n};\n\nexport const SidebarProvider = ({\n  children,\n  open: openProp,\n  setOpen: setOpenProp,\n  animate = true,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  const [openState, setOpenState] = useState(false);\n\n  const open = openProp !== undefined ? openProp : openState;\n  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;\n\n  return (\n    <SidebarContext.Provider value={{ open, setOpen, animate }}>\n      {children}\n    </SidebarContext.Provider>\n  );\n};\n\nexport const Sidebar = ({\n  children,\n  open,\n  setOpen,\n  animate,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  return (\n    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>\n      {children}\n    </SidebarProvider>\n  );\n};\n\nexport const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {\n  return (\n    <>\n      <DesktopSidebar {...props} />\n      <MobileSidebar {...(props as React.ComponentProps<\"div\">)} />\n    </>\n  );\n};\n\nexport const DesktopSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof motion.div>) => {\n  const { open, setOpen, animate } = useSidebar();\n  return (\n    <motion.div\n      className={cn(\n        \"h-full px-4 py-4 hidden md:flex md:flex-col bg-neutral-100 dark:bg-neutral-800 w-[300px] flex-shrink-0\",\n        className\n      )}\n      animate={{\n        width: animate ? (open ? \"300px\" : \"60px\") : \"300px\",\n      }}\n      onMouseEnter={() => setOpen(true)}\n      onMouseLeave={() => setOpen(false)}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport const MobileSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\">) => {\n  const { open, setOpen } = useSidebar();\n  return (\n    <>\n      <div\n        className={cn(\n          \"h-10 px-4 py-4 flex flex-row md:hidden items-center justify-between bg-neutral-100 dark:bg-neutral-800 w-full\"\n        )}\n        {...props}\n      >\n        <div className=\"flex justify-end z-20 w-full\">\n          <Menu\n            className=\"text-neutral-800 dark:text-neutral-200 cursor-pointer\"\n            onClick={() => setOpen(!open)}\n          />\n        </div>\n        <AnimatePresence>\n          {open && (\n            <motion.div\n              initial={{ x: \"-100%\", opacity: 0 }}\n              animate={{ x: 0, opacity: 1 }}\n              exit={{ x: \"-100%\", opacity: 0 }}\n              transition={{\n                duration: 0.3,\n                ease: \"easeInOut\",\n              }}\n              className={cn(\n                \"fixed h-full w-full inset-0 bg-white dark:bg-neutral-900 p-10 z-[100] flex flex-col justify-between\",\n                className\n              )}\n            >\n              <div\n                className=\"absolute right-10 top-10 z-50 text-neutral-800 dark:text-neutral-200 cursor-pointer\"\n                onClick={() => setOpen(!open)}\n              >\n                <X />\n              </div>\n              {children}\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </>\n  );\n};\n\nexport const SidebarLink = ({\n  link,\n  className,\n  ...props\n}: {\n  link: Links;\n  className?: string;\n  props?: LinkProps;\n}) => {\n  const { open, animate } = useSidebar();\n  return (\n    <Link\n      href={link.href}\n      className={cn(\n        \"flex items-center justify-start gap-2 group/sidebar py-2\",\n        className\n      )}\n      {...props}\n    >\n      {link.icon}\n      <motion.span\n        animate={{\n          display: animate ? (open ? \"inline-block\" : \"none\") : \"inline-block\",\n          opacity: animate ? (open ? 1 : 0) : 1,\n        }}\n        className=\"text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0\"\n      >\n        {link.label}\n      </motion.span>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAoBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EACjC;AAGK,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,MAAM,QAAQ,EACd,SAAS,WAAW,EACpB,UAAU,IAAI,EAMf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,aAAa,YAAY,WAAW;IACjD,MAAM,UAAU,gBAAgB,YAAY,cAAc;IAE1D,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;QAAQ;kBACtD;;;;;;AAGP;AAEO,MAAM,UAAU,CAAC,EACtB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EAMR;IACC,qBACE,8OAAC;QAAgB,MAAM;QAAM,SAAS;QAAS,SAAS;kBACrD;;;;;;AAGP;AAEO,MAAM,cAAc,CAAC;IAC1B,qBACE;;0BACE,8OAAC;gBAAgB,GAAG,KAAK;;;;;;0BACzB,8OAAC;gBAAe,GAAI,KAAK;;;;;;;;AAG/B;AAEO,MAAM,iBAAiB,CAAC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACqC;IACxC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAEF,SAAS;YACP,OAAO,UAAW,OAAO,UAAU,SAAU;QAC/C;QACA,cAAc,IAAM,QAAQ;QAC5B,cAAc,IAAM,QAAQ;QAC3B,GAAG,KAAK;kBAER;;;;;;AAGP;AAEO,MAAM,gBAAgB,CAAC,EAC5B,SAAS,EACT,QAAQ,EACR,GAAG,OACyB;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,qBACE;kBACE,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,SAAS,IAAM,QAAQ,CAAC;;;;;;;;;;;8BAG5B,8OAAC,yLAAA,CAAA,kBAAe;8BACb,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,GAAG;4BAAS,SAAS;wBAAE;wBAClC,SAAS;4BAAE,GAAG;4BAAG,SAAS;wBAAE;wBAC5B,MAAM;4BAAE,GAAG;4BAAS,SAAS;wBAAE;wBAC/B,YAAY;4BACV,UAAU;4BACV,MAAM;wBACR;wBACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uGACA;;0CAGF,8OAAC;gCACC,WAAU;gCACV,SAAS,IAAM,QAAQ,CAAC;0CAExB,cAAA,8OAAC,4LAAA,CAAA,IAAC;;;;;;;;;;4BAEH;;;;;;;;;;;;;;;;;;;AAOf;AAEO,MAAM,cAAc,CAAC,EAC1B,IAAI,EACJ,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM,KAAK,IAAI;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;YAER,KAAK,IAAI;0BACV,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBACP,SAAS,UAAW,OAAO,iBAAiB,SAAU;oBACtD,SAAS,UAAW,OAAO,IAAI,IAAK;gBACtC;gBACA,WAAU;0BAET,KAAK,KAAK;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/SidebarMenu.tsx"], "sourcesContent": ["import React from \"react\";\nimport { SidebarBody, SidebarLink } from \"@/components/ui/sidebar\";\nimport { Home, List, Calendar, BarChart2, Folder, Tag } from \"lucide-react\";\n\nconst links = [\n  {\n    label: \"Trang chủ\",\n    href: \"/\",\n    icon: <Home className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Công việc\",\n    href: \"/tasks\",\n    icon: <List className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Thống kê\",\n    href: \"/statistics\",\n    icon: <BarChart2 className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Lịch\",\n    href: \"/calendar\",\n    icon: <Calendar className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Dự án\",\n    href: \"/projects\",\n    icon: <Folder className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Danh mục\",\n    href: \"/categories\",\n    icon: <Tag className=\"h-5 w-5\" />,\n  },\n];\n\nexport default function SidebarMenu() {\n  return (\n    <SidebarBody>\n      <div className=\"flex flex-col gap-1\">\n        {links.map((link) => (\n          <SidebarLink key={link.href} link={link} />\n        ))}\n      </div>\n    </SidebarBody>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,mMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,gOAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC7B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC1B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IACvB;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,mIAAA,CAAA,cAAW;oBAAiB,MAAM;mBAAjB,KAAK,IAAI;;;;;;;;;;;;;;;AAKrC", "debugId": null}}, {"offset": {"line": 339, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/preference-service.ts"], "sourcesContent": ["import { UserPreferences, Preference } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Gi<PERSON> trị mặc định cho tùy chọn người dùng\r\nconst DEFAULT_PREFERENCES: UserPreferences = {\r\n  theme: 'system',\r\n  language: 'vi',\r\n  startOfWeek: 'monday',\r\n  showCompletedTasks: true,\r\n  notifications: true,\r\n  soundEnabled: true,\r\n};\r\n\r\n// Cache cho preferences\r\nlet preferencesCache: UserPreferences | null = null;\r\n\r\n// Helper function to convert Preference to UserPreferences\r\nconst mapPreferenceToUserPreferences = (preference: Preference): UserPreferences => {\r\n  return {\r\n    theme: preference.theme,\r\n    language: (preference.language === 'en' ? 'en' : 'vi') as 'vi' | 'en',\r\n    startOfWeek: preference.startOfWeek === 1 ? 'monday' : 'sunday',\r\n    showCompletedTasks: true, // Default value\r\n    notifications: preference.notifications,\r\n    soundEnabled: true, // Default value\r\n  };\r\n};\r\n\r\n// Helper function to convert UserPreferences to Preference updates\r\nconst mapUserPreferencesToPreference = (userPrefs: Partial<UserPreferences>): Partial<Omit<Preference, 'id' | 'userId'>> => {\r\n  const updates: any = {};\r\n\r\n  if (userPrefs.theme) updates.theme = userPrefs.theme;\r\n  if (userPrefs.language) updates.language = userPrefs.language;\r\n  if (userPrefs.startOfWeek) updates.startOfWeek = userPrefs.startOfWeek === 'monday' ? 1 : 0;\r\n  if (userPrefs.notifications !== undefined) updates.notifications = userPrefs.notifications;\r\n\r\n  return updates;\r\n};\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const PreferenceService = {\r\n  getPreferences: async (): Promise<UserPreferences> => {\r\n    const now = Date.now();\r\n    \r\n    // Nếu có cache và chưa hết hạn, trả về cache\r\n    if (preferencesCache && now - lastFetchTime < CACHE_DURATION) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    try {\r\n      // Gọi API để lấy preferences\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = now;\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi lấy tùy chọn người dùng:', error);\r\n      \r\n      // Nếu có lỗi và đã có cache, trả về cache\r\n      if (preferencesCache) {\r\n        return preferencesCache;\r\n      }\r\n      \r\n      // Nếu không có cache, trả về giá trị mặc định\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Phương thức đồng bộ để lấy preferences từ cache (cho các component không thể đợi async)\r\n  getPreferencesSync: (): UserPreferences => {\r\n    if (preferencesCache) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    // Nếu chưa có cache, trả về giá trị mặc định\r\n    return DEFAULT_PREFERENCES;\r\n  },\r\n  \r\n  updatePreferences: async (updates: Partial<UserPreferences>): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert UserPreferences updates to Preference format\r\n      const preferenceUpdates = mapUserPreferencesToPreference(updates);\r\n\r\n      // Gọi API để cập nhật preferences\r\n      const updatedPreference = await ApiService.preferences.update(preferenceUpdates);\r\n      const updatedUserPreferences = mapPreferenceToUserPreferences(updatedPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = updatedUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return updatedUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi cập nhật tùy chọn người dùng:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  resetPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert default preferences to Preference format\r\n      const defaultPreferenceUpdates = mapUserPreferencesToPreference(DEFAULT_PREFERENCES);\r\n\r\n      // Gọi API để đặt lại preferences về mặc định\r\n      const resetPreference = await ApiService.preferences.update(defaultPreferenceUpdates);\r\n      const resetUserPreferences = mapPreferenceToUserPreferences(resetPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = resetUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return resetUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi đặt lại tùy chọn người dùng:', error);\r\n\r\n      // Nếu có lỗi, đặt cache về mặc định\r\n      preferencesCache = DEFAULT_PREFERENCES;\r\n      lastFetchTime = Date.now();\r\n\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    preferencesCache = null;\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = Date.now();\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới tùy chọn người dùng:', error);\r\n      return preferencesCache || DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,2CAA2C;AAC3C,MAAM,sBAAuC;IAC3C,OAAO;IACP,UAAU;IACV,aAAa;IACb,oBAAoB;IACpB,eAAe;IACf,cAAc;AAChB;AAEA,wBAAwB;AACxB,IAAI,mBAA2C;AAE/C,2DAA2D;AAC3D,MAAM,iCAAiC,CAAC;IACtC,OAAO;QACL,OAAO,WAAW,KAAK;QACvB,UAAW,WAAW,QAAQ,KAAK,OAAO,OAAO;QACjD,aAAa,WAAW,WAAW,KAAK,IAAI,WAAW;QACvD,oBAAoB;QACpB,eAAe,WAAW,aAAa;QACvC,cAAc;IAChB;AACF;AAEA,mEAAmE;AACnE,MAAM,iCAAiC,CAAC;IACtC,MAAM,UAAe,CAAC;IAEtB,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,QAAQ,EAAE,QAAQ,QAAQ,GAAG,UAAU,QAAQ;IAC7D,IAAI,UAAU,WAAW,EAAE,QAAQ,WAAW,GAAG,UAAU,WAAW,KAAK,WAAW,IAAI;IAC1F,IAAI,UAAU,aAAa,KAAK,WAAW,QAAQ,aAAa,GAAG,UAAU,aAAa;IAE1F,OAAO;AACT;AACA,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,oBAAoB;IAC/B,gBAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,6CAA6C;QAC7C,IAAI,oBAAoB,MAAM,gBAAgB,gBAAgB;YAC5D,OAAO;QACT;QAEA,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB;YAChB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,0CAA0C;YAC1C,IAAI,kBAAkB;gBACpB,OAAO;YACT;YAEA,8CAA8C;YAC9C,OAAO;QACT;IACF;IAEA,0FAA0F;IAC1F,oBAAoB;QAClB,IAAI,kBAAkB;YACpB,OAAO;QACT;QAEA,6CAA6C;QAC7C,OAAO;IACT;IAEA,mBAAmB,OAAO;QACxB,IAAI;YACF,uDAAuD;YACvD,MAAM,oBAAoB,+BAA+B;YAEzD,kCAAkC;YAClC,MAAM,oBAAoB,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9D,MAAM,yBAAyB,+BAA+B;YAE9D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;QAChB,IAAI;YACF,mDAAmD;YACnD,MAAM,2BAA2B,+BAA+B;YAEhE,6CAA6C;YAC7C,MAAM,kBAAkB,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC5D,MAAM,uBAAuB,+BAA+B;YAE5D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,oCAAoC;YACpC,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,YAAY;QACV,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,oBAAoB;QAClB,IAAI;YACF,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,oBAAoB;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 529, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/clear-data-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { PreferenceService } from \"@/lib/services/preference-service\";\nimport { Trash2 } from \"lucide-react\";\nimport { useSidebar } from \"@/components/ui/sidebar\";\nimport { motion } from \"framer-motion\";\n\nexport function ClearDataButton() {\n  const { open, animate } = useSidebar();\n  \n  const handleClearData = () => {\n    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu? Hành động này không thể hoàn tác.')) {\n      // Clear localStorage\n      localStorage.clear();\n      // Clear preferences cache\n      PreferenceService.clearCache();\n      // Reload page\n      window.location.reload();\n    }\n  };\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={handleClearData}\n      className=\"w-full flex items-center gap-2 justify-start px-2 text-destructive\"\n    >\n      <Trash2 className=\"h-5 w-5\" />\n      <motion.span\n        initial={{ opacity: 0, width: 0 }}\n        animate={{\n          opacity: animate ? (open ? 1 : 0) : 1,\n          width: animate ? (open ? \"auto\" : 0) : \"auto\",\n          marginLeft: animate ? (open ? \"0.5rem\" : 0) : \"0.5rem\",\n          display: \"inline-block\"\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"whitespace-pre overflow-hidden\"\n      >\n        Xóa dữ liệu\n      </motion.span>\n    </Button>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,gFAAgF;YACjG,qBAAqB;YACrB,aAAa,KAAK;YAClB,0BAA0B;YAC1B,+IAAA,CAAA,oBAAiB,CAAC,UAAU;YAC5B,cAAc;YACd,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,UAAW,OAAO,IAAI,IAAK;oBACpC,OAAO,UAAW,OAAO,SAAS,IAAK;oBACvC,YAAY,UAAW,OAAO,WAAW,IAAK;oBAC9C,SAAS;gBACX;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 604, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 633, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 669, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 733, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/footer-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  <PERSON><PERSON><PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Github, Send, CheckCircle, AlertCircle } from \"lucide-react\"\nimport { ApiService } from \"@/lib/services/api-service\"\n\nfunction Footerdemo() {\n  const [email, setEmail] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)\n\n  const handleEmailSubscription = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!email) {\n      setMessage({ type: 'error', text: '<PERSON><PERSON> lòng nhập địa chỉ email' })\n      return\n    }\n\n    // <PERSON><PERSON><PERSON> tra định dạng email\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(email)) {\n      setMessage({ type: 'error', text: 'Địa chỉ email không hợp lệ' })\n      return\n    }\n\n    setIsLoading(true)\n    setMessage(null)\n\n    try {\n      // Sử dụng API public - không cần đăng nhập\n      await ApiService.notifications.subscribeEmailPublic({\n        email: email,\n        name: '', // Có thể để trống hoặc thêm field name nếu muốn\n        taskReminders: true,\n        dailySummary: false,\n        weeklyReport: false,\n        reminderHours: 24,\n      })\n\n      setMessage({\n        type: 'success',\n        text: 'Đăng ký thành công! Bạn sẽ nhận được thông báo qua email. Kiểm tra hộp thư để xác nhận.'\n      })\n      setEmail('')\n    } catch (error: any) {\n      console.error('Lỗi đăng ký email:', error)\n      setMessage({\n        type: 'error',\n        text: error.message || 'Đăng ký thất bại. Vui lòng thử lại.'\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <footer className=\"relative border-t bg-background/80 backdrop-blur-sm text-foreground transition-colors duration-300\">\n      <div className=\"container mx-auto px-4 py-12 md:px-6 lg:px-8\">\n        <div className=\"grid gap-12 md:grid-cols-2 lg:grid-cols-4\">\n          <div className=\"relative\">\n            <h2 className=\"mb-4 text-3xl font-bold tracking-tight\">QLTime</h2>\n            <p className=\"mb-6 text-muted-foreground\">\n              Giải pháp quản lý thời gian thông minh cho cuộc sống hiện đại.\n            </p>\n            <form onSubmit={handleEmailSubscription} className=\"relative\">\n              <Input\n                type=\"email\"\n                placeholder=\"Đăng ký nhận thông báo\"\n                className=\"pr-12 backdrop-blur-sm\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                disabled={isLoading}\n              />\n              <Button\n                type=\"submit\"\n                size=\"icon\"\n                className=\"absolute right-1 top-1 h-8 w-8 rounded-full bg-primary text-primary-foreground transition-transform hover:scale-105 disabled:opacity-50\"\n                disabled={isLoading}\n              >\n                {isLoading ? (\n                  <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\" />\n                ) : (\n                  <Send className=\"h-4 w-4\" />\n                )}\n                <span className=\"sr-only\">Đăng ký</span>\n              </Button>\n            </form>\n\n            {message && (\n              <Alert className={`mt-3 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>\n                <div className=\"flex items-center gap-2\">\n                  {message.type === 'success' ? (\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <AlertCircle className=\"h-4 w-4 text-red-600\" />\n                  )}\n                  <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>\n                    {message.text}\n                  </AlertDescription>\n                </div>\n              </Alert>\n            )}\n            <div className=\"absolute -right-4 top-0 h-24 w-24 rounded-full bg-primary/10 blur-2xl\" />\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-lg font-semibold\">Tính năng</h3>\n            <nav className=\"space-y-2 text-sm\">\n              <a href=\"/tasks\" className=\"block transition-colors hover:text-primary\">\n                Quản lý công việc\n              </a>\n              <a href=\"/calendar\" className=\"block transition-colors hover:text-primary\">\n                Lịch & Timeblocks\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Thống kê\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Tùy chỉnh\n              </a>\n            </nav>\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-lg font-semibold\">Liên hệ</h3>\n            <address className=\"space-y-2 text-sm not-italic\">\n              <p>Email: <EMAIL></p>\n              <p>SĐT: **********</p>\n            </address>\n          </div>\n          <div className=\"relative\">\n            <h3 className=\"mb-4 text-lg font-semibold\">Theo dõi</h3>\n            <div className=\"mb-6 flex space-x-4\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button \n                      variant=\"outline\" \n                      size=\"icon\" \n                      className=\"rounded-full\"\n                      asChild\n                    >\n                      <a href=\"https://github.com/hungtvu113/WebsiteTimE\" target=\"_blank\" rel=\"noopener noreferrer\">\n                        <Github className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Github</span>\n                      </a>\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Xem mã nguồn trên Github</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-12 flex flex-col items-center justify-between gap-4 border-t pt-8 text-center md:flex-row\">\n          <p className=\"text-sm text-muted-foreground\">\n            © 2024 QLTime. Bản quyền thuộc về nhóm phát triển.\n          </p>\n          <nav className=\"flex gap-4 text-sm\">\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Chính sách bảo mật\n            </a>\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Điều khoản sử dụng\n            </a>\n          </nav>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport { Footerdemo }"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;AAfA;;;;;;;;;AAiBA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAE3F,MAAM,0BAA0B,OAAO;QACrC,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA8B;YAChE;QACF;QAEA,2BAA2B;QAC3B,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA6B;YAC/D;QACF;QAEA,aAAa;QACb,WAAW;QAEX,IAAI;YACF,2CAA2C;YAC3C,MAAM,wIAAA,CAAA,aAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBAClD,OAAO;gBACP,MAAM;gBACN,eAAe;gBACf,cAAc;gBACd,cAAc;gBACd,eAAe;YACjB;YAEA,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,SAAS;QACX,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW;gBACT,MAAM;gBACN,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAK,UAAU;oCAAyB,WAAU;;sDACjD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,UAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;;gDAET,0BACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAElB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI7B,yBACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,YAAY,iCAAiC,4BAA4B;8CAClH,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,KAAK,0BAChB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,8OAAC,iIAAA,CAAA,mBAAgB;gDAAC,WAAW,QAAQ,IAAI,KAAK,YAAY,mBAAmB;0DAC1E,QAAQ,IAAI;;;;;;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAS,WAAU;sDAA6C;;;;;;sDAGxE,8OAAC;4CAAE,MAAK;4CAAY,WAAU;sDAA6C;;;;;;sDAG3E,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;sDAGnE,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAKvE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,OAAO;kEAEP,cAAA,8OAAC;4DAAE,MAAK;4DAA4C,QAAO;4DAAS,KAAI;;8EACtE,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;8DAIhC,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;8CAG7D,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE", "debugId": null}}, {"offset": {"line": 1200, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1398, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1449, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { LogOut, User, Setting<PERSON>, BarChart3, UserCircle } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ApiService } from \"@/lib/services/api-service\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\nexport function Header() {\r\n  const router = useRouter();\r\n  const [user, setUser] = React.useState<any>(null);\r\n\r\n  React.useEffect(() => {\r\n    const fetchUser = async () => {\r\n      try {\r\n        const userData = await ApiService.auth.getCurrentUser();\r\n        setUser(userData);\r\n      } catch (error) {\r\n        console.error('Lỗi lấy thông tin user:', error);\r\n      }\r\n    };\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n\r\n\r\n  const handleLogout = () => {\r\n    ApiService.auth.logout();\r\n    router.push('/login');\r\n  };\r\n\r\n  const handleProfile = () => {\r\n    router.push('/profile');\r\n  };\r\n\r\n  const handleSettings = () => {\r\n    router.push('/settings');\r\n  };\r\n\r\n  const handleStatistics = () => {\r\n    router.push('/statistics');\r\n  };\r\n\r\n  // Tạo initials từ tên user\r\n  const getInitials = (name: string) => {\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-10 backdrop-blur-sm bg-background/80 border-b\">\r\n      <div className=\"container flex h-14 items-center justify-between\">\r\n        <div className=\"mr-4 hidden md:flex\">\r\n          {/* Có thể thêm các phần tử khác ở đây nếu cần */}\r\n        </div>\r\n        \r\n        <div className=\"flex items-center gap-2\">\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 w-8 rounded-full hover:bg-background/80 transition-colors\"\r\n              >\r\n                <Avatar className=\"h-8 w-8\">\r\n                  <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />\r\n                  <AvatarFallback className=\"bg-primary text-primary-foreground\">\r\n                    {user?.name ? getInitials(user.name) : <UserCircle className=\"h-4 w-4\" />}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent\r\n              className=\"w-56 backdrop-blur-sm border border-border/40 bg-background/90 shadow-lg animate-in fade-in-0 zoom-in-95\"\r\n              align=\"end\"\r\n              forceMount\r\n              sideOffset={5}\r\n            >\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-medium leading-none\">\r\n                    {user?.name || 'Người dùng'}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || '<EMAIL>'}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleProfile}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <User className=\"mr-2 h-4 w-4\" />\r\n                <span>Hồ sơ cá nhân</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleStatistics}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <BarChart3 className=\"mr-2 h-4 w-4\" />\r\n                <span>Thống kê</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleSettings}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                <span>Cài đặt</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleLogout}\r\n                className=\"cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors\"\r\n              >\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span>Đăng xuất</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAOA;AAQA;AArBA;;;;;;;;;AAuBO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAO;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,cAAc;gBACrD,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QAEA;IACF,GAAG,EAAE;IAIL,MAAM,eAAe;QACnB,wIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,MAAM;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;;;;;8BAIf,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,MAAM;gDAAQ,KAAK,MAAM,QAAQ;;;;;;0DACnD,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,MAAM,OAAO,YAAY,KAAK,IAAI,kBAAI,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrE,8OAAC,4IAAA,CAAA,sBAAmB;gCAClB,WAAU;gCACV,OAAM;gCACN,UAAU;gCACV,YAAY;;kDAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;8DAEjB,8OAAC;oDAAE,WAAU;8DACV,MAAM,SAAS;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 1745, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/ai-service.ts"], "sourcesContent": ["import { GoogleGenerativeAI } from \"@google/generative-ai\";\r\nimport { Task } from '../types';\r\n\r\n// Khởi tạo Gemini AI\r\nlet genAI: GoogleGenerativeAI | null = null;\r\nlet model: any = null;\r\n\r\n// Lấy API key từ API route\r\nconst getApiKeyFromServer = async (): Promise<string | null> => {\r\n  try {\r\n    const response = await fetch('/api/ai/api-key');\r\n    if (!response.ok) {\r\n      console.error('Failed to get API key from server');\r\n      return null;\r\n    }\r\n    const data = await response.json();\r\n    return data.apiKey || null;\r\n  } catch (error) {\r\n    console.error('Error fetching API key:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Khởi tạo AI service với API key từ server\r\nconst initializeAI = async () => {\r\n  if (model) return true; // Đã khởi tạo rồi\r\n\r\n  const apiKey = await getApiKeyFromServer();\r\n  if (apiKey && !genAI) {\r\n    genAI = new GoogleGenerativeAI(apiKey);\r\n    model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n  }\r\n  return !!model;\r\n};\r\n\r\n// Chat history interface\r\nexport interface ChatMessage {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport const AIService = {\r\n  // Phân tích nội dung công việc và đề xuất độ ưu tiên bằng AI\r\n  suggestPriority: async (title: string, description?: string): Promise<'high' | 'medium' | 'low'> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        // Fallback to keyword-based logic if AI not available\r\n        return AIService.fallbackSuggestPriority(title, description);\r\n      }\r\n\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất độ ưu tiên (high/medium/low):\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\n\r\nHãy trả về chỉ một từ: \"high\", \"medium\", hoặc \"low\" dựa trên:\r\n- Tính khẩn cấp của công việc\r\n- Tầm quan trọng\r\n- Deadline ngầm định\r\n- Từ khóa chỉ độ ưu tiên\r\n\r\nChỉ trả về một từ, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().toLowerCase().trim();\r\n\r\n      if (['high', 'medium', 'low'].includes(response)) {\r\n        return response as 'high' | 'medium' | 'low';\r\n      }\r\n\r\n      return 'medium';\r\n    } catch (error) {\r\n      console.error(\"Error suggesting priority with AI:\", error);\r\n      return AIService.fallbackSuggestPriority(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for priority suggestion\r\n  fallbackSuggestPriority: (title: string, description?: string): 'high' | 'medium' | 'low' => {\r\n    if (!title) return 'medium';\r\n\r\n    const lowerTitle = title.toLowerCase();\r\n    const lowerDesc = description?.toLowerCase() || '';\r\n\r\n    const highPriorityKeywords = ['gấp', 'khẩn', 'ngay', 'quan trọng', 'deadline', 'hạn chót'];\r\n    const lowPriorityKeywords = ['nhẹ nhàng', 'khi rảnh', 'không gấp', 'sau này', 'phụ'];\r\n\r\n    for (const keyword of highPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'high';\r\n      }\r\n    }\r\n\r\n    for (const keyword of lowPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'low';\r\n      }\r\n    }\r\n\r\n    return 'medium';\r\n  },\r\n  \r\n  // Đề xuất thời gian hoàn thành dựa trên nội dung bằng AI\r\n  suggestDueDate: async (title: string, description?: string): Promise<string | null> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const today = new Date();\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất số ngày cần để hoàn thành:\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\nNgày hiện tại: ${today.toLocaleDateString('vi-VN')}\r\n\r\nHãy trả về chỉ một số nguyên (1-365) đại diện cho số ngày cần để hoàn thành công việc này.\r\nXem xét:\r\n- Độ phức tạp của công việc\r\n- Thời gian thông thường cần thiết\r\n- Từ khóa về thời gian (gấp, khẩn, tuần này, tháng này, etc.)\r\n\r\nChỉ trả về số nguyên, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n      const daysToAdd = parseInt(response);\r\n\r\n      if (isNaN(daysToAdd) || daysToAdd < 1 || daysToAdd > 365) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error suggesting due date with AI:\", error);\r\n      return AIService.fallbackSuggestDueDate(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for due date suggestion\r\n  fallbackSuggestDueDate: (title: string, description?: string): string | null => {\r\n    try {\r\n      const lowerTitle = title.toLowerCase();\r\n      const lowerDesc = description?.toLowerCase() || '';\r\n\r\n      const today = new Date();\r\n      let daysToAdd = 7;\r\n\r\n      if (lowerTitle.includes('gấp') || lowerTitle.includes('khẩn') || lowerDesc.includes('gấp')) {\r\n        daysToAdd = 1;\r\n      } else if (lowerTitle.includes('tuần này') || lowerDesc.includes('tuần này')) {\r\n        daysToAdd = 5;\r\n      } else if (lowerTitle.includes('tháng') || lowerDesc.includes('tháng')) {\r\n        daysToAdd = 30;\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error in fallback due date suggestion:\", error);\r\n      return null;\r\n    }\r\n  },\r\n  \r\n  // Tóm tắt danh sách công việc bằng AI\r\n  summarizeTasks: async (tasks: Task[]): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSummarizeTasks(tasks);\r\n      }\r\n\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const pending = tasks.length - completed;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      // Tạo context cho AI\r\n      const taskSummary = tasks.map(task =>\r\n        `- ${task.title} (${task.priority} priority, ${task.completed ? 'completed' : 'pending'})`\r\n      ).join('\\n');\r\n\r\n      const prompt = `\r\nPhân tích danh sách công việc sau và tạo một bản tóm tắt thông minh bằng tiếng Việt với Markdown format:\r\n\r\n## Thống kê hiện tại:\r\n- **Tổng số công việc**: ${tasks.length}\r\n- **Đã hoàn thành**: ${completed}\r\n- **Chưa hoàn thành**: ${pending}\r\n- **Tỷ lệ hoàn thành**: ${completionRate}%\r\n- **Công việc ưu tiên cao chưa hoàn thành**: ${highPriorityPending}\r\n\r\n## Danh sách công việc:\r\n${taskSummary || 'Không có công việc nào'}\r\n\r\nHãy tạo một bản tóm tắt với Markdown format bao gồm:\r\n1. **Đánh giá hiện trạng** - sử dụng emoji và bold\r\n2. **Lời khuyên hoặc động viên** - sử dụng blockquote\r\n3. **Gợi ý hành động tiếp theo** - sử dụng danh sách có dấu đầu dòng\r\n\r\nSử dụng:\r\n- **Bold** cho các điểm quan trọng\r\n- > Blockquote cho lời khuyên\r\n- • Bullet points cho các bước hành động\r\n- 📊 📈 ✅ ⚡ 🎯 emoji phù hợp\r\n\r\nGiữ giọng điệu thân thiện và tích cực.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n\r\n      return response || AIService.fallbackSummarizeTasks(tasks);\r\n    } catch (error) {\r\n      console.error(\"Error summarizing tasks with AI:\", error);\r\n      return AIService.fallbackSummarizeTasks(tasks);\r\n    }\r\n  },\r\n\r\n  // Fallback method for task summarization\r\n  fallbackSummarizeTasks: (tasks: Task[]): string => {\r\n    try {\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      let summary = `Bạn đã hoàn thành ${completed}/${tasks.length} công việc (${completionRate}%). `;\r\n\r\n      if (highPriorityPending > 0) {\r\n        summary += `Hiện có ${highPriorityPending} công việc ưu tiên cao cần hoàn thành. `;\r\n      }\r\n\r\n      if (completionRate >= 80) {\r\n        summary += 'Bạn đang làm rất tốt! Hãy tiếp tục phát huy.';\r\n      } else if (completionRate >= 50) {\r\n        summary += 'Bạn đang tiến triển tốt. Cố gắng hoàn thành các công việc còn lại nhé.';\r\n      } else if (tasks.length > 0) {\r\n        summary += 'Bạn cần tập trung hơn để hoàn thành nhiều công việc hơn.';\r\n      } else {\r\n        summary = 'Bạn chưa có công việc nào. Hãy thêm một số công việc để bắt đầu.';\r\n      }\r\n\r\n      return summary;\r\n    } catch (error) {\r\n      console.error(\"Error in fallback task summarization:\", error);\r\n      return \"Không thể tạo bản tóm tắt tại thời điểm này.\";\r\n    }\r\n  },\r\n  \r\n  // Chat với AI (chỉ sử dụng client-side)\r\n  chat: async (message: string, chatHistory: ChatMessage[] = []): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n      }\r\n\r\n      // Tạo context từ lịch sử chat\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      return result.response.text().trim();\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat:\", error);\r\n      return \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Chat streaming với Gemini\r\n  chatStream: async function* (message: string, chatHistory: ChatMessage[] = []): AsyncGenerator<string, void, unknown> {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        yield \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n        return;\r\n      }\r\n\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContentStream(prompt);\r\n\r\n      for await (const chunk of result.stream) {\r\n        const chunkText = chunk.text();\r\n        if (chunkText) {\r\n          yield chunkText;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat stream:\", error);\r\n      yield \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Kích hoạt API Gemini khi có API key\r\n  initWithApiKey: (apiKey: string) => {\r\n    try {\r\n      genAI = new GoogleGenerativeAI(apiKey);\r\n      model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n      console.log(\"AI service initialized with API key\");\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error initializing AI service:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  // Kiểm tra trạng thái AI\r\n  isAvailable: (): boolean => {\r\n    return !!model;\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAGA,qBAAqB;AACrB,IAAI,QAAmC;AACvC,IAAI,QAAa;AAEjB,2BAA2B;AAC3B,MAAM,sBAAsB;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,MAAM,IAAI;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA,4CAA4C;AAC5C,MAAM,eAAe;IACnB,IAAI,OAAO,OAAO,MAAM,kBAAkB;IAE1C,MAAM,SAAS,MAAM;IACrB,IAAI,UAAU,CAAC,OAAO;QACpB,QAAQ,IAAI,8JAAA,CAAA,qBAAkB,CAAC;QAC/B,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAiC;IAC7E;IACA,OAAO,CAAC,CAAC;AACX;AASO,MAAM,YAAY;IACvB,6DAA6D;IAC7D,iBAAiB,OAAO,OAAe;QACrC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,sDAAsD;gBACtD,OAAO,UAAU,uBAAuB,CAAC,OAAO;YAClD;YAEA,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;;;;;;;;oCAQL,CAAC;YAE/B,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,WAAW,GAAG,IAAI;YAE1D,IAAI;gBAAC;gBAAQ;gBAAU;aAAM,CAAC,QAAQ,CAAC,WAAW;gBAChD,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,uBAAuB,CAAC,OAAO;QAClD;IACF;IAEA,0CAA0C;IAC1C,yBAAyB,CAAC,OAAe;QACvC,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,YAAY,aAAa,iBAAiB;QAEhD,MAAM,uBAAuB;YAAC;YAAO;YAAQ;YAAQ;YAAc;YAAY;SAAW;QAC1F,MAAM,sBAAsB;YAAC;YAAa;YAAY;YAAa;YAAW;SAAM;QAEpF,KAAK,MAAM,WAAW,qBAAsB;YAC1C,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,KAAK,MAAM,WAAW,oBAAqB;YACzC,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,yDAAyD;IACzD,gBAAgB,OAAO,OAAe;QACpC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,QAAQ,IAAI;YAClB,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;eAC1B,EAAE,MAAM,kBAAkB,CAAC,SAAS;;;;;;;;uCAQZ,CAAC;YAElC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAC5C,MAAM,YAAY,SAAS;YAE3B,IAAI,MAAM,cAAc,YAAY,KAAK,YAAY,KAAK;gBACxD,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,sBAAsB,CAAC,OAAO;QACjD;IACF;IAEA,0CAA0C;IAC1C,wBAAwB,CAAC,OAAe;QACtC,IAAI;YACF,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,YAAY,aAAa,iBAAiB;YAEhD,MAAM,QAAQ,IAAI;YAClB,IAAI,YAAY;YAEhB,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,QAAQ;gBAC1F,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,aAAa;gBAC5E,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBACtE,YAAY;YACd;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC;YAC1C;YAEA,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,UAAU,MAAM,MAAM,GAAG;YAC/B,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,qBAAqB;YACrB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAC5B,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,SAAS,GAAG,cAAc,UAAU,CAAC,CAAC,EAC1F,IAAI,CAAC;YAEP,MAAM,SAAS,CAAC;;;;yBAIG,EAAE,MAAM,MAAM,CAAC;qBACnB,EAAE,UAAU;uBACV,EAAE,QAAQ;wBACT,EAAE,eAAe;6CACI,EAAE,oBAAoB;;;AAGnE,EAAE,eAAe,yBAAyB;;;;;;;;;;;;;sCAaJ,CAAC;YAEjC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAE5C,OAAO,YAAY,UAAU,sBAAsB,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,UAAU,sBAAsB,CAAC;QAC1C;IACF;IAEA,yCAAyC;IACzC,wBAAwB,CAAC;QACvB,IAAI;YACF,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,IAAI,UAAU,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,MAAM,MAAM,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC;YAE/F,IAAI,sBAAsB,GAAG;gBAC3B,WAAW,CAAC,QAAQ,EAAE,oBAAoB,uCAAuC,CAAC;YACpF;YAEA,IAAI,kBAAkB,IAAI;gBACxB,WAAW;YACb,OAAO,IAAI,kBAAkB,IAAI;gBAC/B,WAAW;YACb,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG;gBAC3B,WAAW;YACb,OAAO;gBACL,UAAU;YACZ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,MAAM,OAAO,SAAiB,cAA6B,EAAE;QAC3D,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO;YACT;YAEA,8BAA8B;YAC9B,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,OAAO,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,YAAY,gBAAiB,OAAe,EAAE,cAA6B,EAAE;QAC3E,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,MAAM;gBACN;YACF;YAEA,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,qBAAqB,CAAC;YAEjD,WAAW,MAAM,SAAS,OAAO,MAAM,CAAE;gBACvC,MAAM,YAAY,MAAM,IAAI;gBAC5B,IAAI,WAAW;oBACb,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,gBAAgB,CAAC;QACf,IAAI;YACF,QAAQ,IAAI,8JAAA,CAAA,qBAAkB,CAAC;YAC/B,QAAQ,MAAM,kBAAkB,CAAC;gBAAE,OAAO;YAAiC;YAC3E,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,aAAa;QACX,OAAO,CAAC,CAAC;IACX;AACF", "debugId": null}}, {"offset": {"line": 2073, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/task-service.ts"], "sourcesContent": ["import { Task } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Tạo một lớp cache đơn giản để lưu trữ tạm thởi\r\nlet tasksCache: Task[] = [];\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const TaskService = {\r\n  getTasks: async (): Promise<Task[]> => {\r\n    const now = Date.now();\r\n    // Kiểm tra xem cache có hết hạn chưa\r\n    if (now - lastFetchTime > CACHE_DURATION || tasksCache.length === 0) {\r\n      try {\r\n        const tasks = await ApiService.tasks.getAll();\r\n        tasksCache = tasks;\r\n        lastFetchTime = now;\r\n        return tasks;\r\n      } catch (error) {\r\n        console.error('Lỗi khi lấy danh sách công việc:', error);\r\n        // Nếu có lỗi, trả về cache hiện tại hoặc mảng rỗng\r\n        return tasksCache.length > 0 ? tasksCache : [];\r\n      }\r\n    }\r\n    return tasksCache;\r\n  },\r\n  \r\n  getTask: async (id: string): Promise<Task | undefined> => {\r\n    try {\r\n      // Kiểm tra trong cache trước\r\n      const cachedTask = tasksCache.find(task => task.id === id);\r\n      if (cachedTask) return cachedTask;\r\n\r\n      // Nếu không có trong cache, gọi API\r\n      const task = await ApiService.tasks.getById(id);\r\n\r\n      // API trả về null cho 404, không throw error\r\n      if (task === null) {\r\n        console.debug(`Task với ID ${id} không tồn tại (đã bị xóa hoặc không hợp lệ)`);\r\n        return undefined;\r\n      }\r\n\r\n      return task;\r\n    } catch (error: any) {\r\n      // Chỉ log lỗi cho các lỗi khác (không phải 404)\r\n      console.error(`Lỗi khi lấy công việc với id ${id}:`, error);\r\n      return undefined;\r\n    }\r\n  },\r\n  \r\n  createTask: async (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> => {\r\n    try {\r\n      const newTask = await ApiService.tasks.create(task);\r\n      // Cập nhật cache\r\n      tasksCache = [...tasksCache, newTask];\r\n      return newTask;\r\n    } catch (error) {\r\n      console.error('Lỗi khi tạo công việc mới:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  updateTask: async (id: string, updates: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> => {\r\n    try {\r\n      console.log(`TaskService: Đang cập nhật task ${id} với data:`, updates);\r\n      const updatedTask = await ApiService.tasks.update(id, updates);\r\n      console.log(`TaskService: Task ${id} đã được cập nhật:`, updatedTask);\r\n\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`TaskService: Lỗi khi cập nhật công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  deleteTask: async (id: string): Promise<void> => {\r\n    try {\r\n      await ApiService.tasks.delete(id);\r\n      // Cập nhật cache\r\n      tasksCache = tasksCache.filter(task => task.id !== id);\r\n    } catch (error) {\r\n      console.error(`Lỗi khi xóa công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  toggleTaskCompletion: async (id: string): Promise<Task> => {\r\n    try {\r\n      const updatedTask = await ApiService.tasks.toggleCompletion(id);\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`Lỗi khi chuyển trạng thái hoàn thành của công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Phương thức để xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    tasksCache = [];\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshTasks: async (): Promise<Task[]> => {\r\n    try {\r\n      const tasks = await ApiService.tasks.getAll();\r\n      tasksCache = tasks;\r\n      lastFetchTime = Date.now();\r\n      return tasks;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới danh sách công việc:', error);\r\n      return tasksCache;\r\n    }\r\n  }\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,iDAAiD;AACjD,IAAI,aAAqB,EAAE;AAC3B,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,cAAc;IACzB,UAAU;QACR,MAAM,MAAM,KAAK,GAAG;QACpB,qCAAqC;QACrC,IAAI,MAAM,gBAAgB,kBAAkB,WAAW,MAAM,KAAK,GAAG;YACnE,IAAI;gBACF,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,mDAAmD;gBACnD,OAAO,WAAW,MAAM,GAAG,IAAI,aAAa,EAAE;YAChD;QACF;QACA,OAAO;IACT;IAEA,SAAS,OAAO;QACd,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACvD,IAAI,YAAY,OAAO;YAEvB,oCAAoC;YACpC,MAAM,OAAO,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO,CAAC;YAE5C,6CAA6C;YAC7C,IAAI,SAAS,MAAM;gBACjB,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,GAAG,4CAA4C,CAAC;gBAC7E,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,gDAAgD;YAChD,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,OAAO;QACT;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,UAAU,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,iBAAiB;YACjB,aAAa;mBAAI;gBAAY;aAAQ;YACrC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,YAAY,OAAO,IAAY;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,GAAG,UAAU,CAAC,EAAE;YAC/D,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YACtD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,kBAAkB,CAAC,EAAE;YAEzD,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9B,iBAAiB;YACjB,aAAa,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,MAAM;QACR;IACF;IAEA,sBAAsB,OAAO;QAC3B,IAAI;YACF,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC;YAC5D,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,GAAG,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,YAAY;QACV,aAAa,EAAE;QACf,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,cAAc;QACZ,IAAI;YACF,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YAC3C,aAAa;YACb,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 2197, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Cross2Icon } from \"@radix-ui/react-icons\";\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-[51] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-1/2 top-1/2 z-[51] grid max-h-[calc(100%-4rem)] w-full -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto border bg-background/90 backdrop-blur-md p-6 shadow-lg shadow-black/10 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:max-w-[400px] sm:rounded-xl\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"group absolute right-3 top-3 flex size-7 items-center justify-center rounded-lg outline-offset-2 transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none\">\n        <Cross2Icon\n          width={16}\n          height={16}\n          strokeWidth={2}\n          className=\"opacity-60 transition-opacity group-hover:opacity-100\"\n        />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)} {...props} />\n);\nDialogHeader.displayName = \"DialogHeader\";\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3\", className)}\n    {...props}\n  />\n);\nDialogFooter.displayName = \"DialogFooter\";\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold tracking-tight\", className)}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gJACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6kBACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gLAAA,CAAA,aAAU;gCACT,OAAO;gCACP,QAAQ;gCACR,aAAa;gCACb,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QAAa,GAAG,KAAK;;;;;;AAEhG,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2332, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/markdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport rehypeHighlight from 'rehype-highlight';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Import highlight.js styles\r\nimport 'highlight.js/styles/github-dark.css';\r\n\r\ninterface MarkdownProps {\r\n  content: string;\r\n  className?: string;\r\n}\r\n\r\nexport function Markdown({ content, className }: MarkdownProps) {\r\n  return (\r\n    <div className={cn(\"max-w-none\", className)}>\r\n      <ReactMarkdown\r\n        remarkPlugins={[remarkGfm]}\r\n        rehypePlugins={[rehypeHighlight]}\r\n        components={{\r\n          // Custom styling for different elements\r\n          h1: ({ children }) => (\r\n            <h1 className=\"text-lg font-bold mb-2 text-foreground\">{children}</h1>\r\n          ),\r\n          h2: ({ children }) => (\r\n            <h2 className=\"text-base font-semibold mb-2 text-foreground\">{children}</h2>\r\n          ),\r\n          h3: ({ children }) => (\r\n            <h3 className=\"text-sm font-medium mb-1 text-foreground\">{children}</h3>\r\n          ),\r\n          // Sử dụng div thay vì p để tránh nesting issues với code blocks\r\n          p: ({ children }) => (\r\n            <div className=\"mb-2 text-foreground leading-relaxed\">{children}</div>\r\n          ),\r\n          ul: ({ children }) => (\r\n            <ul className=\"list-disc list-inside mb-2 space-y-1 text-foreground\">{children}</ul>\r\n          ),\r\n          ol: ({ children }) => (\r\n            <ol className=\"list-decimal list-inside mb-2 space-y-1 text-foreground\">{children}</ol>\r\n          ),\r\n          li: ({ children }) => (\r\n            <li className=\"text-foreground\">{children}</li>\r\n          ),\r\n          blockquote: ({ children }) => (\r\n            <blockquote className=\"border-l-4 border-blue-500 pl-4 italic my-2 text-muted-foreground\">\r\n              {children}\r\n            </blockquote>\r\n          ),\r\n          code: ({ className, children, ...props }: any) => {\r\n            const match = /language-(\\w+)/.exec(className || '');\r\n            const inline = props.inline;\r\n            return !inline ? (\r\n              <div className=\"relative my-3\">\r\n                <pre className=\"bg-muted rounded-md p-3 overflow-x-auto text-sm\">\r\n                  <code className={className} {...props}>\r\n                    {children}\r\n                  </code>\r\n                </pre>\r\n                {match && (\r\n                  <span className=\"absolute top-2 right-2 text-xs text-muted-foreground bg-background px-2 py-1 rounded\">\r\n                    {match[1]}\r\n                  </span>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <code className=\"bg-muted px-1 py-0.5 rounded text-sm font-mono\" {...props}>\r\n                {children}\r\n              </code>\r\n            );\r\n          },\r\n          table: ({ children }) => (\r\n            <div className=\"overflow-x-auto my-2\">\r\n              <table className=\"min-w-full border-collapse border border-border\">\r\n                {children}\r\n              </table>\r\n            </div>\r\n          ),\r\n          th: ({ children }) => (\r\n            <th className=\"border border-border bg-muted px-3 py-2 text-left font-medium\">\r\n              {children}\r\n            </th>\r\n          ),\r\n          td: ({ children }) => (\r\n            <td className=\"border border-border px-3 py-2\">{children}</td>\r\n          ),\r\n          a: ({ children, href }) => (\r\n            <a \r\n              href={href} \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-blue-600 hover:text-blue-800 underline\"\r\n            >\r\n              {children}\r\n            </a>\r\n          ),\r\n          strong: ({ children }) => (\r\n            <strong className=\"font-semibold text-foreground\">{children}</strong>\r\n          ),\r\n          em: ({ children }) => (\r\n            <em className=\"italic text-foreground\">{children}</em>\r\n          ),\r\n          hr: () => (\r\n            <hr className=\"my-4 border-border\" />\r\n          ),\r\n        }}\r\n      >\r\n        {content}\r\n      </ReactMarkdown>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAiB;IAC5D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,8OAAC,wLAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,6IAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,mJAAA,CAAA,UAAe;aAAC;YAChC,YAAY;gBACV,wCAAwC;gBACxC,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;gBAE1D,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;gBAEhE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;gBAE5D,gEAAgE;gBAChE,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAwD;;;;;;gBAExE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;gBAE3E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAmB;;;;;;gBAEnC,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,8OAAC;wBAAW,WAAU;kCACnB;;;;;;gBAGL,MAAM,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;oBAC3C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;oBACjD,MAAM,SAAS,MAAM,MAAM;oBAC3B,OAAO,CAAC,uBACN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW;oCAAY,GAAG,KAAK;8CAClC;;;;;;;;;;;4BAGJ,uBACC,8OAAC;gCAAK,WAAU;0CACb,KAAK,CAAC,EAAE;;;;;;;;;;;+CAKf,8OAAC;wBAAK,WAAU;wBAAkD,GAAG,KAAK;kCACvE;;;;;;gBAGP;gBACA,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAkC;;;;;;gBAElD,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,8OAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;kCAET;;;;;;gBAGL,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,8OAAC;wBAAO,WAAU;kCAAiC;;;;;;gBAErD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;gBAE1C,IAAI,kBACF,8OAAC;wBAAG,WAAU;;;;;;YAElB;sBAEC;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 2684, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-assistant.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { AIService } from '@/lib/services/ai-service';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Lightbulb, Loader2, MessageCircle, Brain } from 'lucide-react';\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogFooter\r\n} from '@/components/ui/dialog';\r\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Markdown } from '@/components/ui/markdown';\r\n\r\nexport function AIAssistant() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [summary, setSummary] = useState<string | null>(null);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('summary');\r\n\r\n  const generateSummary = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const tasks = await TaskService.getTasks();\r\n      const summaryText = await AIService.summarizeTasks(tasks);\r\n      setSummary(summaryText);\r\n    } catch (error) {\r\n      console.error(\"Error generating summary:\", error);\r\n      setSummary(\"Đã xảy ra lỗi khi tạo báo cáo. Vui lòng thử lại sau.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50\">\r\n      <Button\r\n        onClick={() => setIsOpen(true)}\r\n        size=\"icon\"\r\n        className=\"h-12 w-12 rounded-full shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\r\n      >\r\n        <Brain className=\"h-6 w-6\" />\r\n      </Button>\r\n\r\n      <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n        <DialogContent className=\"sm:max-w-[480px] p-0 overflow-hidden\">\r\n          <DialogHeader className=\"px-6 pt-6 pb-2\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <Brain className=\"h-5 w-5\" />\r\n              Dr.AITime\r\n            </DialogTitle>\r\n            <DialogDescription>Trợ lý AI thông minh cho quản lý thời gian</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"px-6\">\r\n            <TabsList className=\"grid w-full grid-cols-2\">\r\n              <TabsTrigger value=\"summary\" className=\"flex items-center gap-2\">\r\n                <Lightbulb className=\"h-4 w-4\" />\r\n                Phân tích\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"chat\" className=\"flex items-center gap-2\">\r\n                <MessageCircle className=\"h-4 w-4\" />\r\n                Chat AI\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"summary\" className=\"py-4 text-sm\">\r\n              {isLoading ? (\r\n                <div className=\"flex justify-center py-6\">\r\n                  <Loader2 className=\"h-6 w-6 animate-spin text-muted-foreground\" />\r\n                </div>\r\n              ) : summary ? (\r\n                <div className=\"space-y-2\">\r\n                  <Markdown content={summary} className=\"text-foreground\" />\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-muted-foreground\">\r\n                  AI có thể phân tích công việc của bạn và đưa ra đề xuất thông minh.\r\n                </p>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"chat\" className=\"py-4\">\r\n              <div className=\"text-center text-muted-foreground\">\r\n                <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                <p className=\"text-sm\">Tính năng chat AI sẽ có sẵn trong chatbox riêng biệt.</p>\r\n                <p className=\"text-xs mt-1\">Nhấn vào biểu tượng chat ở góc trái màn hình.</p>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n\r\n          <DialogFooter className=\"flex justify-between border-t p-4\">\r\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setIsOpen(false)}>\r\n              Đóng\r\n            </Button>\r\n            {activeTab === 'summary' && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={generateSummary}\r\n                disabled={isLoading}\r\n                className=\"gap-1\"\r\n              >\r\n                {isLoading && <Loader2 className=\"h-3 w-3 animate-spin\" />}\r\n                Phân tích ngay\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAQA;AACA;AAjBA;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,QAAQ,MAAM,yIAAA,CAAA,cAAW,CAAC,QAAQ;YACxC,MAAM,cAAc,MAAM,uIAAA,CAAA,YAAS,CAAC,cAAc,CAAC;YACnD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAGrB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;;8DACrC,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAO,WAAU;;8DAClC,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAKzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;+CAEnB,wBACF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAS,WAAU;;;;;;;;;;6DAGxC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAMzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAE,WAAU;0DAAU;;;;;;0DACvB,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM,UAAU;8CAAQ;;;;;;gCAGpE,cAAc,2BACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;wCAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E", "debugId": null}}, {"offset": {"line": 2987, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3068, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3136, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-chatbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { AIService, ChatMessage } from '@/lib/services/ai-service';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { MessageCircle, Send, Loader2, X, Minimize2, Maximize2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { Markdown } from '@/components/ui/markdown';\r\nimport { safeLocalStorageGet, safeLocalStorageSet, safeLocalStorageRemove, cleanupLocalStorage } from '@/lib/utils/json-utils';\r\n\r\ninterface AIChatboxProps {\r\n  className?: string;\r\n}\r\n\r\nconst CHAT_HISTORY_KEY = 'dr-aitime-chat-history';\r\nconst CHAT_SETTINGS_KEY = 'dr-aitime-chat-settings';\r\n\r\nexport function AIChatbox({ className }: AIChatboxProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\r\n  const [inputMessage, setInputMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [streamingMessage, setStreamingMessage] = useState('');\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Load chat history from localStorage\r\n  const loadChatHistory = (): ChatMessage[] => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_HISTORY_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        const parsed = JSON.parse(saved);\r\n        // Convert timestamp strings back to Date objects\r\n        return parsed.map((msg: any) => ({\r\n          ...msg,\r\n          timestamp: new Date(msg.timestamp)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat history:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    }\r\n    return [];\r\n  };\r\n\r\n  // Save chat history to localStorage\r\n  const saveChatHistory = (newMessages: ChatMessage[]) => {\r\n    try {\r\n      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(newMessages));\r\n    } catch (error) {\r\n      console.error('Error saving chat history:', error);\r\n    }\r\n  };\r\n\r\n  // Load settings from localStorage\r\n  const loadChatSettings = () => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_SETTINGS_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        return JSON.parse(saved);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat settings:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_SETTINGS_KEY);\r\n    }\r\n    return { isOpen: false, isMinimized: false };\r\n  };\r\n\r\n  // Initialize from localStorage\r\n  useEffect(() => {\r\n    // Clean up any invalid localStorage items first\r\n    cleanupLocalStorage();\r\n\r\n    const savedMessages = loadChatHistory();\r\n    const savedSettings = loadChatSettings();\r\n\r\n    setMessages(savedMessages);\r\n    setIsOpen(savedSettings.isOpen || false);\r\n    setIsMinimized(savedSettings.isMinimized || false);\r\n  }, []);\r\n\r\n  // Save settings whenever they change\r\n  useEffect(() => {\r\n    const settings = { isOpen, isMinimized };\r\n    try {\r\n      localStorage.setItem(CHAT_SETTINGS_KEY, JSON.stringify(settings));\r\n    } catch (error) {\r\n      console.error('Error saving chat settings:', error);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  // Save messages whenever they change\r\n  useEffect(() => {\r\n    if (messages.length > 0) {\r\n      saveChatHistory(messages);\r\n    }\r\n  }, [messages]);\r\n\r\n  // Auto scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [messages, streamingMessage]);\r\n\r\n  // Focus input when chat opens\r\n  useEffect(() => {\r\n    if (isOpen && !isMinimized) {\r\n      setTimeout(() => inputRef.current?.focus(), 100);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputMessage.trim() || isLoading) return;\r\n\r\n    const userMessage: ChatMessage = {\r\n      role: 'user',\r\n      content: inputMessage.trim(),\r\n      timestamp: new Date()\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputMessage('');\r\n    setIsLoading(true);\r\n    setStreamingMessage('');\r\n\r\n    try {\r\n      // Use streaming chat\r\n      const stream = AIService.chatStream(userMessage.content, messages);\r\n      let fullResponse = '';\r\n\r\n      for await (const chunk of stream) {\r\n        fullResponse += chunk;\r\n        setStreamingMessage(fullResponse);\r\n      }\r\n\r\n      // Add complete response to messages\r\n      const assistantMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: fullResponse,\r\n        timestamp: new Date()\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n      setStreamingMessage('');\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n      const errorMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',\r\n        timestamp: new Date()\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const clearChat = () => {\r\n    setMessages([]);\r\n    setStreamingMessage('');\r\n    // Clear from localStorage\r\n    try {\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    } catch (error) {\r\n      console.error('Error clearing chat history:', error);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n        <Button\r\n          onClick={() => setIsOpen(true)}\r\n          size=\"icon\"\r\n          className=\"h-12 w-12 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700\"\r\n        >\r\n          <MessageCircle className=\"h-6 w-6\" />\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n      <Card className={cn(\r\n        \"w-80 shadow-xl transition-all duration-200 relative overflow-hidden\",\r\n        isMinimized ? \"h-12\" : \"h-96\"\r\n      )}>\r\n        <CardHeader className=\"flex flex-row items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg\">\r\n          <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\r\n            <MessageCircle className=\"h-4 w-4\" />\r\n            Dr.AITime Chat\r\n            {messages.length > 0 && (\r\n              <span className=\"bg-blue-500 text-xs px-2 py-1 rounded-full\">\r\n                {messages.length}\r\n              </span>\r\n            )}\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsMinimized(!isMinimized)}\r\n            >\r\n              {isMinimized ? <Maximize2 className=\"h-3 w-3\" /> : <Minimize2 className=\"h-3 w-3\" />}\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsOpen(false)}\r\n            >\r\n              <X className=\"h-3 w-3\" />\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        {!isMinimized && (\r\n          <CardContent className=\"p-0 flex flex-col h-80 relative\">\r\n            {/* Backdrop blur overlay chỉ trong khung chat */}\r\n            <div className=\"absolute inset-0 bg-background/70 backdrop-blur-sm z-10 pointer-events-none\" />\r\n            {/* Messages Area */}\r\n            <ScrollArea className=\"flex-1 p-3 relative z-20\">\r\n              <div className=\"space-y-3\">\r\n                {messages.length === 0 && (\r\n                  <div className=\"text-center text-muted-foreground text-sm py-8\">\r\n                    <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                    <p className=\"font-medium\">Xin chào! Tôi là Dr.AITime 👋</p>\r\n                    <p className=\"mt-1\">Hãy hỏi tôi về quản lý thời gian nhé!</p>\r\n                    <div className=\"mt-3 text-xs opacity-70\">\r\n                      <p>💡 Lịch sử chat sẽ được lưu tự động</p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {messages.map((message, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={cn(\r\n                      \"flex\",\r\n                      message.role === 'user' ? \"justify-end\" : \"justify-start\"\r\n                    )}\r\n                  >\r\n                    <div\r\n                      className={cn(\r\n                        \"max-w-[80%] rounded-lg px-3 py-2 text-sm\",\r\n                        message.role === 'user'\r\n                          ? \"bg-blue-600 text-white\"\r\n                          : \"bg-muted\"\r\n                      )}\r\n                    >\r\n                      {message.role === 'user' ? (\r\n                        <p className=\"whitespace-pre-wrap text-white\">{message.content}</p>\r\n                      ) : (\r\n                        <Markdown content={message.content} className=\"text-foreground\" />\r\n                      )}\r\n                      <p className={cn(\r\n                        \"text-xs opacity-70 mt-1\",\r\n                        message.role === 'user' ? \"text-white\" : \"text-muted-foreground\"\r\n                      )}>\r\n                        {message.timestamp.toLocaleTimeString('vi-VN', {\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n\r\n                {/* Streaming message */}\r\n                {streamingMessage && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"max-w-[80%] rounded-lg px-3 py-2 text-sm bg-muted\">\r\n                      <Markdown content={streamingMessage} className=\"text-foreground\" />\r\n                      <div className=\"flex items-center gap-1 mt-1\">\r\n                        <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                        <span className=\"text-xs opacity-70 text-muted-foreground\">Đang trả lời...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div ref={messagesEndRef} />\r\n              </div>\r\n            </ScrollArea>\r\n\r\n            {/* Input Area */}\r\n            <div className=\"border-t p-3 relative z-20 bg-background\">\r\n              <div className=\"flex gap-2\">\r\n                <Input\r\n                  ref={inputRef}\r\n                  value={inputMessage}\r\n                  onChange={(e) => setInputMessage(e.target.value)}\r\n                  onKeyDown={handleKeyPress}\r\n                  placeholder=\"Nhập tin nhắn...\"\r\n                  disabled={isLoading}\r\n                  className=\"flex-1\"\r\n                />\r\n                <Button\r\n                  onClick={handleSendMessage}\r\n                  disabled={isLoading || !inputMessage.trim()}\r\n                  size=\"icon\"\r\n                  className=\"shrink-0\"\r\n                >\r\n                  {isLoading ? (\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                  ) : (\r\n                    <Send className=\"h-4 w-4\" />\r\n                  )}\r\n                </Button>\r\n              </div>\r\n              \r\n              {messages.length > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    if (window.confirm('Bạn có chắc chắn muốn xóa toàn bộ lịch sử chat? Hành động này không thể hoàn tác.')) {\r\n                      clearChat();\r\n                    }\r\n                  }}\r\n                  className=\"w-full mt-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  🗑️ Xóa lịch sử chat ({messages.length} tin nhắn)\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </CardContent>\r\n        )}\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAkBA,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAEnB,SAAS,UAAU,EAAE,SAAS,EAAkB;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sCAAsC;IACtC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,iDAAiD;gBACjD,OAAO,OAAO,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC/B,GAAG,GAAG;wBACN,WAAW,IAAI,KAAK,IAAI,SAAS;oBACnC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO,EAAE;IACX;IAEA,oCAAoC;IACpC,MAAM,kBAAkB,CAAC;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,OAAO,KAAK,KAAK,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO;YAAE,QAAQ;YAAO,aAAa;QAAM;IAC7C;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD;QAElB,MAAM,gBAAgB;QACtB,MAAM,gBAAgB;QAEtB,YAAY;QACZ,UAAU,cAAc,MAAM,IAAI;QAClC,eAAe,cAAc,WAAW,IAAI;IAC9C,GAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YAAE;YAAQ;QAAY;QACvC,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAS;IAEb,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;QAAU;KAAiB;IAE/B,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,aAAa;YAC1B,WAAW,IAAM,SAAS,OAAO,EAAE,SAAS;QAC9C;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QACb,oBAAoB;QAEpB,IAAI;YACF,qBAAqB;YACrB,MAAM,SAAS,uIAAA,CAAA,YAAS,CAAC,UAAU,CAAC,YAAY,OAAO,EAAE;YACzD,IAAI,eAAe;YAEnB,WAAW,MAAM,SAAS,OAAQ;gBAChC,gBAAgB;gBAChB,oBAAoB;YACtB;YAEA,oCAAoC;YACpC,MAAM,mBAAgC;gBACpC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAA4B;gBAChC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY,EAAE;QACd,oBAAoB;QACpB,0BAA0B;QAC1B,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC/C,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uEACA,cAAc,SAAS;;8BAEvB,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;gCAEpC,SAAS,MAAM,GAAG,mBACjB,8OAAC;oCAAK,WAAU;8CACb,SAAS,MAAM;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe,CAAC;8CAE9B,4BAAc,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAAe,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAEzB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKlB,CAAC,6BACA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DACpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;oCAKR,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;sDAG5C,cAAA,8OAAC;gDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,QAAQ,IAAI,KAAK,SACb,2BACA;;oDAGL,QAAQ,IAAI,KAAK,uBAChB,8OAAC;wDAAE,WAAU;kEAAkC,QAAQ,OAAO;;;;;6EAE9D,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,SAAS,QAAQ,OAAO;wDAAE,WAAU;;;;;;kEAEhD,8OAAC;wDAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,2BACA,QAAQ,IAAI,KAAK,SAAS,eAAe;kEAExC,QAAQ,SAAS,CAAC,kBAAkB,CAAC,SAAS;4DAC7C,MAAM;4DACN,QAAQ;wDACV;;;;;;;;;;;;2CA1BC;;;;;oCAiCR,kCACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,SAAS;oDAAkB,WAAU;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;kDAMnE,8OAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAW;4CACX,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC,aAAa,IAAI;4CACzC,MAAK;4CACL,WAAU;sDAET,0BACC,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAKrB,SAAS,MAAM,GAAG,mBACjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,IAAI,OAAO,OAAO,CAAC,sFAAsF;4CACvG;wCACF;oCACF;oCACA,WAAU;;wCACX;wCACwB,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 3716, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Sidebar } from \"@/components/ui/sidebar\";\nimport SidebarMenu from \"@/components/ui/SidebarMenu\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ThemeToggle } from \"@/components/theme/theme-toggle\";\nimport { ClearDataButton } from \"@/components/clear-data-button\";\nimport { Footerdemo } from \"@/components/ui/footer-section\";\nimport { useSidebar } from \"@/components/ui/sidebar\";\nimport { Header } from \"@/components/layout/header\";\nimport { AIAssistant } from '@/components/ai/ai-assistant';\nimport { AIChatbox } from '@/components/ai/ai-chatbox';\n\nexport function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"flex h-screen flex-col md:flex-row\">\n      <Sidebar>\n        <div className=\"flex h-full flex-col justify-between\">\n          <div className=\"flex flex-col gap-2\">\n            <div className=\"flex items-center gap-2 px-2 py-2\">\n              <Logo />\n            </div>\n            <SidebarMenu />\n          </div>\n          <div className=\"flex flex-col gap-2 px-2\">\n            <ClearDataButton />\n          </div>\n        </div>\n      </Sidebar>\n      <div className=\"flex-1 overflow-auto\">\n        <div className=\"min-h-screen flex flex-col\">\n          <Header />\n          <div className=\"flex-grow px-4 py-6 md:px-8 lg:px-12\">\n            {children}\n          </div>\n          <Footerdemo />\n        </div>\n      </div>\n\n      <AIAssistant />\n      <AIChatbox />\n    </div>\n  );\n}\n\nconst Logo = () => {\n  const { open, animate } = useSidebar();\n\n  return (\n    <Link\n      href=\"/\"\n      className=\"font-normal flex space-x-2 items-center text-sm text-black dark:text-white py-1 relative z-20\"\n    >\n      <div className=\"h-5 w-6 bg-primary rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0\" />\n      <motion.span\n        initial={{ opacity: 0 }}\n        animate={{ \n          opacity: 1,\n          marginLeft: animate ? (open ? \"8px\" : \"40px\") : \"8px\",\n          display: animate ? \"inline-block\" : \"inline-block\"\n        }}\n        transition={{ duration: 0.3 }}\n        className=\"font-medium whitespace-pre text-xl\"\n      >\n        QLTime\n      </motion.span>\n    </Link>\n  );\n}; "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAbA;;;;;;;;;;;;AAeO,SAAS,OAAO,EAAE,QAAQ,EAAiC;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAO;0BACN,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;;;;;;;;;8CAEH,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;sCAEd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6IAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,SAAM;;;;;sCACP,8OAAC;4BAAI,WAAU;sCACZ;;;;;;sCAEH,8OAAC,6IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;0BAIf,8OAAC,2IAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,yIAAA,CAAA,YAAS;;;;;;;;;;;AAGhB;AAEA,MAAM,OAAO;IACX,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBACP,SAAS;oBACT,YAAY,UAAW,OAAO,QAAQ,SAAU;oBAChD,SAAS,UAAU,iBAAiB;gBACtC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 3894, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className,\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3939, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/time-block-service.ts"], "sourcesContent": ["import { TimeBlock } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Cache cho time blocks\r\nlet timeBlocksCache: TimeBlock[] | null = null;\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const TimeBlockService = {\r\n  getTasks: async (): Promise<TimeBlock[]> => {\r\n    const now = Date.now();\r\n    \r\n    // Nếu có cache và chưa hết hạn, trả về cache\r\n    if (timeBlocksCache && now - lastFetchTime < CACHE_DURATION) {\r\n      return timeBlocksCache;\r\n    }\r\n    \r\n    try {\r\n      // Gọi API để lấy time blocks\r\n      const timeBlocks = await ApiService.timeBlocks.getAll();\r\n      timeBlocksCache = timeBlocks;\r\n      lastFetchTime = now;\r\n      return timeBlocks;\r\n    } catch (error) {\r\n      console.error('Lỗi khi lấy danh sách khối thời gian:', error);\r\n      \r\n      // Nếu có lỗi và đã có cache, trả về cache\r\n      if (timeBlocksCache) {\r\n        return timeBlocksCache;\r\n      }\r\n      \r\n      // N<PERSON><PERSON> không có cache, trả về mảng rỗng\r\n      return [];\r\n    }\r\n  },\r\n  \r\n  getTimeBlocks: async (): Promise<TimeBlock[]> => {\r\n    return TimeBlockService.getTasks();\r\n  },\r\n  \r\n  getTimeBlocksForDay: async (date: Date): Promise<TimeBlock[]> => {\r\n    const timeBlocks = await TimeBlockService.getTimeBlocks();\r\n    const targetDate = new Date(date);\r\n    targetDate.setHours(0, 0, 0, 0);\r\n    \r\n    const nextDay = new Date(targetDate);\r\n    nextDay.setDate(nextDay.getDate() + 1);\r\n    \r\n    return timeBlocks.filter(block => {\r\n      const blockDate = new Date(block.startTime);\r\n      return blockDate >= targetDate && blockDate < nextDay;\r\n    });\r\n  },\r\n  \r\n  getTimeBlock: async (id: string): Promise<TimeBlock | undefined> => {\r\n    try {\r\n      return await ApiService.timeBlocks.getById(id);\r\n    } catch (error) {\r\n      console.error(`Lỗi khi lấy khối thời gian với id ${id}:`, error);\r\n      \r\n      // Nếu có cache, tìm trong cache\r\n      if (timeBlocksCache) {\r\n        return timeBlocksCache.find(block => block.id === id);\r\n      }\r\n      \r\n      return undefined;\r\n    }\r\n  },\r\n  \r\n  createTimeBlock: async (timeBlock: Omit<TimeBlock, 'id'>): Promise<TimeBlock> => {\r\n    try {\r\n      // Loại bỏ id nếu có (để đảm bảo không gửi id trong request tạo mới)\r\n      const { id, ...timeBlockData } = timeBlock as any;\r\n\r\n      console.log('TimeBlockService: Tạo time block với data:', timeBlockData);\r\n      const newTimeBlock = await ApiService.timeBlocks.create(timeBlockData);\r\n\r\n      // Cập nhật cache nếu có\r\n      if (timeBlocksCache) {\r\n        timeBlocksCache.push(newTimeBlock);\r\n      }\r\n\r\n      return newTimeBlock;\r\n    } catch (error) {\r\n      console.error('Lỗi khi tạo khối thời gian mới:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  updateTimeBlock: async (timeBlock: TimeBlock): Promise<TimeBlock> => {\r\n    try {\r\n      const updatedTimeBlock = await ApiService.timeBlocks.update(timeBlock.id, timeBlock);\r\n      \r\n      // Cập nhật cache nếu có\r\n      if (timeBlocksCache) {\r\n        const index = timeBlocksCache.findIndex(block => block.id === timeBlock.id);\r\n        if (index !== -1) {\r\n          timeBlocksCache[index] = updatedTimeBlock;\r\n        }\r\n      }\r\n      \r\n      return updatedTimeBlock;\r\n    } catch (error) {\r\n      console.error(`Lỗi khi cập nhật khối thời gian với id ${timeBlock.id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  deleteTimeBlock: async (id: string): Promise<void> => {\r\n    try {\r\n      await ApiService.timeBlocks.delete(id);\r\n      \r\n      // Cập nhật cache nếu có\r\n      if (timeBlocksCache) {\r\n        timeBlocksCache = timeBlocksCache.filter(block => block.id !== id);\r\n      }\r\n    } catch (error) {\r\n      console.error(`Lỗi khi xóa khối thời gian với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  toggleTimeBlockCompletion: async (id: string): Promise<TimeBlock> => {\r\n    try {\r\n      const timeBlock = await TimeBlockService.getTimeBlock(id);\r\n      if (!timeBlock) {\r\n        throw new Error('Không tìm thấy khối thời gian');\r\n      }\r\n      \r\n      return await TimeBlockService.updateTimeBlock({\r\n        ...timeBlock,\r\n        isCompleted: !timeBlock.isCompleted\r\n      });\r\n    } catch (error) {\r\n      console.error(`Lỗi khi chuyển đổi trạng thái hoàn thành của khối thời gian với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshTimeBlocks: async (): Promise<TimeBlock[]> => {\r\n    try {\r\n      const timeBlocks = await ApiService.timeBlocks.getAll();\r\n      timeBlocksCache = timeBlocks;\r\n      lastFetchTime = Date.now();\r\n      return timeBlocks;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới danh sách khối thời gian:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    timeBlocksCache = null;\r\n    lastFetchTime = 0;\r\n  },\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,wBAAwB;AACxB,IAAI,kBAAsC;AAC1C,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,mBAAmB;IAC9B,UAAU;QACR,MAAM,MAAM,KAAK,GAAG;QAEpB,6CAA6C;QAC7C,IAAI,mBAAmB,MAAM,gBAAgB,gBAAgB;YAC3D,OAAO;QACT;QAEA,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;YACrD,kBAAkB;YAClB,gBAAgB;YAChB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YAEvD,0CAA0C;YAC1C,IAAI,iBAAiB;gBACnB,OAAO;YACT;YAEA,uCAAuC;YACvC,OAAO,EAAE;QACX;IACF;IAEA,eAAe;QACb,OAAO,iBAAiB,QAAQ;IAClC;IAEA,qBAAqB,OAAO;QAC1B,MAAM,aAAa,MAAM,iBAAiB,aAAa;QACvD,MAAM,aAAa,IAAI,KAAK;QAC5B,WAAW,QAAQ,CAAC,GAAG,GAAG,GAAG;QAE7B,MAAM,UAAU,IAAI,KAAK;QACzB,QAAQ,OAAO,CAAC,QAAQ,OAAO,KAAK;QAEpC,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;YAC1C,OAAO,aAAa,cAAc,YAAY;QAChD;IACF;IAEA,cAAc,OAAO;QACnB,IAAI;YACF,OAAO,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,OAAO,CAAC;QAC7C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAE1D,gCAAgC;YAChC,IAAI,iBAAiB;gBACnB,OAAO,gBAAgB,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACpD;YAEA,OAAO;QACT;IACF;IAEA,iBAAiB,OAAO;QACtB,IAAI;YACF,oEAAoE;YACpE,MAAM,EAAE,EAAE,EAAE,GAAG,eAAe,GAAG;YAEjC,QAAQ,GAAG,CAAC,8CAA8C;YAC1D,MAAM,eAAe,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;YAExD,wBAAwB;YACxB,IAAI,iBAAiB;gBACnB,gBAAgB,IAAI,CAAC;YACvB;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,mBAAmB,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE;YAE1E,wBAAwB;YACxB,IAAI,iBAAiB;gBACnB,MAAM,QAAQ,gBAAgB,SAAS,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK,UAAU,EAAE;gBAC1E,IAAI,UAAU,CAAC,GAAG;oBAChB,eAAe,CAAC,MAAM,GAAG;gBAC3B;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uCAAuC,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,EAAE;YACzE,MAAM;QACR;IACF;IAEA,iBAAiB,OAAO;QACtB,IAAI;YACF,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;YAEnC,wBAAwB;YACxB,IAAI,iBAAiB;gBACnB,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACjE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,kCAAkC,EAAE,GAAG,CAAC,CAAC,EAAE;YAC1D,MAAM;QACR;IACF;IAEA,2BAA2B,OAAO;QAChC,IAAI;YACF,MAAM,YAAY,MAAM,iBAAiB,YAAY,CAAC;YACtD,IAAI,CAAC,WAAW;gBACd,MAAM,IAAI,MAAM;YAClB;YAEA,OAAO,MAAM,iBAAiB,eAAe,CAAC;gBAC5C,GAAG,SAAS;gBACZ,aAAa,CAAC,UAAU,WAAW;YACrC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mEAAmE,EAAE,GAAG,CAAC,CAAC,EAAE;YAC3F,MAAM;QACR;IACF;IAEA,wCAAwC;IACxC,mBAAmB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;YACrD,kBAAkB;YAClB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,YAAY;QACV,kBAAkB;QAClB,gBAAgB;IAClB;AACF", "debugId": null}}, {"offset": {"line": 4080, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4112, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/form.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  ControllerProps,\r\n  FieldPath,\r\n  FieldValues,\r\n  FormProvider,\r\n  useFormContext,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nconst FormItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  )\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message) : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;AACA;AASA;AACA;;;;;;;AAEA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW;IAE9C,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4264, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-[999] max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className,\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\",\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0cACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4456, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/task/task-selector.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { Task } from '@/lib/types';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport {\r\n  Select,\r\n  SelectContent,\r\n  SelectItem,\r\n  SelectTrigger,\r\n  SelectValue,\r\n} from '@/components/ui/select';\r\nimport { Loader2 } from 'lucide-react';\r\n\r\ninterface TaskSelectorProps {\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n}\r\n\r\nexport function TaskSelector({ value, onChange }: TaskSelectorProps) {\r\n  const [tasks, setTasks] = useState<Task[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  \r\n  useEffect(() => {\r\n    const loadTasks = async () => {\r\n      setLoading(true);\r\n      try {\r\n        // Lấy danh sách các công việc chưa hoàn thành\r\n        const allTasks = await TaskService.getTasks();\r\n        const activeTasks = allTasks.filter(task => !task.completed);\r\n        setTasks(activeTasks);\r\n        setError(null);\r\n      } catch (err) {\r\n        console.error('Lỗi khi tải danh sách công việc:', err);\r\n        setError('Không thể tải danh sách công việc');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n    \r\n    loadTasks();\r\n  }, []);\r\n  \r\n  return (\r\n    <div className=\"relative\">\r\n      <Select \r\n        value={value || \"none\"} \r\n        onValueChange={onChange}\r\n        disabled={loading}\r\n      >\r\n        <SelectTrigger className=\"w-full\">\r\n          {loading ? (\r\n            <div className=\"flex items-center gap-2\">\r\n              <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n              <span>Đang tải...</span>\r\n            </div>\r\n          ) : (\r\n            <SelectValue placeholder=\"Chọn công việc (nếu có)\" />\r\n          )}\r\n        </SelectTrigger>\r\n        <SelectContent className=\"backdrop-blur-sm border border-border/40 bg-background/90\">\r\n          <SelectItem value=\"none\">Không liên kết với công việc</SelectItem>\r\n          {tasks.map(task => (\r\n            <SelectItem key={task.id} value={task.id}>\r\n              {task.title}\r\n            </SelectItem>\r\n          ))}\r\n          {error && (\r\n            <div className=\"py-2 px-2 text-sm text-destructive\">\r\n              {error}\r\n            </div>\r\n          )}\r\n          {!loading && tasks.length === 0 && !error && (\r\n            <div className=\"py-2 px-2 text-sm text-muted-foreground\">\r\n              Không có công việc nào chưa hoàn thành\r\n            </div>\r\n          )}\r\n        </SelectContent>\r\n      </Select>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AAOA;AAZA;;;;;;AAmBO,SAAS,aAAa,EAAE,KAAK,EAAE,QAAQ,EAAqB;IACjE,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY;YAChB,WAAW;YACX,IAAI;gBACF,8CAA8C;gBAC9C,MAAM,WAAW,MAAM,yIAAA,CAAA,cAAW,CAAC,QAAQ;gBAC3C,MAAM,cAAc,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;gBAC3D,SAAS;gBACT,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;YACL,OAAO,SAAS;YAChB,eAAe;YACf,UAAU;;8BAEV,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;8BACtB,wBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,iNAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,8OAAC;0CAAK;;;;;;;;;;;6CAGR,8OAAC,kIAAA,CAAA,cAAW;wBAAC,aAAY;;;;;;;;;;;8BAG7B,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,aAAU;4BAAC,OAAM;sCAAO;;;;;;wBACxB,MAAM,GAAG,CAAC,CAAA,qBACT,8OAAC,kIAAA,CAAA,aAAU;gCAAe,OAAO,KAAK,EAAE;0CACrC,KAAK,KAAK;+BADI,KAAK,EAAE;;;;;wBAIzB,uBACC,8OAAC;4BAAI,WAAU;sCACZ;;;;;;wBAGJ,CAAC,WAAW,MAAM,MAAM,KAAK,KAAK,CAAC,uBAClC,8OAAC;4BAAI,WAAU;sCAA0C;;;;;;;;;;;;;;;;;;;;;;;AAQrE", "debugId": null}}, {"offset": {"line": 4594, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/timeblock/time-block-form.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { useForm } from 'react-hook-form';\r\nimport { zodResolver } from '@hookform/resolvers/zod';\r\nimport * as z from 'zod';\r\nimport { format } from 'date-fns';\r\nimport { TimeBlock } from '@/lib/types';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogDescription,\r\n  DialogFooter,\r\n  DialogHeader,\r\n  DialogTitle,\r\n} from '@/components/ui/dialog';\r\nimport {\r\n  Form,\r\n  FormControl,\r\n  FormField,\r\n  FormItem,\r\n  FormLabel,\r\n  FormMessage,\r\n} from '@/components/ui/form';\r\nimport { TimeBlockService } from '@/lib/services/time-block-service';\r\nimport { TaskSelector } from '../task/task-selector';\r\nimport { Loader2 } from 'lucide-react';\r\nimport { useStatisticsRefresh } from '@/lib/contexts/statistics-context';\r\n\r\ninterface TimeBlockFormProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  onUpdate: () => void;\r\n  date: Date;\r\n  timeBlock?: TimeBlock;\r\n}\r\n\r\nconst formSchema = z.object({\r\n  title: z.string().min(1, 'Vui lòng nhập tiêu đề'),\r\n  startTime: z.string().min(1, 'Vui lòng chọn thời gian bắt đầu'),\r\n  endTime: z.string().min(1, 'Vui lòng chọn thời gian kết thúc'),\r\n  taskId: z.string().nullable().optional(),\r\n}).refine(data => {\r\n  return data.endTime > data.startTime;\r\n}, {\r\n  message: 'Thời gian kết thúc phải sau thời gian bắt đầu',\r\n  path: ['endTime'],\r\n});\r\n\r\ntype FormValues = z.infer<typeof formSchema>;\r\n\r\nexport function TimeBlockForm({ isOpen, onClose, onUpdate, date, timeBlock }: TimeBlockFormProps) {\r\n  const refreshStatistics = useStatisticsRefresh();\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  \r\n  const isEditing = !!timeBlock;\r\n  const todayStr = format(date, 'yyyy-MM-dd');\r\n  \r\n  const form = useForm<FormValues>({\r\n    resolver: zodResolver(formSchema),\r\n    defaultValues: {\r\n      title: '',\r\n      startTime: `${todayStr}T09:00`,\r\n      endTime: `${todayStr}T10:00`,\r\n      taskId: '',\r\n    },\r\n  });\r\n  \r\n  useEffect(() => {\r\n    if (timeBlock) {\r\n      form.reset({\r\n        title: timeBlock.title,\r\n        startTime: format(new Date(timeBlock.startTime), \"yyyy-MM-dd'T'HH:mm\"),\r\n        endTime: format(new Date(timeBlock.endTime), \"yyyy-MM-dd'T'HH:mm\"),\r\n        taskId: timeBlock.taskId || '',\r\n      });\r\n    } else {\r\n      // Tạo timeblock mới, đặt giá trị mặc định cho ngày hiện tại\r\n      form.reset({\r\n        title: '',\r\n        startTime: `${todayStr}T09:00`,\r\n        endTime: `${todayStr}T10:00`,\r\n        taskId: '',\r\n      });\r\n    }\r\n  }, [timeBlock, form, todayStr]);\r\n  \r\n  const onSubmit = async (values: FormValues) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n    \r\n    try {\r\n      if (isEditing && timeBlock) {\r\n        // Cập nhật time block hiện có\r\n        await TimeBlockService.updateTimeBlock({\r\n          ...timeBlock,\r\n          title: values.title,\r\n          startTime: new Date(values.startTime).toISOString(),\r\n          endTime: new Date(values.endTime).toISOString(),\r\n          taskId: values.taskId === 'none' ? null : values.taskId,\r\n        });\r\n      } else {\r\n        // Tạo time block mới (không gửi id)\r\n        const taskId = values.taskId === 'none' || values.taskId === '' ? null : values.taskId;\r\n        console.log('TimeBlockForm: Tạo time block với taskId:', taskId);\r\n\r\n        await TimeBlockService.createTimeBlock({\r\n          title: values.title,\r\n          startTime: new Date(values.startTime).toISOString(),\r\n          endTime: new Date(values.endTime).toISOString(),\r\n          isCompleted: false,\r\n          taskId: taskId,\r\n        });\r\n      }\r\n\r\n      // Trigger statistics refresh\r\n      refreshStatistics();\r\n\r\n      onUpdate();\r\n      onClose();\r\n    } catch (error) {\r\n      console.error('Lỗi khi lưu khối thời gian:', error);\r\n      setError('Có lỗi xảy ra khi lưu khối thời gian. Vui lòng thử lại.');\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  \r\n  return (\r\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\r\n      <DialogContent className=\"sm:max-w-[500px]\">\r\n        <DialogHeader>\r\n          <DialogTitle>\r\n            {isEditing ? 'Chỉnh sửa khối thời gian' : 'Tạo khối thời gian mới'}\r\n          </DialogTitle>\r\n          <DialogDescription>\r\n            {isEditing\r\n              ? 'Chỉnh sửa thông tin khối thời gian'\r\n              : 'Thêm khối thời gian mới cho lịch của bạn'}\r\n          </DialogDescription>\r\n        </DialogHeader>\r\n        \r\n        {error && (\r\n          <div className=\"bg-destructive/10 text-destructive p-3 rounded-md text-sm mb-4\">\r\n            {error}\r\n          </div>\r\n        )}\r\n        \r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)} className=\"space-y-4\">\r\n            <FormField\r\n              control={form.control}\r\n              name=\"title\"\r\n              render={({ field }) => (\r\n                <FormItem>\r\n                  <FormLabel>Tiêu đề</FormLabel>\r\n                  <FormControl>\r\n                    <Input placeholder=\"Nhập tiêu đề hoạt động\" {...field} />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            \r\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\r\n              <FormField\r\n                control={form.control}\r\n                name=\"startTime\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Thời gian bắt đầu</FormLabel>\r\n                    <FormControl>\r\n                      <Input type=\"datetime-local\" {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n              \r\n              <FormField\r\n                control={form.control}\r\n                name=\"endTime\"\r\n                render={({ field }) => (\r\n                  <FormItem>\r\n                    <FormLabel>Thời gian kết thúc</FormLabel>\r\n                    <FormControl>\r\n                      <Input type=\"datetime-local\" {...field} />\r\n                    </FormControl>\r\n                    <FormMessage />\r\n                  </FormItem>\r\n                )}\r\n              />\r\n            </div>\r\n            \r\n            <FormField\r\n              control={form.control}\r\n              name=\"taskId\"\r\n              render={({ field: { onChange, value } }) => (\r\n                <FormItem>\r\n                  <FormLabel>Liên kết với công việc (tùy chọn)</FormLabel>\r\n                  <FormControl>\r\n                    <TaskSelector\r\n                      value={value || ''}\r\n                      onChange={onChange}\r\n                    />\r\n                  </FormControl>\r\n                  <FormMessage />\r\n                </FormItem>\r\n              )}\r\n            />\r\n            \r\n            <DialogFooter>\r\n              <Button \r\n                type=\"button\" \r\n                variant=\"outline\" \r\n                onClick={onClose}\r\n                disabled={isLoading}\r\n              >\r\n                Hủy\r\n              </Button>\r\n              <Button \r\n                type=\"submit\"\r\n                disabled={isLoading}\r\n                className=\"gap-2\"\r\n              >\r\n                {isLoading && <Loader2 className=\"h-4 w-4 animate-spin\" />}\r\n                {isLoading ? 'Đang lưu...' : isEditing ? 'Cập nhật' : 'Tạo mới'}\r\n              </Button>\r\n            </DialogFooter>\r\n          </form>\r\n        </Form>\r\n      </DialogContent>\r\n    </Dialog>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAQA;AAQA;AACA;AACA;AACA;AA7BA;;;;;;;;;;;;;;;AAuCA,MAAM,aAAa,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,EAAE;IAC1B,OAAO,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IACzB,WAAW,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC7B,SAAS,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,GAAG,CAAC,GAAG;IAC3B,QAAQ,CAAA,GAAA,oIAAA,CAAA,SAAQ,AAAD,IAAI,QAAQ,GAAG,QAAQ;AACxC,GAAG,MAAM,CAAC,CAAA;IACR,OAAO,KAAK,OAAO,GAAG,KAAK,SAAS;AACtC,GAAG;IACD,SAAS;IACT,MAAM;QAAC;KAAU;AACnB;AAIO,SAAS,cAAc,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAsB;IAC9F,MAAM,oBAAoB,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAC,CAAC;IACpB,MAAM,WAAW,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM;IAE9B,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAc;QAC/B,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,WAAW,GAAG,SAAS,MAAM,CAAC;YAC9B,SAAS,GAAG,SAAS,MAAM,CAAC;YAC5B,QAAQ;QACV;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,WAAW;YACb,KAAK,KAAK,CAAC;gBACT,OAAO,UAAU,KAAK;gBACtB,WAAW,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,SAAS,GAAG;gBACjD,SAAS,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,OAAO,GAAG;gBAC7C,QAAQ,UAAU,MAAM,IAAI;YAC9B;QACF,OAAO;YACL,4DAA4D;YAC5D,KAAK,KAAK,CAAC;gBACT,OAAO;gBACP,WAAW,GAAG,SAAS,MAAM,CAAC;gBAC9B,SAAS,GAAG,SAAS,MAAM,CAAC;gBAC5B,QAAQ;YACV;QACF;IACF,GAAG;QAAC;QAAW;QAAM;KAAS;IAE9B,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,SAAS;QAET,IAAI;YACF,IAAI,aAAa,WAAW;gBAC1B,8BAA8B;gBAC9B,MAAM,kJAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;oBACrC,GAAG,SAAS;oBACZ,OAAO,OAAO,KAAK;oBACnB,WAAW,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW;oBACjD,SAAS,IAAI,KAAK,OAAO,OAAO,EAAE,WAAW;oBAC7C,QAAQ,OAAO,MAAM,KAAK,SAAS,OAAO,OAAO,MAAM;gBACzD;YACF,OAAO;gBACL,oCAAoC;gBACpC,MAAM,SAAS,OAAO,MAAM,KAAK,UAAU,OAAO,MAAM,KAAK,KAAK,OAAO,OAAO,MAAM;gBACtF,QAAQ,GAAG,CAAC,6CAA6C;gBAEzD,MAAM,kJAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;oBACrC,OAAO,OAAO,KAAK;oBACnB,WAAW,IAAI,KAAK,OAAO,SAAS,EAAE,WAAW;oBACjD,SAAS,IAAI,KAAK,OAAO,OAAO,EAAE,WAAW;oBAC7C,aAAa;oBACb,QAAQ;gBACV;YACF;YAEA,6BAA6B;YAC7B;YAEA;YACA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,SAAS;QACX,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCACT,YAAY,6BAA6B;;;;;;sCAE5C,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,YACG,uCACA;;;;;;;;;;;;gBAIP,uBACC,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAIL,8OAAC,gIAAA,CAAA,OAAI;oBAAE,GAAG,IAAI;8BACZ,cAAA,8OAAC;wBAAK,UAAU,KAAK,YAAY,CAAC;wBAAW,WAAU;;0CACrD,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;oDAAC,aAAY;oDAA0B,GAAG,KAAK;;;;;;;;;;;0DAEvD,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,MAAK;4DAAkB,GAAG,KAAK;;;;;;;;;;;kEAExC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;kEACX,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DAAC,MAAK;4DAAkB,GAAG,KAAK;;;;;;;;;;;kEAExC,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0CAMpB,8OAAC,gIAAA,CAAA,YAAS;gCACR,SAAS,KAAK,OAAO;gCACrB,MAAK;gCACL,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE,KAAK,EAAE,EAAE,iBACrC,8OAAC,gIAAA,CAAA,WAAQ;;0DACP,8OAAC,gIAAA,CAAA,YAAS;0DAAC;;;;;;0DACX,8OAAC,gIAAA,CAAA,cAAW;0DACV,cAAA,8OAAC,8IAAA,CAAA,eAAY;oDACX,OAAO,SAAS;oDAChB,UAAU;;;;;;;;;;;0DAGd,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;0CAKlB,8OAAC,kIAAA,CAAA,eAAY;;kDACX,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,SAAQ;wCACR,SAAS;wCACT,UAAU;kDACX;;;;;;kDAGD,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,UAAU;wCACV,WAAU;;4CAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;4CAChC,YAAY,gBAAgB,YAAY,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE", "debugId": null}}, {"offset": {"line": 4998, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/timeblock/time-block-item.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { TimeBlock, Task } from '@/lib/types';\r\nimport { Button } from '@/components/ui/button';\r\nimport { Checkbox } from '@/components/ui/checkbox';\r\nimport { TimeBlockService } from '@/lib/services/time-block-service';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport { ApiService } from '@/lib/services/api-service';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { \r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from '@/components/ui/tooltip';\r\nimport { Edit, Trash2, Clock, LinkIcon, Loader2 } from 'lucide-react';\r\nimport { TimeBlockForm } from './time-block-form';\r\n\r\ninterface TimeBlockItemProps {\r\n  timeBlock: TimeBlock;\r\n  onUpdate: () => void;\r\n}\r\n\r\nexport function TimeBlockItem({ timeBlock, onUpdate }: TimeBlockItemProps) {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);\r\n  const [linkedTask, setLinkedTask] = useState<Task | undefined>(undefined);\r\n  const [taskLoading, setTaskLoading] = useState<boolean>(false);\r\n  \r\n  // Tải thông tin công việc liên kết nếu có\r\n  useEffect(() => {\r\n    const loadLinkedTask = async () => {\r\n      if (!timeBlock.taskId || typeof timeBlock.taskId !== 'string') return;\r\n\r\n      setTaskLoading(true);\r\n      try {\r\n        console.log('TimeBlockItem: Đang tải task với ID:', timeBlock.taskId);\r\n        const task = await TaskService.getTask(timeBlock.taskId);\r\n        setLinkedTask(task);\r\n        if (task) {\r\n          console.log('TimeBlockItem: Đã tải task:', task.title);\r\n        } else {\r\n          console.log('TimeBlockItem: Task không tồn tại hoặc đã bị xóa');\r\n        }\r\n      } catch (error) {\r\n        console.error('Lỗi khi tải thông tin công việc liên kết:', error);\r\n        setLinkedTask(undefined);\r\n      } finally {\r\n        setTaskLoading(false);\r\n      }\r\n    };\r\n\r\n    loadLinkedTask();\r\n  }, [timeBlock.taskId]);\r\n  \r\n  const handleToggleComplete = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      console.log('TimeBlockItem: Toggling completion for:', timeBlock.id, 'current:', timeBlock.isCompleted);\r\n      console.log('TimeBlockItem: Calling toggleCompletion with id:', timeBlock.id, 'newValue:', !timeBlock.isCompleted);\r\n      await ApiService.timeBlocks.toggleCompletion(timeBlock.id, !timeBlock.isCompleted);\r\n      onUpdate();\r\n    } catch (error) {\r\n      console.error('Lỗi khi cập nhật khối thời gian:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  \r\n  const handleDelete = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      await TimeBlockService.deleteTimeBlock(timeBlock.id);\r\n      onUpdate();\r\n    } catch (error) {\r\n      console.error('Lỗi khi xóa khối thời gian:', error);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n  \r\n  // Tính thời gian\r\n  const startTime = format(new Date(timeBlock.startTime), 'HH:mm', { locale: vi });\r\n  const endTime = format(new Date(timeBlock.endTime), 'HH:mm', { locale: vi });\r\n  \r\n  // Tính thời lượng (phút)\r\n  const durationInMinutes = Math.round(\r\n    (new Date(timeBlock.endTime).getTime() - new Date(timeBlock.startTime).getTime()) / (1000 * 60)\r\n  );\r\n  \r\n  // Chuyển đổi sang giờ và phút\r\n  const hours = Math.floor(durationInMinutes / 60);\r\n  const minutes = durationInMinutes % 60;\r\n  const durationText = hours > 0 \r\n    ? `${hours} giờ ${minutes > 0 ? `${minutes} phút` : ''}`\r\n    : `${minutes} phút`;\r\n  \r\n  return (\r\n    <>\r\n      <div \r\n        className={`rounded-lg border p-4 ${\r\n          timeBlock.isCompleted \r\n            ? 'bg-muted/40' \r\n            : 'bg-card'\r\n        }`}\r\n      >\r\n        <div className=\"flex items-start gap-4\">\r\n          <Checkbox \r\n            id={`timeblock-${timeBlock.id}`} \r\n            checked={timeBlock.isCompleted}\r\n            onCheckedChange={handleToggleComplete}\r\n            disabled={isLoading}\r\n            className=\"mt-1\"\r\n          />\r\n          <div className=\"grid flex-1 gap-1\">\r\n            <div className=\"flex flex-wrap items-start justify-between gap-2\">\r\n              <h3 className={`font-medium ${timeBlock.isCompleted ? 'line-through text-muted-foreground' : ''}`}>\r\n                {timeBlock.title}\r\n              </h3>\r\n              \r\n              <div className=\"flex flex-shrink-0 gap-1\">\r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\" \r\n                        size=\"icon\"\r\n                        onClick={() => setIsEditDialogOpen(true)}\r\n                        disabled={isLoading}\r\n                        className=\"h-8 w-8 text-muted-foreground\"\r\n                      >\r\n                        <Edit className=\"h-4 w-4\" />\r\n                        <span className=\"sr-only\">Chỉnh sửa</span>\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>Chỉnh sửa khối thời gian</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n                \r\n                <TooltipProvider>\r\n                  <Tooltip>\r\n                    <TooltipTrigger asChild>\r\n                      <Button\r\n                        variant=\"ghost\" \r\n                        size=\"icon\"\r\n                        onClick={handleDelete}\r\n                        disabled={isLoading}\r\n                        className=\"h-8 w-8 text-muted-foreground hover:text-destructive\"\r\n                      >\r\n                        <Trash2 className=\"h-4 w-4\" />\r\n                        <span className=\"sr-only\">Xóa</span>\r\n                      </Button>\r\n                    </TooltipTrigger>\r\n                    <TooltipContent>\r\n                      <p>Xóa khối thời gian</p>\r\n                    </TooltipContent>\r\n                  </Tooltip>\r\n                </TooltipProvider>\r\n              </div>\r\n            </div>\r\n            \r\n            <div className=\"flex items-center gap-1 text-sm text-muted-foreground\">\r\n              <Clock className=\"h-3.5 w-3.5\" />\r\n              <span>{startTime} - {endTime}</span>\r\n              <span className=\"text-xs px-1 rounded-full bg-muted\">\r\n                {durationText}\r\n              </span>\r\n            </div>\r\n            \r\n            {timeBlock.taskId && (\r\n              <div className=\"mt-1 flex items-center gap-1 text-sm\">\r\n                <LinkIcon className=\"h-3.5 w-3.5 text-blue-500\" />\r\n                {taskLoading ? (\r\n                  <div className=\"flex items-center\">\r\n                    <Loader2 className=\"h-3 w-3 animate-spin mr-1\" />\r\n                    <span className=\"text-muted-foreground\">Đang tải...</span>\r\n                  </div>\r\n                ) : linkedTask ? (\r\n                  <span className=\"text-blue-600 dark:text-blue-400\">\r\n                    {linkedTask.title}\r\n                  </span>\r\n                ) : (\r\n                  <span className=\"text-orange-600 dark:text-orange-400 text-xs\">\r\n                    Công việc đã bị xóa hoặc không tồn tại\r\n                  </span>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n      \r\n      <TimeBlockForm\r\n        isOpen={isEditDialogOpen}\r\n        onClose={() => setIsEditDialogOpen(false)}\r\n        onUpdate={onUpdate}\r\n        date={new Date(timeBlock.startTime)}\r\n        timeBlock={timeBlock}\r\n      />\r\n    </>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;AAlBA;;;;;;;;;;;;;AAyBO,SAAS,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAsB;IACvE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAC/D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAExD,0CAA0C;IAC1C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,IAAI,CAAC,UAAU,MAAM,IAAI,OAAO,UAAU,MAAM,KAAK,UAAU;YAE/D,eAAe;YACf,IAAI;gBACF,QAAQ,GAAG,CAAC,wCAAwC,UAAU,MAAM;gBACpE,MAAM,OAAO,MAAM,yIAAA,CAAA,cAAW,CAAC,OAAO,CAAC,UAAU,MAAM;gBACvD,cAAc;gBACd,IAAI,MAAM;oBACR,QAAQ,GAAG,CAAC,+BAA+B,KAAK,KAAK;gBACvD,OAAO;oBACL,QAAQ,GAAG,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6CAA6C;gBAC3D,cAAc;YAChB,SAAU;gBACR,eAAe;YACjB;QACF;QAEA;IACF,GAAG;QAAC,UAAU,MAAM;KAAC;IAErB,MAAM,uBAAuB;QAC3B,aAAa;QACb,IAAI;YACF,QAAQ,GAAG,CAAC,2CAA2C,UAAU,EAAE,EAAE,YAAY,UAAU,WAAW;YACtG,QAAQ,GAAG,CAAC,oDAAoD,UAAU,EAAE,EAAE,aAAa,CAAC,UAAU,WAAW;YACjH,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC,UAAU,EAAE,EAAE,CAAC,UAAU,WAAW;YACjF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,MAAM,kJAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,UAAU,EAAE;YACnD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,aAAa;QACf;IACF;IAEA,iBAAiB;IACjB,MAAM,YAAY,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,SAAS,GAAG,SAAS;QAAE,QAAQ,2IAAA,CAAA,KAAE;IAAC;IAC9E,MAAM,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,OAAO,GAAG,SAAS;QAAE,QAAQ,2IAAA,CAAA,KAAE;IAAC;IAE1E,yBAAyB;IACzB,MAAM,oBAAoB,KAAK,KAAK,CAClC,CAAC,IAAI,KAAK,UAAU,OAAO,EAAE,OAAO,KAAK,IAAI,KAAK,UAAU,SAAS,EAAE,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;IAGhG,8BAA8B;IAC9B,MAAM,QAAQ,KAAK,KAAK,CAAC,oBAAoB;IAC7C,MAAM,UAAU,oBAAoB;IACpC,MAAM,eAAe,QAAQ,IACzB,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,GAAG,QAAQ,KAAK,CAAC,GAAG,IAAI,GACtD,GAAG,QAAQ,KAAK,CAAC;IAErB,qBACE;;0BACE,8OAAC;gBACC,WAAW,CAAC,sBAAsB,EAChC,UAAU,WAAW,GACjB,gBACA,WACJ;0BAEF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;4BAC/B,SAAS,UAAU,WAAW;4BAC9B,iBAAiB;4BACjB,UAAU;4BACV,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAW,CAAC,YAAY,EAAE,UAAU,WAAW,GAAG,uCAAuC,IAAI;sDAC9F,UAAU,KAAK;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,mIAAA,CAAA,kBAAe;8DACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0EACN,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,oBAAoB;oEACnC,UAAU;oEACV,WAAU;;sFAEV,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;0EAG9B,8OAAC,mIAAA,CAAA,iBAAc;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;8DAKT,8OAAC,mIAAA,CAAA,kBAAe;8DACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0EACN,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS;oEACT,UAAU;oEACV,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;0EAG9B,8OAAC,mIAAA,CAAA,iBAAc;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;;gDAAM;gDAAU;gDAAI;;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDACb;;;;;;;;;;;;gCAIJ,UAAU,MAAM,kBACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,sMAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCACnB,4BACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;oDAAK,WAAU;8DAAwB;;;;;;;;;;;mDAExC,2BACF,8OAAC;4CAAK,WAAU;sDACb,WAAW,KAAK;;;;;iEAGnB,8OAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3E,8OAAC,wJAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,UAAU;gBACV,MAAM,IAAI,KAAK,UAAU,SAAS;gBAClC,WAAW;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 5394, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/timeblock-service.ts"], "sourcesContent": ["import { ApiService } from './api-service';\nimport { TimeBlock } from '@/lib/types';\n\nexport interface CreateTimeBlockData {\n  title: string;\n  description?: string;\n  startTime: string;\n  endTime: string;\n  taskId?: string;\n  isCompleted?: boolean;\n}\n\nexport interface UpdateTimeBlockData {\n  title?: string;\n  description?: string;\n  startTime?: string;\n  endTime?: string;\n  taskId?: string;\n  isCompleted?: boolean;\n}\n\nexport const TimeBlockService = {\n  // Lấy tất cả time blocks\n  getTimeBlocks: async (): Promise<TimeBlock[]> => {\n    try {\n      console.log('TimeBlockService: Đang tải time blocks...');\n      const timeBlocks = await ApiService.timeBlocks.getAll();\n      console.log('TimeBlockService: Đã tải time blocks:', timeBlocks);\n      return timeBlocks;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi tải time blocks:', error);\n      throw error;\n    }\n  },\n\n  // Lấy time blocks theo ngày\n  getTimeBlocksByDate: async (date: string): Promise<TimeBlock[]> => {\n    try {\n      console.log('TimeBlockService: Đang tải time blocks cho ngày...', date);\n      const timeBlocks = await ApiService.timeBlocks.getByDate(date);\n      console.log('TimeBlockService: Đã tải time blocks cho ngày:', timeBlocks);\n      return timeBlocks;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi tải time blocks cho ngày:', error);\n      throw error;\n    }\n  },\n\n  // Lấy time block theo ID\n  getTimeBlockById: async (id: string): Promise<TimeBlock> => {\n    try {\n      console.log('TimeBlockService: Đang tải time block...', id);\n      const timeBlock = await ApiService.timeBlocks.getById(id);\n      console.log('TimeBlockService: Đã tải time block:', timeBlock);\n      return timeBlock;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi tải time block:', error);\n      throw error;\n    }\n  },\n\n  // Tạo time block mới\n  createTimeBlock: async (data: CreateTimeBlockData): Promise<TimeBlock> => {\n    try {\n      console.log('TimeBlockService: Đang tạo time block...', data);\n      \n      // Validate dữ liệu\n      if (!data.title.trim()) {\n        throw new Error('Tiêu đề không được để trống');\n      }\n      \n      if (!data.startTime || !data.endTime) {\n        throw new Error('Thời gian bắt đầu và kết thúc không được để trống');\n      }\n      \n      const startTime = new Date(data.startTime);\n      const endTime = new Date(data.endTime);\n      \n      if (startTime >= endTime) {\n        throw new Error('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');\n      }\n      \n      const timeBlock = await ApiService.timeBlocks.create({\n        title: data.title.trim(),\n        startTime: data.startTime,\n        endTime: data.endTime,\n        taskId: data.taskId,\n        isCompleted: data.isCompleted || false,\n      });\n      \n      console.log('TimeBlockService: Đã tạo time block:', timeBlock);\n      return timeBlock;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi tạo time block:', error);\n      throw error;\n    }\n  },\n\n  // Cập nhật time block\n  updateTimeBlock: async (id: string, data: UpdateTimeBlockData): Promise<TimeBlock> => {\n    try {\n      console.log('TimeBlockService: Đang cập nhật time block...', 'id:', id, 'data:', data);\n      console.log('TimeBlockService: id type:', typeof id, 'data type:', typeof data);\n      \n      // Validate dữ liệu nếu có\n      if (data.title !== undefined && !data.title.trim()) {\n        throw new Error('Tiêu đề không được để trống');\n      }\n      \n      if (data.startTime && data.endTime) {\n        const startTime = new Date(data.startTime);\n        const endTime = new Date(data.endTime);\n        \n        if (startTime >= endTime) {\n          throw new Error('Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc');\n        }\n      }\n      \n      const updateData: any = {};\n      if (data.title !== undefined) updateData.title = data.title.trim();\n      if (data.startTime !== undefined) updateData.startTime = data.startTime;\n      if (data.endTime !== undefined) updateData.endTime = data.endTime;\n      if (data.taskId !== undefined) updateData.taskId = data.taskId;\n      if (data.isCompleted !== undefined) updateData.isCompleted = data.isCompleted;\n\n      console.log('TimeBlockService: Final updateData:', updateData);\n      const timeBlock = await ApiService.timeBlocks.update(id, updateData);\n      console.log('TimeBlockService: Đã cập nhật time block:', timeBlock);\n      return timeBlock;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi cập nhật time block:', error);\n      throw error;\n    }\n  },\n\n  // Xóa time block\n  deleteTimeBlock: async (id: string): Promise<void> => {\n    try {\n      console.log('TimeBlockService: Đang xóa time block...', id);\n      await ApiService.timeBlocks.delete(id);\n      console.log('TimeBlockService: Đã xóa time block:', id);\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi xóa time block:', error);\n      throw error;\n    }\n  },\n\n  // Toggle completion status\n  toggleCompletion: async (id: string): Promise<TimeBlock> => {\n    try {\n      console.log('TimeBlockService: Đang toggle completion...', id);\n\n      // Lấy time block hiện tại để biết trạng thái\n      const currentTimeBlock = await TimeBlockService.getTimeBlockById(id);\n\n      // Gọi API toggle completion trực tiếp\n      const updatedTimeBlock = await ApiService.timeBlocks.toggleCompletion(id, !currentTimeBlock.isCompleted);\n\n      console.log('TimeBlockService: Đã toggle completion:', updatedTimeBlock);\n      return updatedTimeBlock;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi toggle completion:', error);\n      throw error;\n    }\n  },\n\n  // Lấy time blocks trong khoảng thời gian\n  getTimeBlocksInRange: async (startDate: string, endDate: string): Promise<TimeBlock[]> => {\n    try {\n      console.log('TimeBlockService: Đang tải time blocks trong khoảng...', { startDate, endDate });\n      \n      // Lấy tất cả time blocks và filter theo client\n      const allTimeBlocks = await TimeBlockService.getTimeBlocks();\n      \n      const filteredTimeBlocks = allTimeBlocks.filter(timeBlock => {\n        const blockDate = new Date(timeBlock.startTime).toISOString().split('T')[0];\n        return blockDate >= startDate && blockDate <= endDate;\n      });\n      \n      console.log('TimeBlockService: Đã filter time blocks:', filteredTimeBlocks);\n      return filteredTimeBlocks;\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi tải time blocks trong khoảng:', error);\n      throw error;\n    }\n  },\n\n  // Kiểm tra xung đột thời gian\n  checkTimeConflict: async (startTime: string, endTime: string, excludeId?: string): Promise<boolean> => {\n    try {\n      const date = new Date(startTime).toISOString().split('T')[0];\n      const timeBlocks = await TimeBlockService.getTimeBlocksByDate(date);\n      \n      const newStart = new Date(startTime);\n      const newEnd = new Date(endTime);\n      \n      for (const timeBlock of timeBlocks) {\n        // Bỏ qua time block đang được cập nhật\n        if (excludeId && timeBlock.id === excludeId) continue;\n        \n        const existingStart = new Date(timeBlock.startTime);\n        const existingEnd = new Date(timeBlock.endTime);\n        \n        // Kiểm tra xung đột\n        if (\n          (newStart >= existingStart && newStart < existingEnd) ||\n          (newEnd > existingStart && newEnd <= existingEnd) ||\n          (newStart <= existingStart && newEnd >= existingEnd)\n        ) {\n          return true; // Có xung đột\n        }\n      }\n      \n      return false; // Không có xung đột\n    } catch (error) {\n      console.error('TimeBlockService: Lỗi khi kiểm tra xung đột:', error);\n      return false; // Mặc định không có xung đột nếu lỗi\n    }\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AAqBO,MAAM,mBAAmB;IAC9B,yBAAyB;IACzB,eAAe;QACb,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;YACrD,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,qBAAqB,OAAO;QAC1B,IAAI;YACF,QAAQ,GAAG,CAAC,sDAAsD;YAClE,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,SAAS,CAAC;YACzD,QAAQ,GAAG,CAAC,kDAAkD;YAC9D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uDAAuD;YACrE,MAAM;QACR;IACF;IAEA,yBAAyB;IACzB,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,4CAA4C;YACxD,MAAM,YAAY,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,OAAO,CAAC;YACtD,QAAQ,GAAG,CAAC,wCAAwC;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,iBAAiB,OAAO;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,4CAA4C;YAExD,mBAAmB;YACnB,IAAI,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI;gBACtB,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,KAAK,SAAS,IAAI,CAAC,KAAK,OAAO,EAAE;gBACpC,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,YAAY,IAAI,KAAK,KAAK,SAAS;YACzC,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO;YAErC,IAAI,aAAa,SAAS;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,YAAY,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;gBACnD,OAAO,KAAK,KAAK,CAAC,IAAI;gBACtB,WAAW,KAAK,SAAS;gBACzB,SAAS,KAAK,OAAO;gBACrB,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW,IAAI;YACnC;YAEA,QAAQ,GAAG,CAAC,wCAAwC;YACpD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,iBAAiB,OAAO,IAAY;QAClC,IAAI;YACF,QAAQ,GAAG,CAAC,iDAAiD,OAAO,IAAI,SAAS;YACjF,QAAQ,GAAG,CAAC,8BAA8B,OAAO,IAAI,cAAc,OAAO;YAE1E,0BAA0B;YAC1B,IAAI,KAAK,KAAK,KAAK,aAAa,CAAC,KAAK,KAAK,CAAC,IAAI,IAAI;gBAClD,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,KAAK,SAAS,IAAI,KAAK,OAAO,EAAE;gBAClC,MAAM,YAAY,IAAI,KAAK,KAAK,SAAS;gBACzC,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO;gBAErC,IAAI,aAAa,SAAS;oBACxB,MAAM,IAAI,MAAM;gBAClB;YACF;YAEA,MAAM,aAAkB,CAAC;YACzB,IAAI,KAAK,KAAK,KAAK,WAAW,WAAW,KAAK,GAAG,KAAK,KAAK,CAAC,IAAI;YAChE,IAAI,KAAK,SAAS,KAAK,WAAW,WAAW,SAAS,GAAG,KAAK,SAAS;YACvE,IAAI,KAAK,OAAO,KAAK,WAAW,WAAW,OAAO,GAAG,KAAK,OAAO;YACjE,IAAI,KAAK,MAAM,KAAK,WAAW,WAAW,MAAM,GAAG,KAAK,MAAM;YAC9D,IAAI,KAAK,WAAW,KAAK,WAAW,WAAW,WAAW,GAAG,KAAK,WAAW;YAE7E,QAAQ,GAAG,CAAC,uCAAuC;YACnD,MAAM,YAAY,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI;YACzD,QAAQ,GAAG,CAAC,6CAA6C;YACzD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA,iBAAiB;IACjB,iBAAiB,OAAO;QACtB,IAAI;YACF,QAAQ,GAAG,CAAC,4CAA4C;YACxD,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;YACnC,QAAQ,GAAG,CAAC,wCAAwC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6CAA6C;YAC3D,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,kBAAkB,OAAO;QACvB,IAAI;YACF,QAAQ,GAAG,CAAC,+CAA+C;YAE3D,6CAA6C;YAC7C,MAAM,mBAAmB,MAAM,iBAAiB,gBAAgB,CAAC;YAEjE,sCAAsC;YACtC,MAAM,mBAAmB,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,gBAAgB,CAAC,IAAI,CAAC,iBAAiB,WAAW;YAEvG,QAAQ,GAAG,CAAC,2CAA2C;YACvD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,sBAAsB,OAAO,WAAmB;QAC9C,IAAI;YACF,QAAQ,GAAG,CAAC,0DAA0D;gBAAE;gBAAW;YAAQ;YAE3F,+CAA+C;YAC/C,MAAM,gBAAgB,MAAM,iBAAiB,aAAa;YAE1D,MAAM,qBAAqB,cAAc,MAAM,CAAC,CAAA;gBAC9C,MAAM,YAAY,IAAI,KAAK,UAAU,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBAC3E,OAAO,aAAa,aAAa,aAAa;YAChD;YAEA,QAAQ,GAAG,CAAC,4CAA4C;YACxD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2DAA2D;YACzE,MAAM;QACR;IACF;IAEA,8BAA8B;IAC9B,mBAAmB,OAAO,WAAmB,SAAiB;QAC5D,IAAI;YACF,MAAM,OAAO,IAAI,KAAK,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC5D,MAAM,aAAa,MAAM,iBAAiB,mBAAmB,CAAC;YAE9D,MAAM,WAAW,IAAI,KAAK;YAC1B,MAAM,SAAS,IAAI,KAAK;YAExB,KAAK,MAAM,aAAa,WAAY;gBAClC,uCAAuC;gBACvC,IAAI,aAAa,UAAU,EAAE,KAAK,WAAW;gBAE7C,MAAM,gBAAgB,IAAI,KAAK,UAAU,SAAS;gBAClD,MAAM,cAAc,IAAI,KAAK,UAAU,OAAO;gBAE9C,oBAAoB;gBACpB,IACE,AAAC,YAAY,iBAAiB,WAAW,eACxC,SAAS,iBAAiB,UAAU,eACpC,YAAY,iBAAiB,UAAU,aACxC;oBACA,OAAO,MAAM,cAAc;gBAC7B;YACF;YAEA,OAAO,OAAO,oBAAoB;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gDAAgD;YAC9D,OAAO,OAAO,qCAAqC;QACrD;IACF;AACF", "debugId": null}}, {"offset": {"line": 5573, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 5597, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/timeblock/time-block-list.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect } from 'react';\r\nimport { TimeBlock } from '@/lib/types';\r\nimport { TimeBlockItem } from './time-block-item';\r\nimport { TimeBlockForm } from './time-block-form';\r\nimport { Button } from '@/components/ui/button';\r\nimport { TimeBlockService } from '@/lib/services/timeblock-service';\r\nimport { format } from 'date-fns';\r\nimport { vi } from 'date-fns/locale';\r\nimport { PlusCircle, Loader2, RefreshCw } from 'lucide-react';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\n\r\ninterface TimeBlockListProps {\r\n  date: Date;\r\n  compact?: boolean; // Chế độ hiển thị nhỏ gọn cho chế độ xem tuần\r\n  onDataChange?: () => void; // Callback khi có thay đổi dữ liệu\r\n}\r\n\r\nexport function TimeBlockList({ date, compact = false, onDataChange }: TimeBlockListProps) {\r\n  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);\r\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  \r\n  useEffect(() => {\r\n    loadTimeBlocks();\r\n  }, [date]);\r\n  \r\n  const loadTimeBlocks = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const dateString = date.toISOString().split('T')[0];\r\n      console.log('TimeBlockList: Loading time blocks for date:', dateString);\r\n      const blocks = await TimeBlockService.getTimeBlocksByDate(dateString);\r\n      blocks.sort((a, b) => new Date(a.startTime).getTime() - new Date(b.startTime).getTime());\r\n      console.log('TimeBlockList: Loaded time blocks:', blocks);\r\n      setTimeBlocks(blocks);\r\n      setError(null);\r\n    } catch (err) {\r\n      console.error('Lỗi khi tải danh sách khối thời gian:', err);\r\n      setError('Không thể tải danh sách khối thời gian. Vui lòng thử lại sau.');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n  \r\n  const formatDate = format(date, 'EEEE, dd/MM/yyyy', { locale: vi });\r\n  \r\n  if (compact) {\r\n    return (\r\n      <div className=\"space-y-3\">\r\n        {loading ? (\r\n          <div className=\"space-y-2\">\r\n            <Skeleton className=\"h-6 w-full\" />\r\n            <Skeleton className=\"h-6 w-3/4\" />\r\n          </div>\r\n        ) : error ? (\r\n          <div className=\"text-center py-2\">\r\n            <p className=\"text-xs text-destructive\">{error}</p>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full mt-1 h-8 text-xs\"\r\n              onClick={loadTimeBlocks}\r\n            >\r\n              <RefreshCw className=\"h-3 w-3 mr-1\" />\r\n              Thử lại\r\n            </Button>\r\n          </div>\r\n        ) : timeBlocks.length === 0 ? (\r\n          <div className=\"text-center py-2\">\r\n            <p className=\"text-xs text-muted-foreground\">Không có khối thời gian</p>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full mt-1 h-8 text-xs\"\r\n              onClick={() => setIsDialogOpen(true)}\r\n            >\r\n              <PlusCircle className=\"h-3 w-3 mr-1\" />\r\n              Thêm\r\n            </Button>\r\n          </div>\r\n        ) : (\r\n          <>\r\n            {timeBlocks.filter(timeBlock => timeBlock.id).map(timeBlock => (\r\n              <div\r\n                key={timeBlock.id}\r\n                className={`p-2 text-xs rounded border ${\r\n                  timeBlock.isCompleted ? 'bg-muted/50 text-muted-foreground' : 'bg-card'\r\n                }`}\r\n              >\r\n                <div className=\"font-medium truncate\">{timeBlock.title}</div>\r\n                <div className=\"text-muted-foreground\">\r\n                  {format(new Date(timeBlock.startTime), 'HH:mm', { locale: vi })} - \r\n                  {format(new Date(timeBlock.endTime), 'HH:mm', { locale: vi })}\r\n                </div>\r\n              </div>\r\n            ))}\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"sm\"\r\n              className=\"w-full h-8 text-xs\"\r\n              onClick={() => setIsDialogOpen(true)}\r\n            >\r\n              <PlusCircle className=\"h-3 w-3 mr-1\" />\r\n              Thêm khối thời gian\r\n            </Button>\r\n          </>\r\n        )}\r\n        \r\n        <TimeBlockForm\r\n          isOpen={isDialogOpen}\r\n          onClose={() => setIsDialogOpen(false)}\r\n          onUpdate={() => {\r\n            loadTimeBlocks();\r\n            onDataChange?.();\r\n          }}\r\n          date={date}\r\n        />\r\n      </div>\r\n    );\r\n  }\r\n  \r\n  return (\r\n    <div className=\"space-y-4\">\r\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\r\n        {!compact && <h2 className=\"text-xl font-semibold capitalize\">{formatDate}</h2>}\r\n        <Button \r\n          onClick={() => setIsDialogOpen(true)}\r\n          className=\"ml-auto flex items-center gap-2\"\r\n        >\r\n          <PlusCircle className=\"h-4 w-4\" />\r\n          Thêm khối thời gian\r\n        </Button>\r\n      </div>\r\n      \r\n      {loading ? (\r\n        <div className=\"space-y-4\">\r\n          <Skeleton className=\"h-20 w-full\" />\r\n          <Skeleton className=\"h-20 w-full\" />\r\n          <Skeleton className=\"h-20 w-full\" />\r\n        </div>\r\n      ) : error ? (\r\n        <div className=\"rounded-lg border bg-destructive/10 text-destructive p-4\">\r\n          <div className=\"flex flex-col items-center justify-center space-y-3 p-4 text-center\">\r\n            <p>{error}</p>\r\n            <Button \r\n              onClick={loadTimeBlocks}\r\n              variant=\"outline\"\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <RefreshCw className=\"h-4 w-4\" />\r\n              Thử lại\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      ) : timeBlocks.length === 0 ? (\r\n        <div className=\"rounded-lg border bg-card text-card-foreground shadow-sm\" data-v0-t=\"card\">\r\n          <div className=\"flex flex-col items-center justify-center space-y-3 p-8 text-center\">\r\n            <div className=\"rounded-full bg-primary/10 p-3\">\r\n              <svg\r\n                xmlns=\"http://www.w3.org/2000/svg\"\r\n                width=\"24\"\r\n                height=\"24\"\r\n                viewBox=\"0 0 24 24\"\r\n                fill=\"none\"\r\n                stroke=\"currentColor\"\r\n                strokeWidth=\"2\"\r\n                strokeLinecap=\"round\"\r\n                strokeLinejoin=\"round\"\r\n                className=\"h-6 w-6 text-primary\"\r\n              >\r\n                <rect width=\"18\" height=\"18\" x=\"3\" y=\"3\" rx=\"2\" />\r\n                <path d=\"M3 9h18\" />\r\n                <path d=\"M9 21V9\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"space-y-1\">\r\n              <h3 className=\"text-lg font-semibold\">Không có khối thời gian nào</h3>\r\n              <p className=\"text-sm text-muted-foreground\">\r\n                Không có khối thời gian nào cho ngày này. Hãy tạo khối thời gian mới.\r\n              </p>\r\n            </div>\r\n            <Button \r\n              onClick={() => setIsDialogOpen(true)}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <PlusCircle className=\"h-4 w-4\" />\r\n              Thêm khối thời gian mới\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <div className=\"grid grid-cols-1 gap-4\">\r\n          {timeBlocks.filter(timeBlock => timeBlock.id).map(timeBlock => (\r\n            <TimeBlockItem key={timeBlock.id} timeBlock={timeBlock} onUpdate={loadTimeBlocks} />\r\n          ))}\r\n        </div>\r\n      )}\r\n      \r\n      <TimeBlockForm\r\n        isOpen={isDialogOpen}\r\n        onClose={() => setIsDialogOpen(false)}\r\n        onUpdate={() => {\r\n          loadTimeBlocks();\r\n          onDataChange?.();\r\n        }}\r\n        date={date}\r\n      />\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AAXA;;;;;;;;;;;AAmBO,SAAS,cAAc,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,YAAY,EAAsB;IACvF,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAK;IAET,MAAM,iBAAiB;QACrB,WAAW;QACX,IAAI;YACF,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACnD,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,MAAM,SAAS,MAAM,8IAAA,CAAA,mBAAgB,CAAC,mBAAmB,CAAC;YAC1D,OAAO,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;YACrF,QAAQ,GAAG,CAAC,sCAAsC;YAClD,cAAc;YACd,SAAS;QACX,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,yCAAyC;YACvD,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,oBAAoB;QAAE,QAAQ,2IAAA,CAAA,KAAE;IAAC;IAEjE,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;;gBACZ,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;sCACpB,8OAAC,oIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;2BAEpB,sBACF,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA4B;;;;;;sCACzC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;;8CAET,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;2BAIxC,WAAW,MAAM,KAAK,kBACxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAC7C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,gBAAgB;;8CAE/B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;yCAK3C;;wBACG,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,EAAE,EAAE,GAAG,CAAC,CAAA,0BAChD,8OAAC;gCAEC,WAAW,CAAC,2BAA2B,EACrC,UAAU,WAAW,GAAG,sCAAsC,WAC9D;;kDAEF,8OAAC;wCAAI,WAAU;kDAAwB,UAAU,KAAK;;;;;;kDACtD,8OAAC;wCAAI,WAAU;;4CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,SAAS,GAAG,SAAS;gDAAE,QAAQ,2IAAA,CAAA,KAAE;4CAAC;4CAAG;4CAC/D,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,UAAU,OAAO,GAAG,SAAS;gDAAE,QAAQ,2IAAA,CAAA,KAAE;4CAAC;;;;;;;;+BARxD,UAAU,EAAE;;;;;sCAYrB,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,gBAAgB;;8CAE/B,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;8BAM7C,8OAAC,wJAAA,CAAA,gBAAa;oBACZ,QAAQ;oBACR,SAAS,IAAM,gBAAgB;oBAC/B,UAAU;wBACR;wBACA;oBACF;oBACA,MAAM;;;;;;;;;;;;IAId;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;oBACZ,CAAC,yBAAW,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAC/D,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,gBAAgB;wBAC/B,WAAU;;0CAEV,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;YAKrC,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCACpB,8OAAC,oIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;uBAEpB,sBACF,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;sCAAG;;;;;;sCACJ,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAQ;4BACR,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;uBAKrC,WAAW,MAAM,KAAK,kBACxB,8OAAC;gBAAI,WAAU;gBAA2D,aAAU;0BAClF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,8OAAC;wCAAK,OAAM;wCAAK,QAAO;wCAAK,GAAE;wCAAI,GAAE;wCAAI,IAAG;;;;;;kDAC5C,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAI/C,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS,IAAM,gBAAgB;4BAC/B,WAAU;;8CAEV,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;qCAMxC,8OAAC;gBAAI,WAAU;0BACZ,WAAW,MAAM,CAAC,CAAA,YAAa,UAAU,EAAE,EAAE,GAAG,CAAC,CAAA,0BAChD,8OAAC,wJAAA,CAAA,gBAAa;wBAAoB,WAAW;wBAAW,UAAU;uBAA9C,UAAU,EAAE;;;;;;;;;;0BAKtC,8OAAC,wJAAA,CAAA,gBAAa;gBACZ,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,UAAU;oBACR;oBACA;gBACF;gBACA,MAAM;;;;;;;;;;;;AAId", "debugId": null}}, {"offset": {"line": 6085, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cva, type VariantProps } from \"class-variance-authority\";\nimport { cn } from \"@/lib/utils\";\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n        success: \n          \"border-transparent bg-green-500 text-white hover:bg-green-500/80\",\n        warning: \n          \"border-transparent bg-orange-500 text-white hover:bg-orange-500/80\",\n        info: \n          \"border-transparent bg-blue-500 text-white hover:bg-blue-500/80\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n);\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  );\n}\n\nexport { Badge, badgeVariants };\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;YACT,SACE;YACF,SACE;YACF,MACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 6130, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/calendar/calendar-events.tsx"], "sourcesContent": ["'use client';\n\nimport { CalendarEvent } from '@/lib/services/calendar-service';\nimport { format } from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport { Badge } from '@/components/ui/badge';\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\nimport { Clock, CheckCircle, AlertCircle } from 'lucide-react';\n\ninterface CalendarEventsProps {\n  events: CalendarEvent[];\n  date: Date;\n}\n\nexport function CalendarEvents({ events, date }: CalendarEventsProps) {\n  // Filter events cho ngày được chọn\n  const dateString = date.toISOString().split('T')[0];\n  const eventsForDate = events.filter(event => {\n    const eventDate = new Date(event.start).toISOString().split('T')[0];\n    return eventDate === dateString;\n  });\n\n  console.log('CalendarEvents: Rendering events for date:', dateString, eventsForDate);\n\n  if (eventsForDate.length === 0) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"text-lg\">\n            Sự kiện - {format(date, 'dd/MM/yyyy', { locale: vi })}\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"text-center py-6 text-muted-foreground\">\n            Không có sự kiện nào trong ngày này\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"text-lg\">\n          Sự kiện - {format(date, 'dd/MM/yyyy', { locale: vi })}\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        <div className=\"space-y-3\">\n          {eventsForDate.filter(event => event.id).map((event, index) => (\n            <div\n              key={event.id || `event-${index}`}\n              className={`p-3 rounded-lg border ${\n                event.completed ? 'bg-muted/50' : 'bg-card'\n              }`}\n              style={{ borderLeftColor: event.color, borderLeftWidth: '4px' }}\n            >\n              <div className=\"flex items-start justify-between\">\n                <div className=\"flex-1\">\n                  <div className=\"flex items-center gap-2\">\n                    {event.completed ? (\n                      <CheckCircle className=\"h-4 w-4 text-green-500\" />\n                    ) : (\n                      <AlertCircle className=\"h-4 w-4 text-orange-500\" />\n                    )}\n                    <h4 className={`font-medium ${\n                      event.completed ? 'line-through text-muted-foreground' : ''\n                    }`}>\n                      {event.title}\n                    </h4>\n                  </div>\n                  \n                  <div className=\"flex items-center gap-4 mt-2 text-sm text-muted-foreground\">\n                    <div className=\"flex items-center gap-1\">\n                      <Clock className=\"h-3 w-3\" />\n                      {event.end ? (\n                        <span>\n                          {format(new Date(event.start), 'HH:mm', { locale: vi })} - {' '}\n                          {format(new Date(event.end), 'HH:mm', { locale: vi })}\n                        </span>\n                      ) : (\n                        <span>\n                          {format(new Date(event.start), 'HH:mm dd/MM', { locale: vi })}\n                        </span>\n                      )}\n                    </div>\n                    \n                    <Badge variant=\"outline\" className=\"text-xs\">\n                      {event.type === 'task' ? 'Công việc' : 'Khối thời gian'}\n                    </Badge>\n                  </div>\n                  \n                  {event.type === 'task' && (event.data as any).description && (\n                    <p className=\"text-sm text-muted-foreground mt-2\">\n                      {(event.data as any).description}\n                    </p>\n                  )}\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      </CardContent>\n    </Card>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAPA;;;;;;;AAcO,SAAS,eAAe,EAAE,MAAM,EAAE,IAAI,EAAuB;IAClE,mCAAmC;IACnC,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACnD,MAAM,gBAAgB,OAAO,MAAM,CAAC,CAAA;QAClC,MAAM,YAAY,IAAI,KAAK,MAAM,KAAK,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnE,OAAO,cAAc;IACvB;IAEA,QAAQ,GAAG,CAAC,8CAA8C,YAAY;IAEtE,IAAI,cAAc,MAAM,KAAK,GAAG;QAC9B,qBACE,8OAAC,gIAAA,CAAA,OAAI;;8BACH,8OAAC,gIAAA,CAAA,aAAU;8BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;wBAAC,WAAU;;4BAAU;4BAClB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,cAAc;gCAAE,QAAQ,2IAAA,CAAA,KAAE;4BAAC;;;;;;;;;;;;8BAGvD,8OAAC,gIAAA,CAAA,cAAW;8BACV,cAAA,8OAAC;wBAAI,WAAU;kCAAyC;;;;;;;;;;;;;;;;;IAMhE;IAEA,qBACE,8OAAC,gIAAA,CAAA,OAAI;;0BACH,8OAAC,gIAAA,CAAA,aAAU;0BACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;oBAAC,WAAU;;wBAAU;wBAClB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,cAAc;4BAAE,QAAQ,2IAAA,CAAA,KAAE;wBAAC;;;;;;;;;;;;0BAGvD,8OAAC,gIAAA,CAAA,cAAW;0BACV,cAAA,8OAAC;oBAAI,WAAU;8BACZ,cAAc,MAAM,CAAC,CAAA,QAAS,MAAM,EAAE,EAAE,GAAG,CAAC,CAAC,OAAO,sBACnD,8OAAC;4BAEC,WAAW,CAAC,sBAAsB,EAChC,MAAM,SAAS,GAAG,gBAAgB,WAClC;4BACF,OAAO;gCAAE,iBAAiB,MAAM,KAAK;gCAAE,iBAAiB;4BAAM;sCAE9D,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;gDACZ,MAAM,SAAS,iBACd,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;yEAEvB,8OAAC,oNAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DAEzB,8OAAC;oDAAG,WAAW,CAAC,YAAY,EAC1B,MAAM,SAAS,GAAG,uCAAuC,IACzD;8DACC,MAAM,KAAK;;;;;;;;;;;;sDAIhB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;wDAChB,MAAM,GAAG,iBACR,8OAAC;;gEACE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,KAAK,GAAG,SAAS;oEAAE,QAAQ,2IAAA,CAAA,KAAE;gEAAC;gEAAG;gEAAI;gEAC3D,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,GAAG,GAAG,SAAS;oEAAE,QAAQ,2IAAA,CAAA,KAAE;gEAAC;;;;;;iFAGrD,8OAAC;sEACE,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,MAAM,KAAK,GAAG,eAAe;gEAAE,QAAQ,2IAAA,CAAA,KAAE;4DAAC;;;;;;;;;;;;8DAKjE,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAChC,MAAM,IAAI,KAAK,SAAS,cAAc;;;;;;;;;;;;wCAI1C,MAAM,IAAI,KAAK,UAAU,AAAC,MAAM,IAAI,CAAS,WAAW,kBACvD,8OAAC;4CAAE,WAAU;sDACV,AAAC,MAAM,IAAI,CAAS,WAAW;;;;;;;;;;;;;;;;;2BA3CnC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,OAAO;;;;;;;;;;;;;;;;;;;;;AAsD/C", "debugId": null}}, {"offset": {"line": 6369, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/calendar/calendar-view.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { TimeBlockList } from '@/components/timeblock/time-block-list';\nimport { Button } from '@/components/ui/button';\nimport { BentoGrid, BentoCard } from '@/components/ui/bento-grid';\nimport { \n  addDays, \n  format, \n  subDays, \n  startOfWeek, \n  endOfWeek, \n  eachDayOfInterval,\n  isToday,\n  isSameDay,\n  parseISO,\n  isWithinInterval,\n} from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport { PreferenceService } from '@/lib/services/preference-service';\nimport { TaskService } from '@/lib/services/task-service';\nimport { TimeBlockService } from '@/lib/services/timeblock-service';\nimport { Task, TimeBlock, UserPreferences } from '@/lib/types';\nimport { CalendarEvent } from '@/lib/services/calendar-service';\nimport { CalendarDays, ChevronLeft, ChevronRight, Calendar, CheckCircle, AlertCircle, Loader2, RefreshCw } from 'lucide-react';\nimport { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';\nimport { Badge } from '@/components/ui/badge';\nimport { CalendarEvents } from '@/components/calendar/calendar-events';\n\ninterface CalendarViewProps {\n  tasks?: Task[];\n  setTasks?: (tasks: Task[]) => void;\n  timeBlocks?: TimeBlock[];\n  setTimeBlocks?: (timeBlocks: TimeBlock[]) => void;\n  events?: CalendarEvent[];\n  onDataChange?: () => void;\n}\n\nexport function CalendarView({\n  tasks: propTasks,\n  setTasks: propSetTasks,\n  timeBlocks: propTimeBlocks,\n  setTimeBlocks: propSetTimeBlocks,\n  events: propEvents,\n  onDataChange\n}: CalendarViewProps = {}) {\n  const [selectedDate, setSelectedDate] = useState<Date>(new Date());\n  const [view, setView] = useState<'day' | 'week'>('day');\n  const [internalTasks, setInternalTasks] = useState<Task[]>([]);\n  const [internalTimeBlocks, setInternalTimeBlocks] = useState<TimeBlock[]>([]);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n  const [preferences, setPreferences] = useState<UserPreferences>(PreferenceService.getPreferencesSync());\n\n  const tasks = propTasks ?? internalTasks;\n  const setTasks = propSetTasks ?? setInternalTasks;\n  const timeBlocks = propTimeBlocks ?? internalTimeBlocks;\n  const setTimeBlocks = propSetTimeBlocks ?? setInternalTimeBlocks;\n\n  // Debug logs\n  console.log('CalendarView: Current data:', {\n    tasks: tasks.length,\n    timeBlocks: timeBlocks.length,\n    propEvents: propEvents?.length || 0,\n    selectedDate: selectedDate.toISOString().split('T')[0]\n  });\n  \n  // Tải dữ liệu nếu không có props từ parent\n  useEffect(() => {\n    if (propTasks && propTimeBlocks) {\n      // Nếu có data từ props, không cần load\n      setLoading(false);\n      return;\n    }\n\n    const loadData = async () => {\n      setLoading(true);\n      try {\n        const [fetchedTasks, fetchedTimeBlocks] = await Promise.all([\n          TaskService.getTasks(),\n          TimeBlockService.getTimeBlocks(),\n        ]);\n\n        setTasks(fetchedTasks);\n        setTimeBlocks(fetchedTimeBlocks);\n        setError(null);\n      } catch (err) {\n        console.error('Lỗi khi tải dữ liệu lịch:', err);\n        setError('Không thể tải dữ liệu lịch. Vui lòng thử lại sau.');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    // Chỉ load nếu không có props\n    if (!propTasks || !propTimeBlocks) {\n      loadData();\n    }\n  }, [propTasks, propTimeBlocks, setTasks, setTimeBlocks]);\n\n  // Callback để handle data change và reload nếu cần\n  const handleDataChange = async () => {\n    console.log('CalendarView: Data changed, triggering refresh...');\n\n    // Nếu có onDataChange từ parent, gọi nó\n    if (onDataChange) {\n      onDataChange();\n    } else {\n      // Nếu không có parent callback, tự reload\n      try {\n        const [fetchedTasks, fetchedTimeBlocks] = await Promise.all([\n          TaskService.getTasks(),\n          TimeBlockService.getTimeBlocks(),\n        ]);\n\n        setTasks(fetchedTasks);\n        setTimeBlocks(fetchedTimeBlocks);\n      } catch (err) {\n        console.error('Lỗi khi reload dữ liệu:', err);\n      }\n    }\n  };\n  \n  // Tải tùy chọn người dùng\n  useEffect(() => {\n    const loadPreferences = async () => {\n      try {\n        const userPreferences = await PreferenceService.getPreferences();\n        setPreferences(userPreferences);\n      } catch (err) {\n        console.error('Lỗi khi tải tùy chọn người dùng:', err);\n      }\n    };\n    \n    loadPreferences();\n  }, []);\n  \n  // Xác định ngày bắt đầu tuần dựa vào tùy chọn người dùng\n  const getStartOfWeek = (date: Date) => {\n    return startOfWeek(date, { weekStartsOn: preferences.startOfWeek === 'monday' ? 1 : 0 });\n  };\n  \n  const handlePreviousDay = () => {\n    setSelectedDate(subDays(selectedDate, 1));\n  };\n  \n  const handleNextDay = () => {\n    setSelectedDate(addDays(selectedDate, 1));\n  };\n  \n  const handlePreviousWeek = () => {\n    setSelectedDate(subDays(selectedDate, 7));\n  };\n  \n  const handleNextWeek = () => {\n    setSelectedDate(addDays(selectedDate, 7));\n  };\n  \n  const handleToday = () => {\n    setSelectedDate(new Date());\n  };\n  \n  // Lấy danh sách công việc cho một ngày cụ thể\n  const getTasksForDate = (date: Date) => {\n    return tasks.filter(task => {\n      if (!task.dueDate) return false;\n      const dueDate = new Date(task.dueDate);\n      return isSameDay(dueDate, date);\n    });\n  };\n  \n  // Lấy nhãn cho mức độ ưu tiên\n  const getPriorityLabel = (priority: 'low' | 'medium' | 'high') => {\n    switch (priority) {\n      case 'low': return 'Thấp';\n      case 'medium': return 'Trung bình';\n      case 'high': return 'Cao';\n      default: return 'Không xác định';\n    }\n  };\n  \n  // Lấy biến thể màu sắc cho badge dựa vào mức độ ưu tiên\n  const getPriorityVariant = (priority: 'low' | 'medium' | 'high'): 'success' | 'warning' | 'destructive' | 'default' => {\n    switch (priority) {\n      case 'low': return 'success';\n      case 'medium': return 'warning';\n      case 'high': return 'destructive';\n      default: return 'default';\n    }\n  };\n  \n  const weekStart = getStartOfWeek(selectedDate);\n  const weekEnd = endOfWeek(weekStart, { weekStartsOn: preferences.startOfWeek === 'monday' ? 1 : 0 });\n  const daysOfWeek = eachDayOfInterval({ start: weekStart, end: weekEnd });\n  \n  // Xử lý hoàn thành công việc\n  const handleToggleTaskCompletion = async (taskId: string) => {\n    try {\n      const updatedTask = await TaskService.toggleTaskCompletion(taskId);\n      // Cập nhật danh sách công việc\n      setTasks(tasks.map(task => task.id === taskId ? updatedTask : task));\n\n      // Trigger parent refresh nếu có\n      if (onDataChange) {\n        onDataChange();\n      }\n\n      console.log('CalendarView: Task completion toggled, stats will update');\n    } catch (err) {\n      console.error('Lỗi khi cập nhật trạng thái công việc:', err);\n    }\n  };\n  \n  // Lấy time blocks cho một ngày cụ thể\n  const getTimeBlocksForDate = (date: Date) => {\n    const dateString = date.toISOString().split('T')[0];\n    return timeBlocks.filter(timeBlock => {\n      const blockDate = new Date(timeBlock.startTime).toISOString().split('T')[0];\n      return blockDate === dateString;\n    });\n  };\n\n  // Tính toán thống kê nhanh cho ngày được chọn (bao gồm cả tasks và time blocks)\n  const getDailyStats = (date: Date) => {\n    const tasksForDate = getTasksForDate(date);\n    const timeBlocksForDate = getTimeBlocksForDate(date);\n\n    const completedTasks = tasksForDate.filter(task => task.completed).length;\n    const completedTimeBlocks = timeBlocksForDate.filter(block => block.isCompleted).length;\n\n    const totalItems = tasksForDate.length + timeBlocksForDate.length;\n    const completedItems = completedTasks + completedTimeBlocks;\n    const pendingItems = totalItems - completedItems;\n\n    return {\n      totalTasks: tasksForDate.length,\n      totalTimeBlocks: timeBlocksForDate.length,\n      total: totalItems,\n      completed: completedItems,\n      pending: pendingItems,\n      completionRate: totalItems > 0 ? Math.round((completedItems / totalItems) * 100) : 0,\n      taskStats: {\n        total: tasksForDate.length,\n        completed: completedTasks,\n        pending: tasksForDate.length - completedTasks\n      },\n      timeBlockStats: {\n        total: timeBlocksForDate.length,\n        completed: completedTimeBlocks,\n        pending: timeBlocksForDate.length - completedTimeBlocks\n      }\n    };\n  };\n  \n  const dailyStats = getDailyStats(selectedDate);\n\n  // Auto-refresh stats khi data thay đổi\n  useEffect(() => {\n    console.log('CalendarView: Stats updated for date:', selectedDate.toISOString().split('T')[0], dailyStats);\n  }, [tasks, timeBlocks, selectedDate]);\n  \n  return (\n    <div className=\"space-y-6 p-4 md:p-6\">\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\n        <h1 className=\"text-3xl font-bold tracking-tight\">Lịch</h1>\n        <div className=\"flex flex-wrap items-center gap-2\">\n          <div className=\"bg-muted rounded-lg p-1\">\n            <Button \n              variant={view === 'day' ? 'default' : 'ghost'} \n              size=\"sm\" \n              onClick={() => setView('day')}\n              className=\"gap-1\"\n            >\n              <Calendar className=\"h-4 w-4\" />\n              <span>Ngày</span>\n            </Button>\n            <Button \n              variant={view === 'week' ? 'default' : 'ghost'} \n              size=\"sm\" \n              onClick={() => setView('week')}\n              className=\"gap-1\"\n            >\n              <CalendarDays className=\"h-4 w-4\" />\n              <span>Tuần</span>\n            </Button>\n          </div>\n          \n          <Button \n            variant=\"outline\" \n            size=\"sm\"\n            onClick={handleToday}\n          >\n            Hôm nay\n          </Button>\n          \n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button \n                  variant=\"outline\" \n                  size=\"icon\"\n                  onClick={view === 'day' ? handlePreviousDay : handlePreviousWeek}\n                >\n                  <ChevronLeft className=\"h-4 w-4\" />\n                  <span className=\"sr-only\">Trước</span>\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>{view === 'day' ? 'Ngày trước' : 'Tuần trước'}</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n          \n          <div className=\"text-sm font-medium\">\n            {view === 'day' ? (\n              <div className=\"px-2\">\n                {format(selectedDate, 'EEEE, dd/MM/yyyy', { locale: vi })}\n              </div>\n            ) : (\n              <div className=\"px-2\">\n                {format(weekStart, 'dd/MM/yyyy', { locale: vi })} - {format(weekEnd, 'dd/MM/yyyy', { locale: vi })}\n              </div>\n            )}\n          </div>\n          \n          <TooltipProvider>\n            <Tooltip>\n              <TooltipTrigger asChild>\n                <Button \n                  variant=\"outline\" \n                  size=\"icon\"\n                  onClick={view === 'day' ? handleNextDay : handleNextWeek}\n                >\n                  <ChevronRight className=\"h-4 w-4\" />\n                  <span className=\"sr-only\">Tiếp</span>\n                </Button>\n              </TooltipTrigger>\n              <TooltipContent>\n                <p>{view === 'day' ? 'Ngày tiếp theo' : 'Tuần tiếp theo'}</p>\n              </TooltipContent>\n            </Tooltip>\n          </TooltipProvider>\n        </div>\n      </div>\n      \n      {loading ? (\n        <div className=\"flex items-center justify-center py-12\">\n          <Loader2 className=\"h-8 w-8 animate-spin text-primary\" />\n          <span className=\"ml-2\">Đang tải dữ liệu...</span>\n        </div>\n      ) : error ? (\n        <div className=\"bg-destructive/10 text-destructive p-4 rounded-lg\">\n          <p>{error}</p>\n          <Button \n            variant=\"outline\" \n            size=\"sm\" \n            onClick={() => TaskService.refreshTasks().then(setTasks).catch(console.error)}\n            className=\"mt-2\"\n          >\n            Thử lại\n          </Button>\n        </div>\n      ) : (\n        <>\n          {/* Thống kê nhanh cho ngày được chọn */}\n          {view === 'day' && (\n            <div className=\"space-y-4 mb-6\">\n              {/* Header với refresh button */}\n              <div className=\"flex items-center justify-between\">\n                <h3 className=\"text-lg font-semibold\">Thống kê ngày {format(selectedDate, 'dd/MM/yyyy', { locale: vi })}</h3>\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={handleDataChange}\n                  className=\"flex items-center gap-2\"\n                >\n                  <RefreshCw className=\"h-4 w-4\" />\n                  Làm mới\n                </Button>\n              </div>\n\n              {/* Thống kê tổng quan */}\n              <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4\">\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-1\">Tổng mục</h4>\n                  <p className=\"text-2xl font-bold\">{dailyStats.total}</p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    {dailyStats.totalTasks} công việc + {dailyStats.totalTimeBlocks} khối thời gian\n                  </p>\n                </div>\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-1\">Đã hoàn thành</h4>\n                  <p className=\"text-2xl font-bold text-green-500\">{dailyStats.completed}</p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    {dailyStats.taskStats.completed} CV + {dailyStats.timeBlockStats.completed} KTG\n                  </p>\n                </div>\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-1\">Chưa hoàn thành</h4>\n                  <p className=\"text-2xl font-bold text-orange-500\">{dailyStats.pending}</p>\n                  <p className=\"text-xs text-muted-foreground mt-1\">\n                    {dailyStats.taskStats.pending} CV + {dailyStats.timeBlockStats.pending} KTG\n                  </p>\n                </div>\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-1\">Tỷ lệ hoàn thành</h4>\n                  <p className=\"text-2xl font-bold\">{dailyStats.completionRate}%</p>\n                  <div className=\"w-full bg-muted rounded-full h-2 mt-2\">\n                    <div\n                      className=\"bg-green-500 h-2 rounded-full transition-all duration-300\"\n                      style={{ width: `${dailyStats.completionRate}%` }}\n                    ></div>\n                  </div>\n                </div>\n              </div>\n\n              {/* Thống kê chi tiết */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">Công việc</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">Tổng:</span>\n                      <span className=\"font-medium\">{dailyStats.taskStats.total}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-green-600\">Hoàn thành:</span>\n                      <span className=\"font-medium text-green-600\">{dailyStats.taskStats.completed}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-orange-600\">Chưa xong:</span>\n                      <span className=\"font-medium text-orange-600\">{dailyStats.taskStats.pending}</span>\n                    </div>\n                  </div>\n                </div>\n\n                <div className=\"bg-card rounded-lg p-4 border\">\n                  <h4 className=\"text-sm font-medium text-muted-foreground mb-3\">Khối thời gian</h4>\n                  <div className=\"space-y-2\">\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm\">Tổng:</span>\n                      <span className=\"font-medium\">{dailyStats.timeBlockStats.total}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-green-600\">Hoàn thành:</span>\n                      <span className=\"font-medium text-green-600\">{dailyStats.timeBlockStats.completed}</span>\n                    </div>\n                    <div className=\"flex justify-between items-center\">\n                      <span className=\"text-sm text-orange-600\">Chưa xong:</span>\n                      <span className=\"font-medium text-orange-600\">{dailyStats.timeBlockStats.pending}</span>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          {/* Hiển thị công việc đến hạn cho ngày được chọn */}\n          {view === 'day' && (\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-semibold mb-3\">Công việc đến hạn</h3>\n              <div className=\"space-y-2\">\n                {getTasksForDate(selectedDate).length > 0 ? (\n                  getTasksForDate(selectedDate).map(task => (\n                    <div \n                      key={task.id} \n                      className={`p-3 rounded-lg border ${task.completed ? 'bg-muted/30' : 'bg-card'}`}\n                    >\n                      <div className=\"flex items-center gap-2\">\n                        <button \n                          onClick={() => handleToggleTaskCompletion(task.id)}\n                          className=\"focus:outline-none\"\n                        >\n                          {task.completed ? (\n                            <CheckCircle className=\"h-5 w-5 text-green-500\" />\n                          ) : (\n                            <AlertCircle className=\"h-5 w-5 text-orange-500\" />\n                          )}\n                        </button>\n                        <div className=\"flex-1\">\n                          <div className=\"flex items-center justify-between\">\n                            <h4 className={`font-medium ${task.completed ? 'line-through text-muted-foreground' : ''}`}>\n                              {task.title}\n                            </h4>\n                            <Badge variant={getPriorityVariant(task.priority)}>\n                              {getPriorityLabel(task.priority)}\n                            </Badge>\n                          </div>\n                          {task.description && (\n                            <p className=\"text-sm text-muted-foreground mt-1\">{task.description}</p>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  ))\n                ) : (\n                  <div className=\"text-center py-6 text-muted-foreground\">\n                    Không có công việc nào đến hạn vào ngày này\n                  </div>\n                )}\n              </div>\n            </div>\n          )}\n          \n          {view === 'day' ? (\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <TimeBlockList date={selectedDate} onDataChange={handleDataChange} />\n              {propEvents && propEvents.length > 0 && (\n                <CalendarEvents events={propEvents} date={selectedDate} />\n              )}\n            </div>\n          ) : (\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-7 gap-4\">\n              {daysOfWeek.map((day) => (\n                <div \n                  key={day.toString()} \n                  className={`rounded-lg border p-4 ${\n                    isToday(day) \n                      ? 'border-primary/50 bg-primary/5'\n                      : isSameDay(day, selectedDate) && !isToday(day) \n                      ? 'border-muted-foreground/20 bg-muted/30' \n                      : ''\n                  }`}\n                >\n                  <div \n                    className=\"mb-3 text-center cursor-pointer\"\n                    onClick={() => setSelectedDate(day)}\n                  >\n                    <div className=\"text-sm text-muted-foreground\">\n                      {format(day, 'EEEE', { locale: vi })}\n                    </div>\n                    <div className=\"flex items-center justify-center gap-1\">\n                      <div className={`text-lg font-semibold ${isToday(day) ? 'text-primary' : ''}`}>\n                        {format(day, 'dd/MM', { locale: vi })}\n                      </div>\n                      {getTasksForDate(day).length > 0 && (\n                        <span className=\"inline-flex items-center justify-center w-5 h-5 text-xs font-medium rounded-full bg-primary text-primary-foreground\">\n                          {getTasksForDate(day).length}\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                  \n                  <TimeBlockList date={day} compact onDataChange={handleDataChange} />\n                </div>\n              ))}\n            </div>\n          )}\n        </>\n      )}\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AACA;AACA;AACA;AAGA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;;;AAsCO,SAAS,aAAa,EAC3B,OAAO,SAAS,EAChB,UAAU,YAAY,EACtB,YAAY,cAAc,EAC1B,eAAe,iBAAiB,EAChC,QAAQ,UAAU,EAClB,YAAY,EACM,GAAG,CAAC,CAAC;IACvB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ,IAAI;IAC3D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,+IAAA,CAAA,oBAAiB,CAAC,kBAAkB;IAEpG,MAAM,QAAQ,aAAa;IAC3B,MAAM,WAAW,gBAAgB;IACjC,MAAM,aAAa,kBAAkB;IACrC,MAAM,gBAAgB,qBAAqB;IAE3C,aAAa;IACb,QAAQ,GAAG,CAAC,+BAA+B;QACzC,OAAO,MAAM,MAAM;QACnB,YAAY,WAAW,MAAM;QAC7B,YAAY,YAAY,UAAU;QAClC,cAAc,aAAa,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;IACxD;IAEA,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,aAAa,gBAAgB;YAC/B,uCAAuC;YACvC,WAAW;YACX;QACF;QAEA,MAAM,WAAW;YACf,WAAW;YACX,IAAI;gBACF,MAAM,CAAC,cAAc,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC1D,yIAAA,CAAA,cAAW,CAAC,QAAQ;oBACpB,8IAAA,CAAA,mBAAgB,CAAC,aAAa;iBAC/B;gBAED,SAAS;gBACT,cAAc;gBACd,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,8BAA8B;QAC9B,IAAI,CAAC,aAAa,CAAC,gBAAgB;YACjC;QACF;IACF,GAAG;QAAC;QAAW;QAAgB;QAAU;KAAc;IAEvD,mDAAmD;IACnD,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QAEZ,wCAAwC;QACxC,IAAI,cAAc;YAChB;QACF,OAAO;YACL,0CAA0C;YAC1C,IAAI;gBACF,MAAM,CAAC,cAAc,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;oBAC1D,yIAAA,CAAA,cAAW,CAAC,QAAQ;oBACpB,8IAAA,CAAA,mBAAgB,CAAC,aAAa;iBAC/B;gBAED,SAAS;gBACT,cAAc;YAChB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;IACF;IAEA,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,IAAI;gBACF,MAAM,kBAAkB,MAAM,+IAAA,CAAA,oBAAiB,CAAC,cAAc;gBAC9D,eAAe;YACjB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;YACpD;QACF;QAEA;IACF,GAAG,EAAE;IAEL,yDAAyD;IACzD,MAAM,iBAAiB,CAAC;QACtB,OAAO,CAAA,GAAA,0IAAA,CAAA,cAAW,AAAD,EAAE,MAAM;YAAE,cAAc,YAAY,WAAW,KAAK,WAAW,IAAI;QAAE;IACxF;IAEA,MAAM,oBAAoB;QACxB,gBAAgB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,MAAM,gBAAgB;QACpB,gBAAgB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,MAAM,qBAAqB;QACzB,gBAAgB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,MAAM,iBAAiB;QACrB,gBAAgB,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,cAAc;IACxC;IAEA,MAAM,cAAc;QAClB,gBAAgB,IAAI;IACtB;IAEA,8CAA8C;IAC9C,MAAM,kBAAkB,CAAC;QACvB,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;YAC1B,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO;YACrC,OAAO,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,SAAS;QAC5B;IACF;IAEA,8BAA8B;IAC9B,MAAM,mBAAmB,CAAC;QACxB,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,wDAAwD;IACxD,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAO,OAAO;YACnB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAQ,OAAO;YACpB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,YAAY,eAAe;IACjC,MAAM,UAAU,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,WAAW;QAAE,cAAc,YAAY,WAAW,KAAK,WAAW,IAAI;IAAE;IAClG,MAAM,aAAa,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE;QAAE,OAAO;QAAW,KAAK;IAAQ;IAEtE,6BAA6B;IAC7B,MAAM,6BAA6B,OAAO;QACxC,IAAI;YACF,MAAM,cAAc,MAAM,yIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;YAC3D,+BAA+B;YAC/B,SAAS,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,SAAS,cAAc;YAE9D,gCAAgC;YAChC,IAAI,cAAc;gBAChB;YACF;YAEA,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,0CAA0C;QAC1D;IACF;IAEA,sCAAsC;IACtC,MAAM,uBAAuB,CAAC;QAC5B,MAAM,aAAa,KAAK,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QACnD,OAAO,WAAW,MAAM,CAAC,CAAA;YACvB,MAAM,YAAY,IAAI,KAAK,UAAU,SAAS,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAC3E,OAAO,cAAc;QACvB;IACF;IAEA,gFAAgF;IAChF,MAAM,gBAAgB,CAAC;QACrB,MAAM,eAAe,gBAAgB;QACrC,MAAM,oBAAoB,qBAAqB;QAE/C,MAAM,iBAAiB,aAAa,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;QACzE,MAAM,sBAAsB,kBAAkB,MAAM,CAAC,CAAA,QAAS,MAAM,WAAW,EAAE,MAAM;QAEvF,MAAM,aAAa,aAAa,MAAM,GAAG,kBAAkB,MAAM;QACjE,MAAM,iBAAiB,iBAAiB;QACxC,MAAM,eAAe,aAAa;QAElC,OAAO;YACL,YAAY,aAAa,MAAM;YAC/B,iBAAiB,kBAAkB,MAAM;YACzC,OAAO;YACP,WAAW;YACX,SAAS;YACT,gBAAgB,aAAa,IAAI,KAAK,KAAK,CAAC,AAAC,iBAAiB,aAAc,OAAO;YACnF,WAAW;gBACT,OAAO,aAAa,MAAM;gBAC1B,WAAW;gBACX,SAAS,aAAa,MAAM,GAAG;YACjC;YACA,gBAAgB;gBACd,OAAO,kBAAkB,MAAM;gBAC/B,WAAW;gBACX,SAAS,kBAAkB,MAAM,GAAG;YACtC;QACF;IACF;IAEA,MAAM,aAAa,cAAc;IAEjC,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,yCAAyC,aAAa,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,EAAE;IACjG,GAAG;QAAC;QAAO;QAAY;KAAa;IAEpC,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,SAAS,QAAQ,YAAY;wCACtC,MAAK;wCACL,SAAS,IAAM,QAAQ;wCACvB,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAS,SAAS,SAAS,YAAY;wCACvC,MAAK;wCACL,SAAS,IAAM,QAAQ;wCACvB,WAAU;;0DAEV,8OAAC,sNAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DACxB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;0CAIV,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;0CACV;;;;;;0CAID,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,SAAS,QAAQ,oBAAoB;;kEAE9C,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAG,SAAS,QAAQ,eAAe;;;;;;;;;;;;;;;;;;;;;;0CAK1C,8OAAC;gCAAI,WAAU;0CACZ,SAAS,sBACR,8OAAC;oCAAI,WAAU;8CACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,oBAAoB;wCAAE,QAAQ,2IAAA,CAAA,KAAE;oCAAC;;;;;yDAGzD,8OAAC;oCAAI,WAAU;;wCACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,WAAW,cAAc;4CAAE,QAAQ,2IAAA,CAAA,KAAE;wCAAC;wCAAG;wCAAI,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,cAAc;4CAAE,QAAQ,2IAAA,CAAA,KAAE;wCAAC;;;;;;;;;;;;0CAKtG,8OAAC,mIAAA,CAAA,kBAAe;0CACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;sDACN,8OAAC,mIAAA,CAAA,iBAAc;4CAAC,OAAO;sDACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,SAAS,QAAQ,gBAAgB;;kEAE1C,8OAAC,sNAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;kEACxB,8OAAC;wDAAK,WAAU;kEAAU;;;;;;;;;;;;;;;;;sDAG9B,8OAAC,mIAAA,CAAA,iBAAc;sDACb,cAAA,8OAAC;0DAAG,SAAS,QAAQ,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAOjD,wBACC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAK,WAAU;kCAAO;;;;;;;;;;;uBAEvB,sBACF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCAAG;;;;;;kCACJ,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS,IAAM,yIAAA,CAAA,cAAW,CAAC,YAAY,GAAG,IAAI,CAAC,UAAU,KAAK,CAAC,QAAQ,KAAK;wBAC5E,WAAU;kCACX;;;;;;;;;;;qCAKH;;oBAEG,SAAS,uBACR,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;;4CAAwB;4CAAe,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,cAAc,cAAc;gDAAE,QAAQ,2IAAA,CAAA,KAAE;4CAAC;;;;;;;kDACrG,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAMrC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAAsB,WAAW,KAAK;;;;;;0DACnD,8OAAC;gDAAE,WAAU;;oDACV,WAAW,UAAU;oDAAC;oDAAc,WAAW,eAAe;oDAAC;;;;;;;;;;;;;kDAGpE,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAAqC,WAAW,SAAS;;;;;;0DACtE,8OAAC;gDAAE,WAAU;;oDACV,WAAW,SAAS,CAAC,SAAS;oDAAC;oDAAO,WAAW,cAAc,CAAC,SAAS;oDAAC;;;;;;;;;;;;;kDAG/E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;0DAAsC,WAAW,OAAO;;;;;;0DACrE,8OAAC;gDAAE,WAAU;;oDACV,WAAW,SAAS,CAAC,OAAO;oDAAC;oDAAO,WAAW,cAAc,CAAC,OAAO;oDAAC;;;;;;;;;;;;;kDAG3E,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAE,WAAU;;oDAAsB,WAAW,cAAc;oDAAC;;;;;;;0DAC7D,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,WAAW,cAAc,CAAC,CAAC,CAAC;oDAAC;;;;;;;;;;;;;;;;;;;;;;;0CAOxD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAU;0EAAe,WAAW,SAAS,CAAC,KAAK;;;;;;;;;;;;kEAE3D,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,8OAAC;gEAAK,WAAU;0EAA8B,WAAW,SAAS,CAAC,SAAS;;;;;;;;;;;;kEAE9E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA0B;;;;;;0EAC1C,8OAAC;gEAAK,WAAU;0EAA+B,WAAW,SAAS,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;kDAKjF,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAiD;;;;;;0DAC/D,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAU;;;;;;0EAC1B,8OAAC;gEAAK,WAAU;0EAAe,WAAW,cAAc,CAAC,KAAK;;;;;;;;;;;;kEAEhE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAyB;;;;;;0EACzC,8OAAC;gEAAK,WAAU;0EAA8B,WAAW,cAAc,CAAC,SAAS;;;;;;;;;;;;kEAEnF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAA0B;;;;;;0EAC1C,8OAAC;gEAAK,WAAU;0EAA+B,WAAW,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAS3F,SAAS,uBACR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,gBAAgB,cAAc,MAAM,GAAG,IACtC,gBAAgB,cAAc,GAAG,CAAC,CAAA,qBAChC,8OAAC;wCAEC,WAAW,CAAC,sBAAsB,EAAE,KAAK,SAAS,GAAG,gBAAgB,WAAW;kDAEhF,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,SAAS,IAAM,2BAA2B,KAAK,EAAE;oDACjD,WAAU;8DAET,KAAK,SAAS,iBACb,8OAAC,2NAAA,CAAA,cAAW;wDAAC,WAAU;;;;;6EAEvB,8OAAC,oNAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;;;;;;8DAG3B,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAW,CAAC,YAAY,EAAE,KAAK,SAAS,GAAG,uCAAuC,IAAI;8EACvF,KAAK,KAAK;;;;;;8EAEb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAS,mBAAmB,KAAK,QAAQ;8EAC7C,iBAAiB,KAAK,QAAQ;;;;;;;;;;;;wDAGlC,KAAK,WAAW,kBACf,8OAAC;4DAAE,WAAU;sEAAsC,KAAK,WAAW;;;;;;;;;;;;;;;;;;uCAxBpE,KAAK,EAAE;;;;8DA+BhB,8OAAC;oCAAI,WAAU;8CAAyC;;;;;;;;;;;;;;;;;oBAQ/D,SAAS,sBACR,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wJAAA,CAAA,gBAAa;gCAAC,MAAM;gCAAc,cAAc;;;;;;4BAChD,cAAc,WAAW,MAAM,GAAG,mBACjC,8OAAC,oJAAA,CAAA,iBAAc;gCAAC,QAAQ;gCAAY,MAAM;;;;;;;;;;;6CAI9C,8OAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC,oBACf,8OAAC;gCAEC,WAAW,CAAC,sBAAsB,EAChC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OACJ,mCACA,CAAA,GAAA,wIAAA,CAAA,YAAS,AAAD,EAAE,KAAK,iBAAiB,CAAC,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OACzC,2CACA,IACJ;;kDAEF,8OAAC;wCACC,WAAU;wCACV,SAAS,IAAM,gBAAgB;;0DAE/B,8OAAC;gDAAI,WAAU;0DACZ,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,QAAQ;oDAAE,QAAQ,2IAAA,CAAA,KAAE;gDAAC;;;;;;0DAEpC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,sBAAsB,EAAE,CAAA,GAAA,sIAAA,CAAA,UAAO,AAAD,EAAE,OAAO,iBAAiB,IAAI;kEAC1E,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,SAAS;4DAAE,QAAQ,2IAAA,CAAA,KAAE;wDAAC;;;;;;oDAEpC,gBAAgB,KAAK,MAAM,GAAG,mBAC7B,8OAAC;wDAAK,WAAU;kEACb,gBAAgB,KAAK,MAAM;;;;;;;;;;;;;;;;;;kDAMpC,8OAAC,wJAAA,CAAA,gBAAa;wCAAC,MAAM;wCAAK,OAAO;wCAAC,cAAc;;;;;;;+BA5B3C,IAAI,QAAQ;;;;;;;;;;;;;;;;;;AAqCnC", "debugId": null}}, {"offset": {"line": 7575, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/calendar-service.ts"], "sourcesContent": ["import { ApiService } from './api-service';\nimport { Task, TimeBlock } from '@/lib/types';\n\nexport interface CalendarEvent {\n  id: string;\n  title: string;\n  start: string;\n  end?: string;\n  type: 'task' | 'timeblock';\n  data: Task | TimeBlock;\n  color?: string;\n  completed?: boolean;\n}\n\nexport interface CalendarData {\n  tasks: Task[];\n  timeBlocks: TimeBlock[];\n  events: CalendarEvent[];\n}\n\nexport interface DayData {\n  date: string;\n  tasks: Task[];\n  timeBlocks: TimeBlock[];\n  events: CalendarEvent[];\n}\n\nexport interface WeekData {\n  startDate: string;\n  endDate: string;\n  days: DayData[];\n}\n\nexport const CalendarService = {\n  // L<PERSON>y dữ liệu lịch cho khoảng thời gian\n  getCalendarData: async (start?: string, end?: string): Promise<CalendarData> => {\n    try {\n      console.log('CalendarService: Đang tải dữ liệu lịch...', { start, end });\n      \n      const params = new URLSearchParams();\n      if (start) params.append('start', start);\n      if (end) params.append('end', end);\n      \n      const url = `/calendar/events${params.toString() ? `?${params.toString()}` : ''}`;\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/calendar/events${params.toString() ? `?${params.toString()}` : ''}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      console.log('CalendarService: Đã tải dữ liệu lịch:', data);\n      return data;\n    } catch (error) {\n      console.error('CalendarService: Lỗi khi tải dữ liệu lịch:', error);\n      throw error;\n    }\n  },\n\n  // Lấy dữ liệu cho một ngày cụ thể\n  getDayData: async (date: string): Promise<DayData> => {\n    try {\n      console.log('CalendarService: Đang tải dữ liệu ngày...', date);\n      \n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/calendar/day/${date}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      console.log('CalendarService: Đã tải dữ liệu ngày:', data);\n      return data;\n    } catch (error) {\n      console.error('CalendarService: Lỗi khi tải dữ liệu ngày:', error);\n      throw error;\n    }\n  },\n\n  // Lấy dữ liệu cho một tuần\n  getWeekData: async (date: string): Promise<WeekData> => {\n    try {\n      console.log('CalendarService: Đang tải dữ liệu tuần...', date);\n      \n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/calendar/week/${date}`, {\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,\n          'Content-Type': 'application/json',\n        },\n      });\n      \n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n      \n      const data = await response.json();\n      console.log('CalendarService: Đã tải dữ liệu tuần:', data);\n      return data;\n    } catch (error) {\n      console.error('CalendarService: Lỗi khi tải dữ liệu tuần:', error);\n      throw error;\n    }\n  },\n\n  // Tạo events từ tasks và time blocks\n  createEventsFromData: (tasks: Task[], timeBlocks: TimeBlock[]): CalendarEvent[] => {\n    console.log('CalendarService: Creating events from data:', {\n      tasksCount: tasks.length,\n      timeBlocksCount: timeBlocks.length\n    });\n\n    const events: CalendarEvent[] = [];\n\n    // Tạo events từ tasks có dueDate\n    tasks.forEach(task => {\n      if (task.dueDate && task.id) {\n        events.push({\n          id: `task-${task.id}`,\n          title: task.title,\n          start: task.dueDate,\n          type: 'task',\n          data: task,\n          color: task.completed ? '#10b981' : '#f59e0b',\n          completed: task.completed,\n        });\n      }\n    });\n\n    // Tạo events từ time blocks\n    timeBlocks.forEach((timeBlock, index) => {\n      console.log('CalendarService: Creating event for timeBlock:', timeBlock);\n      if (timeBlock.id) {\n        events.push({\n          id: `timeblock-${timeBlock.id}`,\n          title: timeBlock.title,\n          start: timeBlock.startTime,\n          end: timeBlock.endTime,\n          type: 'timeblock',\n          data: timeBlock,\n          color: timeBlock.isCompleted ? '#10b981' : '#3b82f6',\n          completed: timeBlock.isCompleted,\n        });\n      } else {\n        console.warn('CalendarService: TimeBlock without id:', timeBlock);\n        // Tạo id tạm thời nếu không có\n        events.push({\n          id: `timeblock-temp-${index}`,\n          title: timeBlock.title,\n          start: timeBlock.startTime,\n          end: timeBlock.endTime,\n          type: 'timeblock',\n          data: timeBlock,\n          color: timeBlock.isCompleted ? '#10b981' : '#3b82f6',\n          completed: timeBlock.isCompleted,\n        });\n      }\n    });\n\n    console.log('CalendarService: Created events:', events);\n    return events;\n  },\n\n  // Lấy tasks và time blocks cho ngày cụ thể\n  getDataForDate: async (date: string): Promise<{ tasks: Task[], timeBlocks: TimeBlock[] }> => {\n    try {\n      console.log('CalendarService: Đang tải dữ liệu cho ngày...', date);\n      \n      // Lấy tasks có dueDate trong ngày\n      const tasks = await ApiService.tasks.getAll();\n      const tasksForDate = tasks.filter(task => {\n        if (!task.dueDate) return false;\n        const taskDate = new Date(task.dueDate).toISOString().split('T')[0];\n        return taskDate === date;\n      });\n\n      // Lấy time blocks trong ngày\n      const timeBlocks = await ApiService.timeBlocks.getByDate(date);\n\n      console.log('CalendarService: Đã tải dữ liệu cho ngày:', { tasks: tasksForDate, timeBlocks });\n      \n      return {\n        tasks: tasksForDate,\n        timeBlocks: timeBlocks,\n      };\n    } catch (error) {\n      console.error('CalendarService: Lỗi khi tải dữ liệu cho ngày:', error);\n      throw error;\n    }\n  },\n\n  // Lấy tất cả events cho tháng hiện tại\n  getCurrentMonthEvents: async (): Promise<CalendarEvent[]> => {\n    try {\n      const now = new Date();\n      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);\n      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);\n      \n      const start = startOfMonth.toISOString().split('T')[0];\n      const end = endOfMonth.toISOString().split('T')[0];\n      \n      const calendarData = await CalendarService.getCalendarData(start, end);\n      return CalendarService.createEventsFromData(calendarData.tasks, calendarData.timeBlocks);\n    } catch (error) {\n      console.error('CalendarService: Lỗi khi tải events tháng hiện tại:', error);\n      // Fallback: lấy từ API riêng lẻ\n      try {\n        const tasks = await ApiService.tasks.getAll();\n        const timeBlocks = await ApiService.timeBlocks.getAll();\n        return CalendarService.createEventsFromData(tasks, timeBlocks);\n      } catch (fallbackError) {\n        console.error('CalendarService: Lỗi fallback:', fallbackError);\n        return [];\n      }\n    }\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;;AAiCO,MAAM,kBAAkB;IAC7B,wCAAwC;IACxC,iBAAiB,OAAO,OAAgB;QACtC,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;gBAAE;gBAAO;YAAI;YAEtE,MAAM,SAAS,IAAI;YACnB,IAAI,OAAO,OAAO,MAAM,CAAC,SAAS;YAClC,IAAI,KAAK,OAAO,MAAM,CAAC,OAAO;YAE9B,MAAM,MAAM,CAAC,gBAAgB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI;YACjF,MAAM,WAAW,MAAM,MAAM,GAAG,iEAAmC,wBAAwB,oBAAoB,EAAE,OAAO,QAAQ,KAAK,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI,GAAG,IAAI,EAAE;gBACnK,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;oBAC9D,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,kCAAkC;IAClC,YAAY,OAAO;QACjB,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,MAAM,WAAW,MAAM,MAAM,GAAG,iEAAmC,wBAAwB,kBAAkB,EAAE,MAAM,EAAE;gBACrH,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;oBAC9D,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,2BAA2B;IAC3B,aAAa,OAAO;QAClB,IAAI;YACF,QAAQ,GAAG,CAAC,6CAA6C;YAEzD,MAAM,WAAW,MAAM,MAAM,GAAG,iEAAmC,wBAAwB,mBAAmB,EAAE,MAAM,EAAE;gBACtH,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,cAAc;oBAC9D,gBAAgB;gBAClB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,QAAQ,GAAG,CAAC,yCAAyC;YACrD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,qCAAqC;IACrC,sBAAsB,CAAC,OAAe;QACpC,QAAQ,GAAG,CAAC,+CAA+C;YACzD,YAAY,MAAM,MAAM;YACxB,iBAAiB,WAAW,MAAM;QACpC;QAEA,MAAM,SAA0B,EAAE;QAElC,iCAAiC;QACjC,MAAM,OAAO,CAAC,CAAA;YACZ,IAAI,KAAK,OAAO,IAAI,KAAK,EAAE,EAAE;gBAC3B,OAAO,IAAI,CAAC;oBACV,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBACrB,OAAO,KAAK,KAAK;oBACjB,OAAO,KAAK,OAAO;oBACnB,MAAM;oBACN,MAAM;oBACN,OAAO,KAAK,SAAS,GAAG,YAAY;oBACpC,WAAW,KAAK,SAAS;gBAC3B;YACF;QACF;QAEA,4BAA4B;QAC5B,WAAW,OAAO,CAAC,CAAC,WAAW;YAC7B,QAAQ,GAAG,CAAC,kDAAkD;YAC9D,IAAI,UAAU,EAAE,EAAE;gBAChB,OAAO,IAAI,CAAC;oBACV,IAAI,CAAC,UAAU,EAAE,UAAU,EAAE,EAAE;oBAC/B,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,SAAS;oBAC1B,KAAK,UAAU,OAAO;oBACtB,MAAM;oBACN,MAAM;oBACN,OAAO,UAAU,WAAW,GAAG,YAAY;oBAC3C,WAAW,UAAU,WAAW;gBAClC;YACF,OAAO;gBACL,QAAQ,IAAI,CAAC,0CAA0C;gBACvD,+BAA+B;gBAC/B,OAAO,IAAI,CAAC;oBACV,IAAI,CAAC,eAAe,EAAE,OAAO;oBAC7B,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,SAAS;oBAC1B,KAAK,UAAU,OAAO;oBACtB,MAAM;oBACN,MAAM;oBACN,OAAO,UAAU,WAAW,GAAG,YAAY;oBAC3C,WAAW,UAAU,WAAW;gBAClC;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,oCAAoC;QAChD,OAAO;IACT;IAEA,2CAA2C;IAC3C,gBAAgB,OAAO;QACrB,IAAI;YACF,QAAQ,GAAG,CAAC,iDAAiD;YAE7D,kCAAkC;YAClC,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YAC3C,MAAM,eAAe,MAAM,MAAM,CAAC,CAAA;gBAChC,IAAI,CAAC,KAAK,OAAO,EAAE,OAAO;gBAC1B,MAAM,WAAW,IAAI,KAAK,KAAK,OAAO,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;gBACnE,OAAO,aAAa;YACtB;YAEA,6BAA6B;YAC7B,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,SAAS,CAAC;YAEzD,QAAQ,GAAG,CAAC,6CAA6C;gBAAE,OAAO;gBAAc;YAAW;YAE3F,OAAO;gBACL,OAAO;gBACP,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kDAAkD;YAChE,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,uBAAuB;QACrB,IAAI;YACF,MAAM,MAAM,IAAI;YAChB,MAAM,eAAe,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,IAAI;YACjE,MAAM,aAAa,IAAI,KAAK,IAAI,WAAW,IAAI,IAAI,QAAQ,KAAK,GAAG;YAEnE,MAAM,QAAQ,aAAa,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YACtD,MAAM,MAAM,WAAW,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;YAElD,MAAM,eAAe,MAAM,gBAAgB,eAAe,CAAC,OAAO;YAClE,OAAO,gBAAgB,oBAAoB,CAAC,aAAa,KAAK,EAAE,aAAa,UAAU;QACzF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uDAAuD;YACrE,gCAAgC;YAChC,IAAI;gBACF,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;gBAC3C,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;gBACrD,OAAO,gBAAgB,oBAAoB,CAAC,OAAO;YACrD,EAAE,OAAO,eAAe;gBACtB,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,OAAO,EAAE;YACX;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 7760, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/utils/cleanup-utils.ts"], "sourcesContent": ["import { TimeBlockService } from '../services/time-block-service';\r\nimport { TaskService } from '../services/task-service';\r\n\r\n/**\r\n * Dọn dẹp các TimeBlock có taskId không hợp lệ (task đã bị xóa)\r\n */\r\nexport const cleanupInvalidTimeBlocks = async () => {\r\n  try {\r\n    console.log('CleanupUtils: Bắt đầu dọn dẹp TimeBlocks không hợp lệ...');\r\n    \r\n    // Lấy tất cả time blocks\r\n    const timeBlocks = await TimeBlockService.getTimeBlocks();\r\n    \r\n    // Lọc ra những time blocks có taskId\r\n    const timeBlocksWithTasks = timeBlocks.filter(tb => tb.taskId);\r\n    \r\n    if (timeBlocksWithTasks.length === 0) {\r\n      console.log('CleanupUtils: Không có TimeBlock nào có taskId để kiểm tra');\r\n      return 0;\r\n    }\r\n    \r\n    console.log(`CleanupUtils: Kiểm tra ${timeBlocksWithTasks.length} TimeBlocks có taskId...`);\r\n    \r\n    let cleanedCount = 0;\r\n    \r\n    // Kiểm tra từng time block\r\n    for (const timeBlock of timeBlocksWithTasks) {\r\n      try {\r\n        const task = await TaskService.getTask(timeBlock.taskId!);\r\n        \r\n        // Nếu task không tồn tại, xóa taskId khỏi timeBlock\r\n        if (!task) {\r\n          console.log(`CleanupUtils: Dọn dẹp TimeBlock \"${timeBlock.title}\" - task ID không hợp lệ: ${timeBlock.taskId}`);\r\n\r\n          await TimeBlockService.updateTimeBlock({\r\n            ...timeBlock,\r\n            taskId: null\r\n          });\r\n\r\n          cleanedCount++;\r\n        }\r\n      } catch (error) {\r\n        console.error(`CleanupUtils: Lỗi khi kiểm tra task ${timeBlock.taskId}:`, error);\r\n      }\r\n    }\r\n    \r\n    if (cleanedCount > 0) {\r\n      console.log(`CleanupUtils: Đã dọn dẹp ${cleanedCount} TimeBlocks có taskId không hợp lệ`);\r\n    } else {\r\n      console.log('CleanupUtils: Tất cả TimeBlocks đều có taskId hợp lệ');\r\n    }\r\n    \r\n    return cleanedCount;\r\n  } catch (error) {\r\n    console.error('CleanupUtils: Lỗi khi dọn dẹp TimeBlocks:', error);\r\n    return 0;\r\n  }\r\n};\r\n\r\n/**\r\n * Kiểm tra và báo cáo các TimeBlock có vấn đề mà không sửa\r\n */\r\nexport const validateTimeBlocks = async () => {\r\n  try {\r\n    const timeBlocks = await TimeBlockService.getTimeBlocks();\r\n    const timeBlocksWithTasks = timeBlocks.filter(tb => tb.taskId);\r\n    \r\n    const invalidTimeBlocks = [];\r\n    \r\n    for (const timeBlock of timeBlocksWithTasks) {\r\n      try {\r\n        const task = await TaskService.getTask(timeBlock.taskId!);\r\n        if (!task) {\r\n          invalidTimeBlocks.push({\r\n            timeBlockId: timeBlock.id,\r\n            timeBlockTitle: timeBlock.title,\r\n            invalidTaskId: timeBlock.taskId\r\n          });\r\n        }\r\n      } catch (error) {\r\n        invalidTimeBlocks.push({\r\n          timeBlockId: timeBlock.id,\r\n          timeBlockTitle: timeBlock.title,\r\n          invalidTaskId: timeBlock.taskId,\r\n          error: error\r\n        });\r\n      }\r\n    }\r\n    \r\n    return {\r\n      total: timeBlocksWithTasks.length,\r\n      invalid: invalidTimeBlocks.length,\r\n      invalidTimeBlocks\r\n    };\r\n  } catch (error) {\r\n    console.error('CleanupUtils: Lỗi khi validate TimeBlocks:', error);\r\n    return {\r\n      total: 0,\r\n      invalid: 0,\r\n      invalidTimeBlocks: [],\r\n      error\r\n    };\r\n  }\r\n};\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAKO,MAAM,2BAA2B;IACtC,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,yBAAyB;QACzB,MAAM,aAAa,MAAM,kJAAA,CAAA,mBAAgB,CAAC,aAAa;QAEvD,qCAAqC;QACrC,MAAM,sBAAsB,WAAW,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM;QAE7D,IAAI,oBAAoB,MAAM,KAAK,GAAG;YACpC,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,oBAAoB,MAAM,CAAC,wBAAwB,CAAC;QAE1F,IAAI,eAAe;QAEnB,2BAA2B;QAC3B,KAAK,MAAM,aAAa,oBAAqB;YAC3C,IAAI;gBACF,MAAM,OAAO,MAAM,yIAAA,CAAA,cAAW,CAAC,OAAO,CAAC,UAAU,MAAM;gBAEvD,oDAAoD;gBACpD,IAAI,CAAC,MAAM;oBACT,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,UAAU,KAAK,CAAC,0BAA0B,EAAE,UAAU,MAAM,EAAE;oBAE9G,MAAM,kJAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;wBACrC,GAAG,SAAS;wBACZ,QAAQ;oBACV;oBAEA;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,oCAAoC,EAAE,UAAU,MAAM,CAAC,CAAC,CAAC,EAAE;YAC5E;QACF;QAEA,IAAI,eAAe,GAAG;YACpB,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,aAAa,kCAAkC,CAAC;QAC1F,OAAO;YACL,QAAQ,GAAG,CAAC;QACd;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO;IACT;AACF;AAKO,MAAM,qBAAqB;IAChC,IAAI;QACF,MAAM,aAAa,MAAM,kJAAA,CAAA,mBAAgB,CAAC,aAAa;QACvD,MAAM,sBAAsB,WAAW,MAAM,CAAC,CAAA,KAAM,GAAG,MAAM;QAE7D,MAAM,oBAAoB,EAAE;QAE5B,KAAK,MAAM,aAAa,oBAAqB;YAC3C,IAAI;gBACF,MAAM,OAAO,MAAM,yIAAA,CAAA,cAAW,CAAC,OAAO,CAAC,UAAU,MAAM;gBACvD,IAAI,CAAC,MAAM;oBACT,kBAAkB,IAAI,CAAC;wBACrB,aAAa,UAAU,EAAE;wBACzB,gBAAgB,UAAU,KAAK;wBAC/B,eAAe,UAAU,MAAM;oBACjC;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,kBAAkB,IAAI,CAAC;oBACrB,aAAa,UAAU,EAAE;oBACzB,gBAAgB,UAAU,KAAK;oBAC/B,eAAe,UAAU,MAAM;oBAC/B,OAAO;gBACT;YACF;QACF;QAEA,OAAO;YACL,OAAO,oBAAoB,MAAM;YACjC,SAAS,kBAAkB,MAAM;YACjC;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO;YACL,OAAO;YACP,SAAS;YACT,mBAAmB,EAAE;YACrB;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 7854, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/app/calendar/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { Layout } from '@/components/layout/layout';\r\nimport { CalendarView } from '@/components/calendar/calendar-view';\r\nimport ProjectSelector from '@/components/project/ProjectSelector';\r\nimport { useEffect, useState } from 'react';\r\nimport { Task, TimeBlock } from '@/lib/types';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport { TimeBlockService } from '@/lib/services/time-block-service';\r\nimport { CalendarService, CalendarEvent } from '@/lib/services/calendar-service';\r\nimport { Skeleton } from '@/components/ui/skeleton';\r\nimport { Alert, AlertDescription } from '@/components/ui/alert';\r\nimport { Button } from '@/components/ui/button';\r\nimport { RefreshCw, AlertCircle, Trash2, CheckCircle } from 'lucide-react';\r\nimport { cleanupInvalidTimeBlocks } from '@/lib/utils/cleanup-utils';\r\n\r\nexport default function CalendarPage() {\r\n  const [tasks, setTasks] = useState<Task[]>([]);\r\n  const [timeBlocks, setTimeBlocks] = useState<TimeBlock[]>([]);\r\n  const [events, setEvents] = useState<CalendarEvent[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [cleanupLoading, setCleanupLoading] = useState<boolean>(false);\r\n  const [successMessage, setSuccessMessage] = useState<string | null>(null);\r\n\r\n  // Load calendar data từ API\r\n  const loadCalendarData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n      console.log('CalendarPage: Đang tải dữ liệu lịch...');\r\n\r\n      // Load tasks và time blocks song song\r\n      const [fetchedTasks, fetchedTimeBlocks] = await Promise.all([\r\n        TaskService.getTasks(),\r\n        TimeBlockService.getTimeBlocks(),\r\n      ]);\r\n\r\n      setTasks(fetchedTasks);\r\n      setTimeBlocks(fetchedTimeBlocks);\r\n\r\n      // Tạo events từ tasks và time blocks\r\n      const calendarEvents = CalendarService.createEventsFromData(fetchedTasks, fetchedTimeBlocks);\r\n      setEvents(calendarEvents);\r\n\r\n      console.log('CalendarPage: Đã tải dữ liệu lịch thành công:', {\r\n        tasks: fetchedTasks.length,\r\n        timeBlocks: fetchedTimeBlocks.length,\r\n        events: calendarEvents.length\r\n      });\r\n    } catch (err: any) {\r\n      console.error('CalendarPage: Lỗi khi tải dữ liệu lịch:', err);\r\n      setError(err.message || 'Không thể tải dữ liệu lịch');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Callback để refresh data khi có thay đổi\r\n  const handleDataChange = async () => {\r\n    console.log('CalendarPage: Data changed, reloading...');\r\n    await loadCalendarData();\r\n  };\r\n\r\n  // Dọn dẹp TimeBlocks không hợp lệ\r\n  const handleCleanup = async () => {\r\n    try {\r\n      setCleanupLoading(true);\r\n      setError(null);\r\n      setSuccessMessage(null);\r\n\r\n      const cleanedCount = await cleanupInvalidTimeBlocks();\r\n\r\n      if (cleanedCount && cleanedCount > 0) {\r\n        // Reload data sau khi cleanup\r\n        await loadCalendarData();\r\n        setSuccessMessage(`Đã dọn dẹp ${cleanedCount} khối thời gian có liên kết không hợp lệ`);\r\n      } else {\r\n        setSuccessMessage('Không có khối thời gian nào cần dọn dẹp. Tất cả liên kết đều hợp lệ.');\r\n      }\r\n    } catch (error: any) {\r\n      console.error('Lỗi khi dọn dẹp:', error);\r\n      setError(error.message || 'Có lỗi xảy ra khi dọn dẹp');\r\n    } finally {\r\n      setCleanupLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    loadCalendarData();\r\n  }, []);\r\n  \r\n  return (\r\n    <Layout>\r\n      <div className=\"max-w-6xl mx-auto\">\r\n        {/* Header với refresh button */}\r\n        <div className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between p-4 md:p-6 pb-2\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold tracking-tight\">Lịch công việc</h1>\r\n            <p className=\"text-muted-foreground\">\r\n              Quản lý công việc và khối thời gian của bạn\r\n            </p>\r\n          </div>\r\n          <div className=\"flex gap-2 mt-4 sm:mt-0\">\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={handleCleanup}\r\n              disabled={loading || cleanupLoading}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <Trash2 className={`h-4 w-4 ${cleanupLoading ? 'animate-pulse' : ''}`} />\r\n              Dọn dẹp\r\n            </Button>\r\n            <Button\r\n              variant=\"outline\"\r\n              size=\"sm\"\r\n              onClick={loadCalendarData}\r\n              disabled={loading || cleanupLoading}\r\n              className=\"flex items-center gap-2\"\r\n            >\r\n              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />\r\n              Làm mới\r\n            </Button>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Error alert */}\r\n        {error && (\r\n          <div className=\"px-4 md:px-6 pb-4\">\r\n            <Alert variant=\"destructive\">\r\n              <AlertCircle className=\"h-4 w-4\" />\r\n              <AlertDescription className=\"flex items-center justify-between\">\r\n                <span>{error}</span>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={loadCalendarData}\r\n                  className=\"flex items-center gap-1\"\r\n                >\r\n                  <RefreshCw className=\"h-3 w-3\" />\r\n                  Thử lại\r\n                </Button>\r\n              </AlertDescription>\r\n            </Alert>\r\n          </div>\r\n        )}\r\n\r\n        {/* Success alert */}\r\n        {successMessage && (\r\n          <div className=\"px-4 md:px-6 pb-4\">\r\n            <Alert className=\"border-green-200 bg-green-50 text-green-800 dark:border-green-800 dark:bg-green-950 dark:text-green-200\">\r\n              <CheckCircle className=\"h-4 w-4\" />\r\n              <AlertDescription className=\"flex items-center justify-between\">\r\n                <span>{successMessage}</span>\r\n                <Button\r\n                  variant=\"outline\"\r\n                  size=\"sm\"\r\n                  onClick={() => setSuccessMessage(null)}\r\n                  className=\"flex items-center gap-1\"\r\n                >\r\n                  ✕\r\n                </Button>\r\n              </AlertDescription>\r\n            </Alert>\r\n          </div>\r\n        )}\r\n\r\n        {/* Content */}\r\n        {loading ? (\r\n          <div className=\"p-4 md:p-6\">\r\n            <div className=\"space-y-4\">\r\n              <Skeleton className=\"h-8 w-full\" />\r\n              <Skeleton className=\"h-8 w-3/4\" />\r\n              <Skeleton className=\"h-8 w-5/6\" />\r\n            </div>\r\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 mt-6\">\r\n              <Skeleton className=\"h-40\" />\r\n              <Skeleton className=\"h-40\" />\r\n            </div>\r\n          </div>\r\n        ) : (\r\n          <CalendarView\r\n            tasks={tasks}\r\n            setTasks={setTasks}\r\n            timeBlocks={timeBlocks}\r\n            setTimeBlocks={setTimeBlocks}\r\n            events={events}\r\n            onDataChange={handleDataChange}\r\n          />\r\n        )}\r\n      </div>\r\n    </Layout>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAdA;;;;;;;;;;;;;AAgBe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,EAAE;IAC5D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC9D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEpE,4BAA4B;IAC5B,MAAM,mBAAmB;QACvB,IAAI;YACF,WAAW;YACX,SAAS;YACT,QAAQ,GAAG,CAAC;YAEZ,sCAAsC;YACtC,MAAM,CAAC,cAAc,kBAAkB,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC1D,yIAAA,CAAA,cAAW,CAAC,QAAQ;gBACpB,kJAAA,CAAA,mBAAgB,CAAC,aAAa;aAC/B;YAED,SAAS;YACT,cAAc;YAEd,qCAAqC;YACrC,MAAM,iBAAiB,6IAAA,CAAA,kBAAe,CAAC,oBAAoB,CAAC,cAAc;YAC1E,UAAU;YAEV,QAAQ,GAAG,CAAC,iDAAiD;gBAC3D,OAAO,aAAa,MAAM;gBAC1B,YAAY,kBAAkB,MAAM;gBACpC,QAAQ,eAAe,MAAM;YAC/B;QACF,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,2CAA2C;YACzD,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,2CAA2C;IAC3C,MAAM,mBAAmB;QACvB,QAAQ,GAAG,CAAC;QACZ,MAAM;IACR;IAEA,kCAAkC;IAClC,MAAM,gBAAgB;QACpB,IAAI;YACF,kBAAkB;YAClB,SAAS;YACT,kBAAkB;YAElB,MAAM,eAAe,MAAM,CAAA,GAAA,uIAAA,CAAA,2BAAwB,AAAD;YAElD,IAAI,gBAAgB,eAAe,GAAG;gBACpC,8BAA8B;gBAC9B,MAAM;gBACN,kBAAkB,CAAC,WAAW,EAAE,aAAa,wCAAwC,CAAC;YACxF,OAAO;gBACL,kBAAkB;YACpB;QACF,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oBAAoB;YAClC,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,kBAAkB;QACpB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,sIAAA,CAAA,SAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAW,CAAC,QAAQ,EAAE,iBAAiB,kBAAkB,IAAI;;;;;;wCAAI;;;;;;;8CAG3E,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;oCACT,UAAU,WAAW;oCACrB,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAW,CAAC,QAAQ,EAAE,UAAU,iBAAiB,IAAI;;;;;;wCAAI;;;;;;;;;;;;;;;;;;;gBAOzE,uBACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,SAAQ;;0CACb,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;gCAAC,WAAU;;kDAC1B,8OAAC;kDAAM;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;;;;;;;gBAS1C,gCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;wBAAC,WAAU;;0CACf,8OAAC,2NAAA,CAAA,cAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC,iIAAA,CAAA,mBAAgB;gCAAC,WAAU;;kDAC1B,8OAAC;kDAAM;;;;;;kDACP,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB;wCACjC,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;gBASR,wBACC,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;sCAEtB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;;;;;;;;;;;;yCAIxB,8OAAC,kJAAA,CAAA,eAAY;oBACX,OAAO;oBACP,UAAU;oBACV,YAAY;oBACZ,eAAe;oBACf,QAAQ;oBACR,cAAc;;;;;;;;;;;;;;;;;AAM1B", "debugId": null}}]}