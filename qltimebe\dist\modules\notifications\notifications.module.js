"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationsModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const config_1 = require("@nestjs/config");
const notifications_controller_1 = require("./notifications.controller");
const notifications_service_1 = require("./services/notifications.service");
const email_service_1 = require("./services/email.service");
const scheduler_service_1 = require("./services/scheduler.service");
const email_subscription_schema_1 = require("./schemas/email-subscription.schema");
const public_email_subscription_schema_1 = require("./schemas/public-email-subscription.schema");
const tasks_module_1 = require("../tasks/tasks.module");
const users_module_1 = require("../users/users.module");
let NotificationsModule = class NotificationsModule {
};
exports.NotificationsModule = NotificationsModule;
exports.NotificationsModule = NotificationsModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: email_subscription_schema_1.EmailSubscription.name, schema: email_subscription_schema_1.EmailSubscriptionSchema },
                { name: public_email_subscription_schema_1.PublicEmailSubscription.name, schema: public_email_subscription_schema_1.PublicEmailSubscriptionSchema },
            ]),
            config_1.ConfigModule,
            tasks_module_1.TasksModule,
            users_module_1.UsersModule,
        ],
        controllers: [notifications_controller_1.NotificationsController],
        providers: [notifications_service_1.NotificationsService, email_service_1.EmailService, scheduler_service_1.SchedulerService],
        exports: [notifications_service_1.NotificationsService, email_service_1.EmailService, scheduler_service_1.SchedulerService],
    })
], NotificationsModule);
//# sourceMappingURL=notifications.module.js.map