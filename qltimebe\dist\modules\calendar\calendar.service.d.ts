import { TasksService } from '../tasks/tasks.service';
import { TimeBlocksService } from '../time-blocks/time-blocks.service';
export declare class CalendarService {
    private readonly tasksService;
    private readonly timeBlocksService;
    constructor(tasksService: TasksService, timeBlocksService: TimeBlocksService);
    getCalendarData(userId: string, start: string, end: string): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
    getDayData(userId: string, date: string): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
    getWeekData(userId: string, date: string): Promise<{
        tasks: import("../tasks/schemas/task.schema").Task[];
        timeBlocks: import("../time-blocks/schemas/time-block.schema").TimeBlock[];
    }>;
}
