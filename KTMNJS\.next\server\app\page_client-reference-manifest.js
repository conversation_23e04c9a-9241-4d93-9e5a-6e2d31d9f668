globalThis.__RSC_MANIFEST = globalThis.__RSC_MANIFEST || {};
globalThis.__RSC_MANIFEST["/page"] = {"moduleLoading":{"prefix":"/_next/","crossOrigin":null},"clientModules":{"[project]/node_modules/next/dist/esm/client/components/layout-router.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/layout-router.js":{"id":"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js":{"id":"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-page.js":{"id":"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/client-segment.js":{"id":"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/node_modules/next/dist/client/components/error-boundary.js":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"async":false},"[project]/src/app/providers.tsx <module evaluation>":{"id":"[project]/src/app/providers.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/app/providers.tsx":{"id":"[project]/src/app/providers.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/components/app-initializer.tsx <module evaluation>":{"id":"[project]/src/components/app-initializer.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/components/app-initializer.tsx":{"id":"[project]/src/components/app-initializer.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"async":false},"[project]/src/components/layout/layout.tsx <module evaluation>":{"id":"[project]/src/components/layout/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f8b57250._.js","static/chunks/src_4a908eaf._.js","static/chunks/node_modules_framer-motion_dist_es_728b44ff._.js","static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js","static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js","static/chunks/node_modules_highlight_d98bfb5f.js","static/chunks/node_modules_@radix-ui_513098dc._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_fc1f2046._.js","static/chunks/src_app_page_tsx_d0bf8d82._.js"],"async":false},"[project]/src/components/layout/layout.tsx":{"id":"[project]/src/components/layout/layout.tsx [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f8b57250._.js","static/chunks/src_4a908eaf._.js","static/chunks/node_modules_framer-motion_dist_es_728b44ff._.js","static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js","static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js","static/chunks/node_modules_highlight_d98bfb5f.js","static/chunks/node_modules_@radix-ui_513098dc._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_fc1f2046._.js","static/chunks/src_app_page_tsx_d0bf8d82._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js <module evaluation>":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f8b57250._.js","static/chunks/src_4a908eaf._.js","static/chunks/node_modules_framer-motion_dist_es_728b44ff._.js","static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js","static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js","static/chunks/node_modules_highlight_d98bfb5f.js","static/chunks/node_modules_@radix-ui_513098dc._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_fc1f2046._.js","static/chunks/src_app_page_tsx_d0bf8d82._.js"],"async":false},"[project]/node_modules/next/dist/client/app-dir/link.js":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)","name":"*","chunks":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f8b57250._.js","static/chunks/src_4a908eaf._.js","static/chunks/node_modules_framer-motion_dist_es_728b44ff._.js","static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js","static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js","static/chunks/node_modules_highlight_d98bfb5f.js","static/chunks/node_modules_@radix-ui_513098dc._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_fc1f2046._.js","static/chunks/src_app_page_tsx_d0bf8d82._.js"],"async":false}},"ssrModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/node_modules_ce9aef9d._.js","server/chunks/ssr/[root of the server]__29912de3._.js"],"async":false}},"[project]/src/app/providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/providers.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__10869f76._.js"],"async":false}},"[project]/src/components/app-initializer.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/app-initializer.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__10869f76._.js"],"async":false}},"[project]/src/components/layout/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/layout.tsx [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__10869f76._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_adc6bb7c._.js","server/chunks/ssr/[root of the server]__c6f324cf._.js","server/chunks/ssr/node_modules_next_dist_b96c9f82._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_b2dd6e3b._.js","server/chunks/ssr/node_modules_@radix-ui_react-icons_dist_react-icons_esm_305d8165.js","server/chunks/ssr/node_modules_micromark-core-commonmark_dev_lib_fbbb9cae._.js","server/chunks/ssr/node_modules_highlight_27b10e40.js","server/chunks/ssr/node_modules_@radix-ui_c6be60f0._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_740de9d1._.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js [app-ssr] (ecmascript)","name":"*","chunks":["server/chunks/ssr/[root of the server]__10869f76._.js","server/chunks/ssr/[turbopack]_browser_dev_hmr-client_hmr-client_ts_adc6bb7c._.js","server/chunks/ssr/[root of the server]__c6f324cf._.js","server/chunks/ssr/node_modules_next_dist_b96c9f82._.js","server/chunks/ssr/node_modules_framer-motion_dist_es_b2dd6e3b._.js","server/chunks/ssr/node_modules_@radix-ui_react-icons_dist_react-icons_esm_305d8165.js","server/chunks/ssr/node_modules_micromark-core-commonmark_dev_lib_fbbb9cae._.js","server/chunks/ssr/node_modules_highlight_27b10e40.js","server/chunks/ssr/node_modules_@radix-ui_c6be60f0._.js","server/chunks/ssr/node_modules_@floating-ui_90d70670._.js","server/chunks/ssr/node_modules_740de9d1._.js"],"async":false}}},"edgeSSRModuleMapping":{},"rscModuleMapping":{"[project]/node_modules/next/dist/client/components/layout-router.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/layout-router.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/render-from-template-context.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/render-from-template-context.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-page.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-page.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/client-segment.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/client-segment.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/http-access-fallback/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/http-access-fallback/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/async-metadata.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/async-metadata.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/error-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/components/error-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/components/metadata/metadata-boundary.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/esm/client/components/metadata/metadata-boundary.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/app/providers.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/app/providers.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/app-initializer.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/app-initializer.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/src/components/layout/layout.tsx [app-client] (ecmascript)":{"*":{"id":"[project]/src/components/layout/layout.tsx (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}},"[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)":{"*":{"id":"[project]/node_modules/next/dist/client/app-dir/link.js (client reference/proxy)","name":"*","chunks":["server/app/page.js"],"async":false}}},"edgeRscModuleMapping":{},"entryCSSFiles":{"[project]/src/app/favicon.ico":[],"[project]/src/app/layout":[{"path":"static/chunks/[root of the server]__08514339._.css","inlined":false}],"[project]/src/app/page":[{"path":"static/chunks/[root of the server]__08514339._.css","inlined":false},{"path":"static/chunks/node_modules_highlight_js_styles_github-dark_dbb8ae5a.css","inlined":false}]},"entryJSFiles":{"[project]/src/app/favicon.ico":["static/chunks/node_modules_next_dist_1a6ee436._.js","static/chunks/src_app_favicon_ico_mjs_79b6a596._.js"],"[project]/src/app/layout":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js"],"[project]/src/app/page":["static/chunks/_9e817198._.js","static/chunks/src_app_layout_tsx_f0e4c1a2._.js","static/chunks/[turbopack]_browser_dev_hmr-client_hmr-client_ts_f8b57250._.js","static/chunks/src_4a908eaf._.js","static/chunks/node_modules_framer-motion_dist_es_728b44ff._.js","static/chunks/node_modules_@radix-ui_react-icons_dist_react-icons_esm_ecd85ef9.js","static/chunks/node_modules_micromark-core-commonmark_dev_lib_36a4b45d._.js","static/chunks/node_modules_highlight_d98bfb5f.js","static/chunks/node_modules_@radix-ui_513098dc._.js","static/chunks/node_modules_@floating-ui_9ec1fa39._.js","static/chunks/node_modules_fc1f2046._.js","static/chunks/src_app_page_tsx_d0bf8d82._.js"]}}
