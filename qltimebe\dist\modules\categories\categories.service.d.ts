import { Model } from 'mongoose';
import { Category } from './schemas/category.schema';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
export declare class CategoriesService {
    private categoryModel;
    constructor(categoryModel: Model<Category>);
    create(createCategoryDto: CreateCategoryDto, userId: string): Promise<Category>;
    findAll(userId: string): Promise<Category[]>;
    findById(id: string, userId: string): Promise<Category>;
    update(id: string, updateCategoryDto: UpdateCategoryDto, userId: string): Promise<Category>;
    remove(id: string, userId: string): Promise<void>;
}
