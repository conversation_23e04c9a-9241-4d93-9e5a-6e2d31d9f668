{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 24, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx <module evaluation>\",\n    \"Layout\",\n);\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx <module evaluation>\",\n    \"Logo\",\n);\nexport const LogoIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoIcon() from the server but LogoIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx <module evaluation>\",\n    \"LogoIcon\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,kEACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,kEACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,kEACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/layout.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Layout = registerClientReference(\n    function() { throw new Error(\"Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx\",\n    \"Layout\",\n);\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx\",\n    \"Logo\",\n);\nexport const LogoIcon = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoIcon() from the server but LogoIcon is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/layout.tsx\",\n    \"LogoIcon\",\n);\n"], "names": [], "mappings": ";;;;;AAAA;;AACO,MAAM,SAAS,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACxC;IAAa,MAAM,IAAI,MAAM;AAA4N,GACzP,8CACA;AAEG,MAAM,OAAO,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,8CACA;AAEG,MAAM,WAAW,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC1C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,8CACA", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 154, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/app/page.tsx"], "sourcesContent": ["import { Layout } from '@/components/layout/layout';\nimport { Button } from '@/components/ui/button';\nimport { ArrowRight } from 'lucide-react';\nimport Link from 'next/link';\n\nexport default function Home() {\n  return (\n    <Layout>\n      <section className=\"py-12 md:py-24 lg:py-32 xl:py-40\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"flex flex-col items-center justify-center space-y-6 text-center\">\n            <div className=\"space-y-3\">\n              <h1 className=\"text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl xl:text-7xl\">\n                Qu<PERSON>n lý thời gian hiệu quả\n              </h1>\n              <p className=\"mx-auto max-w-[800px] text-gray-500 md:text-xl dark:text-gray-400\">\n                QLTime giúp bạn sắp xếp công việc, lên lịch hợp lý và đạt đư<PERSON><PERSON> hiệu suất cao nhất trong cuộc sống hàng ngày.\n              </p>\n            </div>\n            <div className=\"space-x-4\">\n              <Link href=\"/tasks\">\n                <Button size=\"lg\" className=\"h-12 px-6 rounded-lg\">\n                  Bắt đầu ngay\n                  <ArrowRight className=\"ml-2 h-5 w-5\" />\n                </Button>\n              </Link>\n            </div>\n          </div>\n        </div>\n      </section>\n\n      <section className=\"py-12 md:py-24 lg:py-32 backdrop-blur-sm bg-background/40 rounded-xl border border-border/40\">\n        <div className=\"container px-4 md:px-6\">\n          <div className=\"grid gap-10 px-4 sm:px-6 md:gap-16 lg:grid-cols-2\">\n            <div className=\"space-y-4\">\n              <div className=\"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm\">\n                Theo dõi công việc\n              </div>\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">\n                Quản lý danh sách công việc\n              </h2>\n              <p className=\"max-w-[600px] text-gray-500 md:text-xl/relaxed dark:text-gray-400\">\n                Tạo và quản lý các công việc một cách dễ dàng. Thiết lập hạn hoàn thành, ưu tiên và danh mục để tổ chức công việc hiệu quả.\n              </p>\n            </div>\n            <div className=\"space-y-4\">\n              <div className=\"inline-block rounded-lg bg-primary/10 px-3 py-1 text-sm\">\n                Lập lịch thông minh\n              </div>\n              <h2 className=\"text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl\">\n                Lên lịch và nhắc nhở\n              </h2>\n              <p className=\"max-w-[600px] text-gray-500 md:text-xl/relaxed dark:text-gray-400\">\n                Tạo khung giờ cụ thể cho từng công việc để tối ưu hóa thời gian của bạn. Nhận thông báo nhắc nhở để không bỏ lỡ bất kỳ nhiệm vụ quan trọng nào.\n              </p>\n            </div>\n          </div>\n        </div>\n      </section>\n    </Layout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,sIAAA,CAAA,SAAM;;0BACL,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0E;;;;;;kDAGxF,8OAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAInF,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,WAAU;;4CAAuB;0DAEjD,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,8OAAC;gBAAQ,WAAU;0BACjB,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0D;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,8OAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAInF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDAA0D;;;;;;kDAGzE,8OAAC;wCAAG,WAAU;kDAA8D;;;;;;kDAG5E,8OAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/F", "debugId": null}}]}