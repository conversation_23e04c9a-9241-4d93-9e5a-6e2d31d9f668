import { CategoriesService } from './categories.service';
import { CreateCategoryDto } from './dto/create-category.dto';
import { UpdateCategoryDto } from './dto/update-category.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class CategoriesController {
    private readonly categoriesService;
    constructor(categoriesService: CategoriesService);
    create(createCategoryDto: CreateCategoryDto, user: UserDocument): Promise<import("./schemas/category.schema").Category>;
    findAll(user: UserDocument): Promise<import("./schemas/category.schema").Category[]>;
    findOne(id: string, user: UserDocument): Promise<import("./schemas/category.schema").Category>;
    update(id: string, updateCategoryDto: UpdateCategoryDto, user: UserDocument): Promise<import("./schemas/category.schema").Category>;
    remove(id: string, user: UserDocument): Promise<{
        success: boolean;
    }>;
}
