import { TasksService } from '../tasks/tasks.service';
import { TimeBlocksService } from '../time-blocks/time-blocks.service';
export declare class StatisticsService {
    private readonly tasksService;
    private readonly timeBlocksService;
    constructor(tasksService: TasksService, timeBlocksService: TimeBlocksService);
    getTasksStatistics(userId: string, startDate?: string, endDate?: string): Promise<{
        total: number;
        completed: number;
        pending: number;
        overdue: number;
        byPriority: {
            low: number;
            medium: number;
            high: number;
        };
        byStatus: {
            backlog: number;
            todo: number;
            doing: number;
            done: number;
        };
        byCategory: {};
    }>;
    getTimeBlocksStatistics(userId: string, startDate?: string, endDate?: string): Promise<{
        totalHours: number;
        completedHours: number;
        completionRate: number;
        byDay: {};
    }>;
    getProductivityStatistics(userId: string, startDate?: string, endDate?: string): Promise<{
        productivityScore: number;
        taskCompletionRate: number;
        timeBlockCompletionRate: number;
        dailyScores: {};
    }>;
}
