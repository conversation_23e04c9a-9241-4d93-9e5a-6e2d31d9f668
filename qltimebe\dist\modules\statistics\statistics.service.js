"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticsService = void 0;
const common_1 = require("@nestjs/common");
const tasks_service_1 = require("../tasks/tasks.service");
const time_blocks_service_1 = require("../time-blocks/time-blocks.service");
let StatisticsService = class StatisticsService {
    tasksService;
    timeBlocksService;
    constructor(tasksService, timeBlocksService) {
        this.tasksService = tasksService;
        this.timeBlocksService = timeBlocksService;
    }
    async getTasksStatistics(userId, startDate, endDate) {
        const start = startDate
            ? new Date(startDate)
            : new Date(new Date().setDate(new Date().getDate() - 30));
        const end = endDate
            ? new Date(endDate)
            : new Date();
        const tasks = await this.tasksService.findAll(userId, {
            createdAt: { $gte: start, $lte: end },
        });
        const completed = tasks.filter(task => task.completed).length;
        const pending = tasks.filter(task => !task.completed).length;
        const overdue = tasks.filter(task => {
            if (!task.dueDate || task.completed)
                return false;
            return new Date(task.dueDate) < new Date();
        }).length;
        const byPriority = {
            low: tasks.filter(task => task.priority === 'low').length,
            medium: tasks.filter(task => task.priority === 'medium').length,
            high: tasks.filter(task => task.priority === 'high').length,
        };
        const byStatus = {
            backlog: tasks.filter(task => task.status === 'backlog').length,
            todo: tasks.filter(task => task.status === 'todo').length,
            doing: tasks.filter(task => task.status === 'doing').length,
            done: tasks.filter(task => task.status === 'done').length,
        };
        const byCategory = {};
        tasks.forEach(task => {
            if (task.category) {
                const categoryName = task.category.name || task.category.toString();
                byCategory[categoryName] = (byCategory[categoryName] || 0) + 1;
            }
            else {
                byCategory['Không có danh mục'] = (byCategory['Không có danh mục'] || 0) + 1;
            }
        });
        return {
            total: tasks.length,
            completed,
            pending,
            overdue,
            byPriority,
            byStatus,
            byCategory,
        };
    }
    async getTimeBlocksStatistics(userId, startDate, endDate) {
        const start = startDate
            ? new Date(startDate)
            : new Date(new Date().setDate(new Date().getDate() - 30));
        const end = endDate
            ? new Date(endDate)
            : new Date();
        const timeBlocks = await this.timeBlocksService.findAll(userId);
        const filteredTimeBlocks = timeBlocks.filter(block => {
            const blockDate = new Date(block.startTime);
            return blockDate >= start && blockDate <= end;
        });
        let totalHours = 0;
        let completedHours = 0;
        filteredTimeBlocks.forEach(block => {
            const startTime = new Date(block.startTime);
            const endTime = new Date(block.endTime);
            const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
            totalHours += durationHours;
            if (block.isCompleted) {
                completedHours += durationHours;
            }
        });
        const byDay = {};
        filteredTimeBlocks.forEach(block => {
            const date = new Date(block.startTime).toISOString().split('T')[0];
            if (!byDay[date]) {
                byDay[date] = {
                    total: 0,
                    completed: 0,
                };
            }
            const startTime = new Date(block.startTime);
            const endTime = new Date(block.endTime);
            const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
            byDay[date].total += durationHours;
            if (block.isCompleted) {
                byDay[date].completed += durationHours;
            }
        });
        return {
            totalHours: parseFloat(totalHours.toFixed(2)),
            completedHours: parseFloat(completedHours.toFixed(2)),
            completionRate: totalHours > 0 ? parseFloat((completedHours / totalHours * 100).toFixed(2)) : 0,
            byDay,
        };
    }
    async getProductivityStatistics(userId, startDate, endDate) {
        const tasksStats = await this.getTasksStatistics(userId, startDate, endDate);
        const timeBlocksStats = await this.getTimeBlocksStatistics(userId, startDate, endDate);
        const taskCompletionRate = tasksStats.total > 0
            ? (tasksStats.completed / tasksStats.total) * 100
            : 0;
        const timeBlockCompletionRate = timeBlocksStats.totalHours > 0
            ? (timeBlocksStats.completedHours / timeBlocksStats.totalHours) * 100
            : 0;
        const productivityScore = (taskCompletionRate * 0.6) + (timeBlockCompletionRate * 0.4);
        const dailyScores = {};
        const allDays = new Set([
            ...Object.keys(timeBlocksStats.byDay),
        ]);
        allDays.forEach(date => {
            const timeBlockData = timeBlocksStats.byDay[date] || { total: 0, completed: 0 };
            const timeBlockScore = timeBlockData.total > 0
                ? (timeBlockData.completed / timeBlockData.total) * 100
                : 0;
            dailyScores[date] = parseFloat(timeBlockScore.toFixed(2));
        });
        return {
            productivityScore: parseFloat(productivityScore.toFixed(2)),
            taskCompletionRate: parseFloat(taskCompletionRate.toFixed(2)),
            timeBlockCompletionRate: parseFloat(timeBlockCompletionRate.toFixed(2)),
            dailyScores,
        };
    }
};
exports.StatisticsService = StatisticsService;
exports.StatisticsService = StatisticsService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [tasks_service_1.TasksService,
        time_blocks_service_1.TimeBlocksService])
], StatisticsService);
//# sourceMappingURL=statistics.service.js.map