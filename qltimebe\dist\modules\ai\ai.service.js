"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
var AIService_1;
Object.defineProperty(exports, "__esModule", { value: true });
exports.AIService = void 0;
const common_1 = require("@nestjs/common");
const config_1 = require("@nestjs/config");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const genkit_1 = require("genkit");
const googleai_1 = require("@genkit-ai/googleai");
const chat_history_schema_1 = require("./schemas/chat-history.schema");
const uuid_1 = require("uuid");
let AIService = AIService_1 = class AIService {
    chatHistoryModel;
    configService;
    logger = new common_1.Logger(AIService_1.name);
    ai;
    constructor(chatHistoryModel, configService) {
        this.chatHistoryModel = chatHistoryModel;
        this.configService = configService;
        this.initializeAI();
    }
    initializeAI() {
        try {
            const apiKey = this.configService.get('GEMINI_API_KEY');
            if (!apiKey) {
                this.logger.warn('GEMINI_API_KEY not found. AI features will be limited.');
                return;
            }
            this.ai = (0, genkit_1.genkit)({
                plugins: [(0, googleai_1.googleAI)()],
                model: googleai_1.gemini25FlashPreview0417,
            });
            this.logger.log('AI service initialized successfully');
        }
        catch (error) {
            this.logger.error('Failed to initialize AI service:', error);
        }
    }
    async chat(userId, chatRequest) {
        try {
            if (!this.ai) {
                throw new Error('AI service not available');
            }
            const sessionId = chatRequest.sessionId || (0, uuid_1.v4)();
            await this.saveChatMessage(userId, 'user', chatRequest.message, sessionId);
            const chatHistory = chatRequest.chatHistory || [];
            const historyContext = chatHistory.length > 0
                ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\n')
                : '';
            const prompt = `
Bạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.

${historyContext ? `Lịch sử cuộc trò chuyện:\n${historyContext}\n` : ''}

Người dùng: ${chatRequest.message}

Hãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:
- Quản lý thời gian
- Tổ chức công việc
- Tăng năng suất
- Lời khuyên thực tế

Trả lời bằng tiếng Việt.`;
            const { text } = await this.ai.generate(prompt);
            await this.saveChatMessage(userId, 'assistant', text, sessionId);
            return {
                response: text,
                sessionId,
                timestamp: new Date(),
            };
        }
        catch (error) {
            this.logger.error('Error in chat:', error);
            throw new Error('Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.');
        }
    }
    async suggestPriority(taskSuggestion) {
        try {
            if (!this.ai) {
                return this.fallbackSuggestPriority(taskSuggestion);
            }
            const prompt = `
Phân tích công việc sau và đề xuất độ ưu tiên (high/medium/low):

Tiêu đề: ${taskSuggestion.title}
Mô tả: ${taskSuggestion.description || 'Không có mô tả'}

Hãy trả về chỉ một từ: "high", "medium", hoặc "low" dựa trên:
- Tính khẩn cấp của công việc
- Tầm quan trọng 
- Deadline ngầm định
- Từ khóa chỉ độ ưu tiên

Chỉ trả về một từ, không giải thích.`;
            const { text } = await this.ai.generate(prompt);
            const response = text.toLowerCase().trim();
            if (['high', 'medium', 'low'].includes(response)) {
                return response;
            }
            return 'medium';
        }
        catch (error) {
            this.logger.error('Error suggesting priority:', error);
            return this.fallbackSuggestPriority(taskSuggestion);
        }
    }
    async suggestDueDate(taskSuggestion) {
        try {
            if (!this.ai) {
                return this.fallbackSuggestDueDate(taskSuggestion);
            }
            const today = new Date();
            const prompt = `
Phân tích công việc sau và đề xuất số ngày cần để hoàn thành:

Tiêu đề: ${taskSuggestion.title}
Mô tả: ${taskSuggestion.description || 'Không có mô tả'}
Ngày hiện tại: ${today.toLocaleDateString('vi-VN')}

Hãy trả về chỉ một số nguyên (1-365) đại diện cho số ngày cần để hoàn thành công việc này.
Xem xét:
- Độ phức tạp của công việc
- Thời gian thông thường cần thiết
- Từ khóa về thời gian (gấp, khẩn, tuần này, tháng này, etc.)

Chỉ trả về số nguyên, không giải thích.`;
            const { text } = await this.ai.generate(prompt);
            const daysToAdd = parseInt(text.trim());
            if (isNaN(daysToAdd) || daysToAdd < 1 || daysToAdd > 365) {
                return this.fallbackSuggestDueDate(taskSuggestion);
            }
            const dueDate = new Date(today);
            dueDate.setDate(today.getDate() + daysToAdd);
            return dueDate.toISOString().split('T')[0];
        }
        catch (error) {
            this.logger.error('Error suggesting due date:', error);
            return this.fallbackSuggestDueDate(taskSuggestion);
        }
    }
    async getChatHistory(userId, sessionId, limit = 50) {
        const query = { userId };
        if (sessionId) {
            query.sessionId = sessionId;
        }
        return this.chatHistoryModel
            .find(query)
            .sort({ timestamp: -1 })
            .limit(limit)
            .exec();
    }
    async clearChatHistory(userId, sessionId) {
        const query = { userId };
        if (sessionId) {
            query.sessionId = sessionId;
        }
        await this.chatHistoryModel.deleteMany(query).exec();
    }
    async saveChatMessage(userId, role, content, sessionId) {
        try {
            const chatMessage = new this.chatHistoryModel({
                userId,
                role,
                content,
                sessionId,
                timestamp: new Date(),
            });
            await chatMessage.save();
        }
        catch (error) {
            this.logger.error('Error saving chat message:', error);
        }
    }
    fallbackSuggestPriority(taskSuggestion) {
        const title = taskSuggestion.title.toLowerCase();
        const description = taskSuggestion.description?.toLowerCase() || '';
        const highPriorityKeywords = ['gấp', 'khẩn', 'ngay', 'quan trọng', 'deadline', 'hạn chót'];
        const lowPriorityKeywords = ['nhẹ nhàng', 'khi rảnh', 'không gấp', 'sau này', 'phụ'];
        for (const keyword of highPriorityKeywords) {
            if (title.includes(keyword) || description.includes(keyword)) {
                return 'high';
            }
        }
        for (const keyword of lowPriorityKeywords) {
            if (title.includes(keyword) || description.includes(keyword)) {
                return 'low';
            }
        }
        return 'medium';
    }
    fallbackSuggestDueDate(taskSuggestion) {
        try {
            const title = taskSuggestion.title.toLowerCase();
            const description = taskSuggestion.description?.toLowerCase() || '';
            const today = new Date();
            let daysToAdd = 7;
            if (title.includes('gấp') || title.includes('khẩn') || description.includes('gấp')) {
                daysToAdd = 1;
            }
            else if (title.includes('tuần này') || description.includes('tuần này')) {
                daysToAdd = 5;
            }
            else if (title.includes('tháng') || description.includes('tháng')) {
                daysToAdd = 30;
            }
            const dueDate = new Date(today);
            dueDate.setDate(today.getDate() + daysToAdd);
            return dueDate.toISOString().split('T')[0];
        }
        catch (error) {
            this.logger.error('Error in fallback due date suggestion:', error);
            return null;
        }
    }
};
exports.AIService = AIService;
exports.AIService = AIService = AIService_1 = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(chat_history_schema_1.ChatHistory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        config_1.ConfigService])
], AIService);
//# sourceMappingURL=ai.service.js.map