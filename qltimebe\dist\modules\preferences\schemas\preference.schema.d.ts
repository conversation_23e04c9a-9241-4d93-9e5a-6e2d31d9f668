import { Document, Schema as MongooseSchema, Types } from 'mongoose';
export type ThemeType = 'light' | 'dark' | 'system';
export type CalendarViewType = 'day' | 'week' | 'month';
export type StartOfWeekType = 0 | 1 | 6;
export type PreferenceDocument = Preference & Document & {
    _id: Types.ObjectId;
};
export declare class Preference {
    user: MongooseSchema.Types.ObjectId;
    theme: ThemeType;
    language: string;
    notifications: boolean;
    calendarView: CalendarViewType;
    startOfWeek: StartOfWeekType;
    showCompletedTasks: boolean;
}
export declare const PreferenceSchema: MongooseSchema<Preference, import("mongoose").Model<Preference, any, any, any, Document<unknown, any, Preference, any> & Preference & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Preference, Document<unknown, {}, import("mongoose").FlatRecord<Preference>, {}> & import("mongoose").FlatRecord<Preference> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
