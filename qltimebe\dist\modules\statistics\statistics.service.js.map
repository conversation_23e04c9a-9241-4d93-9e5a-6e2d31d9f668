{"version": 3, "file": "statistics.service.js", "sourceRoot": "", "sources": ["../../../src/modules/statistics/statistics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,2CAA4C;AAC5C,0DAAsD;AACtD,4EAAuE;AAGhE,IAAM,iBAAiB,GAAvB,MAAM,iBAAiB;IAET;IACA;IAFnB,YACmB,YAA0B,EAC1B,iBAAoC;QADpC,iBAAY,GAAZ,YAAY,CAAc;QAC1B,sBAAiB,GAAjB,iBAAiB,CAAmB;IACpD,CAAC;IAEJ,KAAK,CAAC,kBAAkB,CAAC,MAAc,EAAE,SAAkB,EAAE,OAAgB;QAE3E,MAAM,KAAK,GAAG,SAAS;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAE5D,MAAM,GAAG,GAAG,OAAO;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAGf,MAAM,KAAK,GAAG,MAAM,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC,MAAM,EAAE;YACpD,SAAS,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE;SACtC,CAAC,CAAC;QAGH,MAAM,SAAS,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAC9D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,MAAM,CAAC;QAC7D,MAAM,OAAO,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;YAClC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,SAAS;gBAAE,OAAO,KAAK,CAAC;YAClD,OAAO,IAAI,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7C,CAAC,CAAC,CAAC,MAAM,CAAC;QAGV,MAAM,UAAU,GAAG;YACjB,GAAG,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,CAAC,MAAM;YACzD,MAAM,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,CAAC,CAAC,MAAM;YAC/D,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,KAAK,MAAM,CAAC,CAAC,MAAM;SAC5D,CAAC;QAGF,MAAM,QAAQ,GAAG;YACf,OAAO,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM;YAC/D,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;YACzD,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,OAAO,CAAC,CAAC,MAAM;YAC3D,IAAI,EAAE,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,KAAK,MAAM,CAAC,CAAC,MAAM;SAC1D,CAAC;QAGF,MAAM,UAAU,GAAG,EAAE,CAAC;QACtB,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACnB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAElB,MAAM,YAAY,GAAI,IAAI,CAAC,QAAgB,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,CAAC;gBAC7E,UAAU,CAAC,YAAY,CAAC,GAAG,CAAC,UAAU,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YACjE,CAAC;iBAAM,CAAC;gBACN,UAAU,CAAC,mBAAmB,CAAC,GAAG,CAAC,UAAU,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC,GAAG,CAAC,CAAC;YAC/E,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,MAAM;YACnB,SAAS;YACT,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;YACR,UAAU;SACX,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc,EAAE,SAAkB,EAAE,OAAgB;QAEhF,MAAM,KAAK,GAAG,SAAS;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,SAAS,CAAC;YACrB,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,CAAC,IAAI,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;QAE5D,MAAM,GAAG,GAAG,OAAO;YACjB,CAAC,CAAC,IAAI,IAAI,CAAC,OAAO,CAAC;YACnB,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC;QAGf,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,iBAAiB,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC;QAGhE,MAAM,kBAAkB,GAAG,UAAU,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE;YACnD,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,OAAO,SAAS,IAAI,KAAK,IAAI,SAAS,IAAI,GAAG,CAAC;QAChD,CAAC,CAAC,CAAC;QAGH,IAAI,UAAU,GAAG,CAAC,CAAC;QACnB,IAAI,cAAc,GAAG,CAAC,CAAC;QAEvB,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEnF,UAAU,IAAI,aAAa,CAAC;YAE5B,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtB,cAAc,IAAI,aAAa,CAAC;YAClC,CAAC;QACH,CAAC,CAAC,CAAC;QAGH,MAAM,KAAK,GAAG,EAAE,CAAC;QACjB,kBAAkB,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE;YACjC,MAAM,IAAI,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC,WAAW,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;YAEnE,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;gBACjB,KAAK,CAAC,IAAI,CAAC,GAAG;oBACZ,KAAK,EAAE,CAAC;oBACR,SAAS,EAAE,CAAC;iBACb,CAAC;YACJ,CAAC;YAED,MAAM,SAAS,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,SAAS,CAAC,CAAC;YAC5C,MAAM,OAAO,GAAG,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACxC,MAAM,aAAa,GAAG,CAAC,OAAO,CAAC,OAAO,EAAE,GAAG,SAAS,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;YAEnF,KAAK,CAAC,IAAI,CAAC,CAAC,KAAK,IAAI,aAAa,CAAC;YAEnC,IAAI,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtB,KAAK,CAAC,IAAI,CAAC,CAAC,SAAS,IAAI,aAAa,CAAC;YACzC,CAAC;QACH,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,UAAU,EAAE,UAAU,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7C,cAAc,EAAE,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACrD,cAAc,EAAE,UAAU,GAAG,CAAC,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,cAAc,GAAG,UAAU,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAC/F,KAAK;SACN,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,yBAAyB,CAAC,MAAc,EAAE,SAAkB,EAAE,OAAgB;QAElF,MAAM,UAAU,GAAG,MAAM,IAAI,CAAC,kBAAkB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAC7E,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,MAAM,EAAE,SAAS,EAAE,OAAO,CAAC,CAAC;QAGvF,MAAM,kBAAkB,GAAG,UAAU,CAAC,KAAK,GAAG,CAAC;YAC7C,CAAC,CAAC,CAAC,UAAU,CAAC,SAAS,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,GAAG;YACjD,CAAC,CAAC,CAAC,CAAC;QAEN,MAAM,uBAAuB,GAAG,eAAe,CAAC,UAAU,GAAG,CAAC;YAC5D,CAAC,CAAC,CAAC,eAAe,CAAC,cAAc,GAAG,eAAe,CAAC,UAAU,CAAC,GAAG,GAAG;YACrE,CAAC,CAAC,CAAC,CAAC;QAGN,MAAM,iBAAiB,GAAG,CAAC,kBAAkB,GAAG,GAAG,CAAC,GAAG,CAAC,uBAAuB,GAAG,GAAG,CAAC,CAAC;QAGvF,MAAM,WAAW,GAAG,EAAE,CAAC;QAGvB,MAAM,OAAO,GAAG,IAAI,GAAG,CAAC;YACtB,GAAG,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC;SACtC,CAAC,CAAC;QAEH,OAAO,CAAC,OAAO,CAAC,IAAI,CAAC,EAAE;YACrB,MAAM,aAAa,GAAG,eAAe,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,EAAE,KAAK,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,CAAC;YAChF,MAAM,cAAc,GAAG,aAAa,CAAC,KAAK,GAAG,CAAC;gBAC5C,CAAC,CAAC,CAAC,aAAa,CAAC,SAAS,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,GAAG;gBACvD,CAAC,CAAC,CAAC,CAAC;YAGN,WAAW,CAAC,IAAI,CAAC,GAAG,UAAU,CAAC,cAAc,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5D,CAAC,CAAC,CAAC;QAEH,OAAO;YACL,iBAAiB,EAAE,UAAU,CAAC,iBAAiB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC3D,kBAAkB,EAAE,UAAU,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YAC7D,uBAAuB,EAAE,UAAU,CAAC,uBAAuB,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC;YACvE,WAAW;SACZ,CAAC;IACJ,CAAC;CACF,CAAA;AA/KY,8CAAiB;4BAAjB,iBAAiB;IAD7B,IAAA,mBAAU,GAAE;qCAGsB,4BAAY;QACP,uCAAiB;GAH5C,iBAAiB,CA+K7B"}