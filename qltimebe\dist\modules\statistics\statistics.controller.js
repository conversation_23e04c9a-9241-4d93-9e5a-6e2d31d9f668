"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatisticsController = void 0;
const common_1 = require("@nestjs/common");
const statistics_service_1 = require("./statistics.service");
const statistics_query_dto_1 = require("./dto/statistics-query.dto");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
let StatisticsController = class StatisticsController {
    statisticsService;
    constructor(statisticsService) {
        this.statisticsService = statisticsService;
    }
    async getTasksStatistics(user, query) {
        return await this.statisticsService.getTasksStatistics(user._id.toString(), query.startDate, query.endDate);
    }
    async getTimeBlocksStatistics(user, query) {
        return await this.statisticsService.getTimeBlocksStatistics(user._id.toString(), query.startDate, query.endDate);
    }
    async getProductivityStatistics(user, query) {
        return await this.statisticsService.getProductivityStatistics(user._id.toString(), query.startDate, query.endDate);
    }
};
exports.StatisticsController = StatisticsController;
__decorate([
    (0, common_1.Get)('tasks'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê về công việc' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Ngày bắt đầu (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Ngày kết thúc (YYYY-MM-DD)' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, statistics_query_dto_1.StatisticsQueryDto]),
    __metadata("design:returntype", Promise)
], StatisticsController.prototype, "getTasksStatistics", null);
__decorate([
    (0, common_1.Get)('time-blocks'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê về khối thời gian' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Ngày bắt đầu (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Ngày kết thúc (YYYY-MM-DD)' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, statistics_query_dto_1.StatisticsQueryDto]),
    __metadata("design:returntype", Promise)
], StatisticsController.prototype, "getTimeBlocksStatistics", null);
__decorate([
    (0, common_1.Get)('productivity'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy thống kê về năng suất' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    (0, swagger_1.ApiQuery)({ name: 'startDate', required: false, description: 'Ngày bắt đầu (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'endDate', required: false, description: 'Ngày kết thúc (YYYY-MM-DD)' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, statistics_query_dto_1.StatisticsQueryDto]),
    __metadata("design:returntype", Promise)
], StatisticsController.prototype, "getProductivityStatistics", null);
exports.StatisticsController = StatisticsController = __decorate([
    (0, swagger_1.ApiTags)('Thống kê'),
    (0, common_1.Controller)('api/statistics'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [statistics_service_1.StatisticsService])
], StatisticsController);
//# sourceMappingURL=statistics.controller.js.map