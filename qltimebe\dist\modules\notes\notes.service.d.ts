import { Model } from 'mongoose';
import { Note } from './schemas/note.schema';
import { CreateNoteDto } from './dto/create-note.dto';
import { UpdateNoteDto } from './dto/update-note.dto';
export declare class NotesService {
    private noteModel;
    constructor(noteModel: Model<Note>);
    create(createNoteDto: CreateNoteDto, userId: string): Promise<Note>;
    findAll(userId: string): Promise<Note[]>;
    findById(id: string, userId: string): Promise<Note>;
    update(id: string, updateNoteDto: UpdateNoteDto, userId: string): Promise<Note>;
    remove(id: string, userId: string): Promise<void>;
}
