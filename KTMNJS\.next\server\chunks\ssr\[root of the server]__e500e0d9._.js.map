{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport Link, { LinkProps } from \"next/link\";\nimport React, { useState, createContext, useContext } from \"react\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { Menu, X } from \"lucide-react\";\nimport { useIsMobile } from \"@/lib/hooks/use-mobile\";\n\ninterface Links {\n  label: string;\n  href: string;\n  icon: React.JSX.Element | React.ReactNode;\n}\n\ninterface SidebarContextProps {\n  open: boolean;\n  setOpen: React.Dispatch<React.SetStateAction<boolean>>;\n  animate: boolean;\n}\n\nconst SidebarContext = createContext<SidebarContextProps | undefined>(\n  undefined\n);\n\nexport const useSidebar = () => {\n  const context = useContext(SidebarContext);\n  if (!context) {\n    throw new Error(\"useSidebar must be used within a SidebarProvider\");\n  }\n  return context;\n};\n\nexport const SidebarProvider = ({\n  children,\n  open: openProp,\n  setOpen: setOpenProp,\n  animate = true,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  const [openState, setOpenState] = useState(false);\n\n  const open = openProp !== undefined ? openProp : openState;\n  const setOpen = setOpenProp !== undefined ? setOpenProp : setOpenState;\n\n  return (\n    <SidebarContext.Provider value={{ open, setOpen, animate }}>\n      {children}\n    </SidebarContext.Provider>\n  );\n};\n\nexport const Sidebar = ({\n  children,\n  open,\n  setOpen,\n  animate,\n}: {\n  children: React.ReactNode;\n  open?: boolean;\n  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;\n  animate?: boolean;\n}) => {\n  return (\n    <SidebarProvider open={open} setOpen={setOpen} animate={animate}>\n      {children}\n    </SidebarProvider>\n  );\n};\n\nexport const SidebarBody = (props: React.ComponentProps<typeof motion.div>) => {\n  return (\n    <>\n      <DesktopSidebar {...props} />\n      <MobileSidebar {...(props as React.ComponentProps<\"div\">)} />\n    </>\n  );\n};\n\nexport const DesktopSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof motion.div>) => {\n  const { open, setOpen, animate } = useSidebar();\n  return (\n    <motion.div\n      className={cn(\n        \"h-full px-4 py-4 hidden md:flex md:flex-col bg-neutral-100 dark:bg-neutral-800 w-[300px] flex-shrink-0\",\n        className\n      )}\n      animate={{\n        width: animate ? (open ? \"300px\" : \"60px\") : \"300px\",\n      }}\n      onMouseEnter={() => setOpen(true)}\n      onMouseLeave={() => setOpen(false)}\n      {...props}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\nexport const MobileSidebar = ({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<\"div\">) => {\n  const { open, setOpen } = useSidebar();\n  return (\n    <>\n      <div\n        className={cn(\n          \"h-10 px-4 py-4 flex flex-row md:hidden items-center justify-between bg-neutral-100 dark:bg-neutral-800 w-full\"\n        )}\n        {...props}\n      >\n        <div className=\"flex justify-end z-20 w-full\">\n          <Menu\n            className=\"text-neutral-800 dark:text-neutral-200 cursor-pointer\"\n            onClick={() => setOpen(!open)}\n          />\n        </div>\n        <AnimatePresence>\n          {open && (\n            <>\n              {/* Backdrop overlay */}\n              <motion.div\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 0.2 }}\n                className=\"fixed inset-0 bg-black/50 z-[90]\"\n                onClick={() => setOpen(false)}\n              />\n\n              {/* Sidebar panel */}\n              <motion.div\n                initial={{ x: \"-100%\", opacity: 0 }}\n                animate={{ x: 0, opacity: 1 }}\n                exit={{ x: \"-100%\", opacity: 0 }}\n                transition={{\n                  duration: 0.3,\n                  ease: \"easeInOut\",\n                }}\n                className={cn(\n                  \"fixed h-full w-80 max-w-[85vw] left-0 top-0 bg-white dark:bg-neutral-900 p-6 z-[100] flex flex-col justify-between shadow-2xl\",\n                  className\n                )}\n              >\n                <div className=\"flex flex-col h-full\">\n                  {/* Header with close button */}\n                  <div className=\"flex items-center justify-between mb-6\">\n                    <h2 className=\"text-lg font-semibold text-neutral-800 dark:text-neutral-200\">\n                      Menu\n                    </h2>\n                    <div\n                      className=\"p-2 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 text-neutral-800 dark:text-neutral-200 cursor-pointer transition-colors\"\n                      onClick={() => setOpen(false)}\n                    >\n                      <X className=\"h-5 w-5\" />\n                    </div>\n                  </div>\n\n                  {/* Content */}\n                  <div className=\"flex-1 overflow-y-auto\">\n                    {children}\n                  </div>\n                </div>\n              </motion.div>\n            </>\n          )}\n        </AnimatePresence>\n      </div>\n    </>\n  );\n};\n\nexport const SidebarLink = ({\n  link,\n  className,\n  ...props\n}: {\n  link: Links;\n  className?: string;\n  props?: LinkProps;\n}) => {\n  const { open, animate, setOpen } = useSidebar();\n\n  const handleClick = () => {\n    // Đóng sidebar trên mobile khi click vào link\n    if (window.innerWidth < 768) {\n      setOpen(false);\n    }\n  };\n\n  return (\n    <Link\n      href={link.href}\n      className={cn(\n        \"flex items-center justify-start gap-3 group/sidebar py-3 px-3 rounded-lg hover:bg-neutral-100 dark:hover:bg-neutral-800 transition-colors\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    >\n      <div className=\"flex-shrink-0\">\n        {link.icon}\n      </div>\n      <motion.span\n        animate={{\n          display: animate ? (open ? \"inline-block\" : \"none\") : \"inline-block\",\n          opacity: animate ? (open ? 1 : 0) : 1,\n        }}\n        className=\"text-neutral-700 dark:text-neutral-200 text-sm group-hover/sidebar:translate-x-1 transition duration-150 whitespace-pre inline-block !p-0 !m-0 font-medium\"\n      >\n        {link.label}\n      </motion.span>\n    </Link>\n  );\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AANA;;;;;;;AAqBA,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EACjC;AAGK,MAAM,aAAa;IACxB,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,CAAC,SAAS;QACZ,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAEO,MAAM,kBAAkB,CAAC,EAC9B,QAAQ,EACR,MAAM,QAAQ,EACd,SAAS,WAAW,EACpB,UAAU,IAAI,EAMf;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,OAAO,aAAa,YAAY,WAAW;IACjD,MAAM,UAAU,gBAAgB,YAAY,cAAc;IAE1D,qBACE,8OAAC,eAAe,QAAQ;QAAC,OAAO;YAAE;YAAM;YAAS;QAAQ;kBACtD;;;;;;AAGP;AAEO,MAAM,UAAU,CAAC,EACtB,QAAQ,EACR,IAAI,EACJ,OAAO,EACP,OAAO,EAMR;IACC,qBACE,8OAAC;QAAgB,MAAM;QAAM,SAAS;QAAS,SAAS;kBACrD;;;;;;AAGP;AAEO,MAAM,cAAc,CAAC;IAC1B,qBACE;;0BACE,8OAAC;gBAAgB,GAAG,KAAK;;;;;;0BACzB,8OAAC;gBAAe,GAAI,KAAK;;;;;;;;AAG/B;AAEO,MAAM,iBAAiB,CAAC,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OACqC;IACxC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IACnC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAEF,SAAS;YACP,OAAO,UAAW,OAAO,UAAU,SAAU;QAC/C;QACA,cAAc,IAAM,QAAQ;QAC5B,cAAc,IAAM,QAAQ;QAC3B,GAAG,KAAK;kBAER;;;;;;AAGP;AAEO,MAAM,gBAAgB,CAAC,EAC5B,SAAS,EACT,QAAQ,EACR,GAAG,OACyB;IAC5B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;IAC1B,qBACE;kBACE,cAAA,8OAAC;YACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;YAED,GAAG,KAAK;;8BAET,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;wBACH,WAAU;wBACV,SAAS,IAAM,QAAQ,CAAC;;;;;;;;;;;8BAG5B,8OAAC,yLAAA,CAAA,kBAAe;8BACb,sBACC;;0CAEE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;gCAAE;gCACtB,SAAS;oCAAE,SAAS;gCAAE;gCACtB,MAAM;oCAAE,SAAS;gCAAE;gCACnB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;gCACV,SAAS,IAAM,QAAQ;;;;;;0CAIzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,GAAG;oCAAS,SAAS;gCAAE;gCAClC,SAAS;oCAAE,GAAG;oCAAG,SAAS;gCAAE;gCAC5B,MAAM;oCAAE,GAAG;oCAAS,SAAS;gCAAE;gCAC/B,YAAY;oCACV,UAAU;oCACV,MAAM;gCACR;gCACA,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iIACA;0CAGF,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAA+D;;;;;;8DAG7E,8OAAC;oDACC,WAAU;oDACV,SAAS,IAAM,QAAQ;8DAEvB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAKjB,8OAAC;4CAAI,WAAU;sDACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrB;AAEO,MAAM,cAAc,CAAC,EAC1B,IAAI,EACJ,SAAS,EACT,GAAG,OAKJ;IACC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;IAEnC,MAAM,cAAc;QAClB,8CAA8C;QAC9C,IAAI,OAAO,UAAU,GAAG,KAAK;YAC3B,QAAQ;QACV;IACF;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAM,KAAK,IAAI;QACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6IACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAI,WAAU;0BACZ,KAAK,IAAI;;;;;;0BAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBACP,SAAS,UAAW,OAAO,iBAAiB,SAAU;oBACtD,SAAS,UAAW,OAAO,IAAI,IAAK;gBACtC;gBACA,WAAU;0BAET,KAAK,KAAK;;;;;;;;;;;;AAInB", "debugId": null}}, {"offset": {"line": 300, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/SidebarMenu.tsx"], "sourcesContent": ["import React from \"react\";\nimport { SidebarBody, SidebarLink } from \"@/components/ui/sidebar\";\nimport { Home, List, Calendar, BarChart2, Folder, Tag } from \"lucide-react\";\n\nconst links = [\n  {\n    label: \"Trang chủ\",\n    href: \"/\",\n    icon: <Home className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Công việc\",\n    href: \"/tasks\",\n    icon: <List className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Thống kê\",\n    href: \"/statistics\",\n    icon: <BarChart2 className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Lịch\",\n    href: \"/calendar\",\n    icon: <Calendar className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Dự án\",\n    href: \"/projects\",\n    icon: <Folder className=\"h-5 w-5\" />,\n  },\n  {\n    label: \"Danh mục\",\n    href: \"/categories\",\n    icon: <Tag className=\"h-5 w-5\" />,\n  },\n];\n\nexport default function SidebarMenu() {\n  return (\n    <SidebarBody>\n      <div className=\"flex flex-col gap-2\">\n        {links.map((link) => (\n          <SidebarLink key={link.href} link={link} />\n        ))}\n      </div>\n    </SidebarBody>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;AAEA,MAAM,QAAQ;IACZ;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,mMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,kMAAA,CAAA,OAAI;YAAC,WAAU;;;;;;IACxB;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,gOAAA,CAAA,YAAS;YAAC,WAAU;;;;;;IAC7B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,0MAAA,CAAA,WAAQ;YAAC,WAAU;;;;;;IAC5B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,sMAAA,CAAA,SAAM;YAAC,WAAU;;;;;;IAC1B;IACA;QACE,OAAO;QACP,MAAM;QACN,oBAAM,8OAAC,gMAAA,CAAA,MAAG;YAAC,WAAU;;;;;;IACvB;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,cAAW;kBACV,cAAA,8OAAC;YAAI,WAAU;sBACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC,mIAAA,CAAA,cAAW;oBAAiB,MAAM;mBAAjB,KAAK,IAAI;;;;;;;;;;;;;;;AAKrC", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  },\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  },\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/preference-service.ts"], "sourcesContent": ["import { UserPreferences, Preference } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Gi<PERSON> trị mặc định cho tùy chọn người dùng\r\nconst DEFAULT_PREFERENCES: UserPreferences = {\r\n  theme: 'system',\r\n  language: 'vi',\r\n  startOfWeek: 'monday',\r\n  showCompletedTasks: true,\r\n  notifications: true,\r\n  soundEnabled: true,\r\n};\r\n\r\n// Cache cho preferences\r\nlet preferencesCache: UserPreferences | null = null;\r\n\r\n// Helper function to convert Preference to UserPreferences\r\nconst mapPreferenceToUserPreferences = (preference: Preference): UserPreferences => {\r\n  return {\r\n    theme: preference.theme,\r\n    language: (preference.language === 'en' ? 'en' : 'vi') as 'vi' | 'en',\r\n    startOfWeek: preference.startOfWeek === 1 ? 'monday' : 'sunday',\r\n    showCompletedTasks: true, // Default value\r\n    notifications: preference.notifications,\r\n    soundEnabled: true, // Default value\r\n  };\r\n};\r\n\r\n// Helper function to convert UserPreferences to Preference updates\r\nconst mapUserPreferencesToPreference = (userPrefs: Partial<UserPreferences>): Partial<Omit<Preference, 'id' | 'userId'>> => {\r\n  const updates: any = {};\r\n\r\n  if (userPrefs.theme) updates.theme = userPrefs.theme;\r\n  if (userPrefs.language) updates.language = userPrefs.language;\r\n  if (userPrefs.startOfWeek) updates.startOfWeek = userPrefs.startOfWeek === 'monday' ? 1 : 0;\r\n  if (userPrefs.notifications !== undefined) updates.notifications = userPrefs.notifications;\r\n\r\n  return updates;\r\n};\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const PreferenceService = {\r\n  getPreferences: async (): Promise<UserPreferences> => {\r\n    const now = Date.now();\r\n    \r\n    // Nếu có cache và chưa hết hạn, trả về cache\r\n    if (preferencesCache && now - lastFetchTime < CACHE_DURATION) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    try {\r\n      // Gọi API để lấy preferences\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = now;\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi lấy tùy chọn người dùng:', error);\r\n      \r\n      // Nếu có lỗi và đã có cache, trả về cache\r\n      if (preferencesCache) {\r\n        return preferencesCache;\r\n      }\r\n      \r\n      // Nếu không có cache, trả về giá trị mặc định\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Phương thức đồng bộ để lấy preferences từ cache (cho các component không thể đợi async)\r\n  getPreferencesSync: (): UserPreferences => {\r\n    if (preferencesCache) {\r\n      return preferencesCache;\r\n    }\r\n    \r\n    // Nếu chưa có cache, trả về giá trị mặc định\r\n    return DEFAULT_PREFERENCES;\r\n  },\r\n  \r\n  updatePreferences: async (updates: Partial<UserPreferences>): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert UserPreferences updates to Preference format\r\n      const preferenceUpdates = mapUserPreferencesToPreference(updates);\r\n\r\n      // Gọi API để cập nhật preferences\r\n      const updatedPreference = await ApiService.preferences.update(preferenceUpdates);\r\n      const updatedUserPreferences = mapPreferenceToUserPreferences(updatedPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = updatedUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return updatedUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi cập nhật tùy chọn người dùng:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  resetPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      // Convert default preferences to Preference format\r\n      const defaultPreferenceUpdates = mapUserPreferencesToPreference(DEFAULT_PREFERENCES);\r\n\r\n      // Gọi API để đặt lại preferences về mặc định\r\n      const resetPreference = await ApiService.preferences.update(defaultPreferenceUpdates);\r\n      const resetUserPreferences = mapPreferenceToUserPreferences(resetPreference);\r\n\r\n      // Cập nhật cache\r\n      preferencesCache = resetUserPreferences;\r\n      lastFetchTime = Date.now();\r\n\r\n      return resetUserPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi đặt lại tùy chọn người dùng:', error);\r\n\r\n      // Nếu có lỗi, đặt cache về mặc định\r\n      preferencesCache = DEFAULT_PREFERENCES;\r\n      lastFetchTime = Date.now();\r\n\r\n      return DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n  \r\n  // Xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    preferencesCache = null;\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshPreferences: async (): Promise<UserPreferences> => {\r\n    try {\r\n      const preference = await ApiService.preferences.get();\r\n      const userPreferences = mapPreferenceToUserPreferences(preference);\r\n      preferencesCache = userPreferences;\r\n      lastFetchTime = Date.now();\r\n      return userPreferences;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới tùy chọn người dùng:', error);\r\n      return preferencesCache || DEFAULT_PREFERENCES;\r\n    }\r\n  },\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,2CAA2C;AAC3C,MAAM,sBAAuC;IAC3C,OAAO;IACP,UAAU;IACV,aAAa;IACb,oBAAoB;IACpB,eAAe;IACf,cAAc;AAChB;AAEA,wBAAwB;AACxB,IAAI,mBAA2C;AAE/C,2DAA2D;AAC3D,MAAM,iCAAiC,CAAC;IACtC,OAAO;QACL,OAAO,WAAW,KAAK;QACvB,UAAW,WAAW,QAAQ,KAAK,OAAO,OAAO;QACjD,aAAa,WAAW,WAAW,KAAK,IAAI,WAAW;QACvD,oBAAoB;QACpB,eAAe,WAAW,aAAa;QACvC,cAAc;IAChB;AACF;AAEA,mEAAmE;AACnE,MAAM,iCAAiC,CAAC;IACtC,MAAM,UAAe,CAAC;IAEtB,IAAI,UAAU,KAAK,EAAE,QAAQ,KAAK,GAAG,UAAU,KAAK;IACpD,IAAI,UAAU,QAAQ,EAAE,QAAQ,QAAQ,GAAG,UAAU,QAAQ;IAC7D,IAAI,UAAU,WAAW,EAAE,QAAQ,WAAW,GAAG,UAAU,WAAW,KAAK,WAAW,IAAI;IAC1F,IAAI,UAAU,aAAa,KAAK,WAAW,QAAQ,aAAa,GAAG,UAAU,aAAa;IAE1F,OAAO;AACT;AACA,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,oBAAoB;IAC/B,gBAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QAEpB,6CAA6C;QAC7C,IAAI,oBAAoB,MAAM,gBAAgB,gBAAgB;YAC5D,OAAO;QACT;QAEA,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB;YAChB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAElD,0CAA0C;YAC1C,IAAI,kBAAkB;gBACpB,OAAO;YACT;YAEA,8CAA8C;YAC9C,OAAO;QACT;IACF;IAEA,0FAA0F;IAC1F,oBAAoB;QAClB,IAAI,kBAAkB;YACpB,OAAO;QACT;QAEA,6CAA6C;QAC7C,OAAO;IACT;IAEA,mBAAmB,OAAO;QACxB,IAAI;YACF,uDAAuD;YACvD,MAAM,oBAAoB,+BAA+B;YAEzD,kCAAkC;YAClC,MAAM,oBAAoB,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC9D,MAAM,yBAAyB,+BAA+B;YAE9D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,kBAAkB;QAChB,IAAI;YACF,mDAAmD;YACnD,MAAM,2BAA2B,+BAA+B;YAEhE,6CAA6C;YAC7C,MAAM,kBAAkB,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,MAAM,CAAC;YAC5D,MAAM,uBAAuB,+BAA+B;YAE5D,iBAAiB;YACjB,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YAEtD,oCAAoC;YACpC,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YAExB,OAAO;QACT;IACF;IAEA,0BAA0B;IAC1B,YAAY;QACV,mBAAmB;QACnB,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,oBAAoB;QAClB,IAAI;YACF,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,WAAW,CAAC,GAAG;YACnD,MAAM,kBAAkB,+BAA+B;YACvD,mBAAmB;YACnB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO,oBAAoB;QAC7B;IACF;AACF", "debugId": null}}, {"offset": {"line": 600, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/clear-data-button.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { PreferenceService } from \"@/lib/services/preference-service\";\nimport { Trash2 } from \"lucide-react\";\nimport { useSidebar } from \"@/components/ui/sidebar\";\nimport { motion } from \"framer-motion\";\n\nexport function ClearDataButton() {\n  const { open, animate } = useSidebar();\n  \n  const handleClearData = () => {\n    if (window.confirm('Bạn có chắc chắn muốn xóa tất cả dữ liệu? Hành động này không thể hoàn tác.')) {\n      // Clear localStorage\n      localStorage.clear();\n      // Clear preferences cache\n      PreferenceService.clearCache();\n      // Reload page\n      window.location.reload();\n    }\n  };\n\n  return (\n    <Button\n      variant=\"ghost\"\n      size=\"icon\"\n      onClick={handleClearData}\n      className=\"w-full flex items-center gap-2 justify-start px-2 text-destructive\"\n    >\n      <Trash2 className=\"h-5 w-5\" />\n      <motion.span\n        initial={{ opacity: 0, width: 0 }}\n        animate={{\n          opacity: animate ? (open ? 1 : 0) : 1,\n          width: animate ? (open ? \"auto\" : 0) : \"auto\",\n          marginLeft: animate ? (open ? \"0.5rem\" : 0) : \"0.5rem\",\n          display: \"inline-block\"\n        }}\n        transition={{ duration: 0.2 }}\n        className=\"whitespace-pre overflow-hidden\"\n      >\n        Xóa dữ liệu\n      </motion.span>\n    </Button>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAEnC,MAAM,kBAAkB;QACtB,IAAI,OAAO,OAAO,CAAC,gFAAgF;YACjG,qBAAqB;YACrB,aAAa,KAAK;YAClB,0BAA0B;YAC1B,+IAAA,CAAA,oBAAiB,CAAC,UAAU;YAC5B,cAAc;YACd,OAAO,QAAQ,CAAC,MAAM;QACxB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QACL,SAAQ;QACR,MAAK;QACL,SAAS;QACT,WAAU;;0BAEV,8OAAC,0MAAA,CAAA,SAAM;gBAAC,WAAU;;;;;;0BAClB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;oBAAG,OAAO;gBAAE;gBAChC,SAAS;oBACP,SAAS,UAAW,OAAO,IAAI,IAAK;oBACpC,OAAO,UAAW,OAAO,SAAS,IAAK;oBACvC,YAAY,UAAW,OAAO,WAAW,IAAK;oBAC9C,SAAS;gBACX;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 675, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, ...props }, ref) => {\n    return (\n      <input\n        type={type}\n        className={cn(\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nInput.displayName = \"Input\"\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qXACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 704, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst TooltipProvider = TooltipPrimitive.Provider\n\nconst Tooltip = TooltipPrimitive.Root\n\nconst TooltipTrigger = TooltipPrimitive.Trigger\n\nconst TooltipContent = React.forwardRef<\n  React.ElementRef<typeof TooltipPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>\n>(({ className, sideOffset = 4, ...props }, ref) => (\n  <TooltipPrimitive.Content\n    ref={ref}\n    sideOffset={sideOffset}\n    className={cn(\n      \"z-50 overflow-hidden rounded-md border bg-popover px-3 py-1.5 text-sm text-popover-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n      className\n    )}\n    {...props}\n  />\n))\nTooltipContent.displayName = TooltipPrimitive.Content.displayName\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,kBAAkB,mKAAA,CAAA,WAAyB;AAEjD,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,mKAAA,CAAA,UAAwB;QACvB,KAAK;QACL,YAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sYACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 740, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 804, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/footer-section.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { useState } from \"react\"\nimport { <PERSON><PERSON> } from \"@/components/ui/button\"\nimport { Input } from \"@/components/ui/input\"\nimport { Label } from \"@/components/ui/label\"\nimport {\n  <PERSON><PERSON><PERSON>,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from \"@/components/ui/tooltip\"\nimport { Alert, AlertDescription } from \"@/components/ui/alert\"\nimport { Github, Send, CheckCircle, AlertCircle } from \"lucide-react\"\nimport { ApiService } from \"@/lib/services/api-service\"\n\nfunction Footerdemo() {\n  const [email, setEmail] = useState('')\n  const [isLoading, setIsLoading] = useState(false)\n  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null)\n\n  const handleEmailSubscription = async (e: React.FormEvent) => {\n    e.preventDefault()\n\n    if (!email) {\n      setMessage({ type: 'error', text: '<PERSON><PERSON> lòng nhập địa chỉ email' })\n      return\n    }\n\n    // <PERSON><PERSON><PERSON> tra định dạng email\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n    if (!emailRegex.test(email)) {\n      setMessage({ type: 'error', text: 'Địa chỉ email không hợp lệ' })\n      return\n    }\n\n    setIsLoading(true)\n    setMessage(null)\n\n    try {\n      // Sử dụng API public - không cần đăng nhập\n      await ApiService.notifications.subscribeEmailPublic({\n        email: email,\n        name: '', // Có thể để trống hoặc thêm field name nếu muốn\n        taskReminders: true,\n        dailySummary: false,\n        weeklyReport: false,\n        reminderHours: 24,\n      })\n\n      setMessage({\n        type: 'success',\n        text: 'Đăng ký thành công! Bạn sẽ nhận được thông báo qua email. Kiểm tra hộp thư để xác nhận.'\n      })\n      setEmail('')\n    } catch (error: any) {\n      console.error('Lỗi đăng ký email:', error)\n      setMessage({\n        type: 'error',\n        text: error.message || 'Đăng ký thất bại. Vui lòng thử lại.'\n      })\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  return (\n    <footer className=\"relative border-t bg-background/80 backdrop-blur-sm text-foreground transition-colors duration-300\">\n      <div className=\"container mx-auto px-4 py-12 md:px-6 lg:px-8\">\n        <div className=\"grid gap-12 md:grid-cols-2 lg:grid-cols-4\">\n          <div className=\"relative\">\n            <h2 className=\"mb-4 text-3xl font-bold tracking-tight\">QLTime</h2>\n            <p className=\"mb-6 text-muted-foreground\">\n              Giải pháp quản lý thời gian thông minh cho cuộc sống hiện đại.\n            </p>\n            <form onSubmit={handleEmailSubscription} className=\"relative\">\n              <Input\n                type=\"email\"\n                placeholder=\"Đăng ký nhận thông báo\"\n                className=\"pr-12 backdrop-blur-sm\"\n                value={email}\n                onChange={(e) => setEmail(e.target.value)}\n                disabled={isLoading}\n              />\n              <Button\n                type=\"submit\"\n                size=\"icon\"\n                className=\"absolute right-1 top-1 h-8 w-8 rounded-full bg-primary text-primary-foreground transition-transform hover:scale-105 disabled:opacity-50\"\n                disabled={isLoading}\n              >\n                {isLoading ? (\n                  <div className=\"h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent\" />\n                ) : (\n                  <Send className=\"h-4 w-4\" />\n                )}\n                <span className=\"sr-only\">Đăng ký</span>\n              </Button>\n            </form>\n\n            {message && (\n              <Alert className={`mt-3 ${message.type === 'success' ? 'border-green-500 bg-green-50' : 'border-red-500 bg-red-50'}`}>\n                <div className=\"flex items-center gap-2\">\n                  {message.type === 'success' ? (\n                    <CheckCircle className=\"h-4 w-4 text-green-600\" />\n                  ) : (\n                    <AlertCircle className=\"h-4 w-4 text-red-600\" />\n                  )}\n                  <AlertDescription className={message.type === 'success' ? 'text-green-800' : 'text-red-800'}>\n                    {message.text}\n                  </AlertDescription>\n                </div>\n              </Alert>\n            )}\n            <div className=\"absolute -right-4 top-0 h-24 w-24 rounded-full bg-primary/10 blur-2xl\" />\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-lg font-semibold\">Tính năng</h3>\n            <nav className=\"space-y-2 text-sm\">\n              <a href=\"/tasks\" className=\"block transition-colors hover:text-primary\">\n                Quản lý công việc\n              </a>\n              <a href=\"/calendar\" className=\"block transition-colors hover:text-primary\">\n                Lịch & Timeblocks\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Thống kê\n              </a>\n              <a href=\"#\" className=\"block transition-colors hover:text-primary\">\n                Tùy chỉnh\n              </a>\n            </nav>\n          </div>\n          <div>\n            <h3 className=\"mb-4 text-lg font-semibold\">Liên hệ</h3>\n            <address className=\"space-y-2 text-sm not-italic\">\n              <p>Email: <EMAIL></p>\n              <p>SĐT: **********</p>\n            </address>\n          </div>\n          <div className=\"relative\">\n            <h3 className=\"mb-4 text-lg font-semibold\">Theo dõi</h3>\n            <div className=\"mb-6 flex space-x-4\">\n              <TooltipProvider>\n                <Tooltip>\n                  <TooltipTrigger asChild>\n                    <Button \n                      variant=\"outline\" \n                      size=\"icon\" \n                      className=\"rounded-full\"\n                      asChild\n                    >\n                      <a href=\"https://github.com/hungtvu113/WebsiteTimE\" target=\"_blank\" rel=\"noopener noreferrer\">\n                        <Github className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Github</span>\n                      </a>\n                    </Button>\n                  </TooltipTrigger>\n                  <TooltipContent>\n                    <p>Xem mã nguồn trên Github</p>\n                  </TooltipContent>\n                </Tooltip>\n              </TooltipProvider>\n            </div>\n          </div>\n        </div>\n        <div className=\"mt-12 flex flex-col items-center justify-between gap-4 border-t pt-8 text-center md:flex-row\">\n          <p className=\"text-sm text-muted-foreground\">\n            © 2024 QLTime. Bản quyền thuộc về nhóm phát triển.\n          </p>\n          <nav className=\"flex gap-4 text-sm\">\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Chính sách bảo mật\n            </a>\n            <a href=\"#\" className=\"transition-colors hover:text-primary\">\n              Điều khoản sử dụng\n            </a>\n          </nav>\n        </div>\n      </div>\n    </footer>\n  )\n}\n\nexport { Footerdemo }"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AAEA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;AAfA;;;;;;;;;AAiBA,SAAS;IACP,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsD;IAE3F,MAAM,0BAA0B,OAAO;QACrC,EAAE,cAAc;QAEhB,IAAI,CAAC,OAAO;YACV,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA8B;YAChE;QACF;QAEA,2BAA2B;QAC3B,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,WAAW;gBAAE,MAAM;gBAAS,MAAM;YAA6B;YAC/D;QACF;QAEA,aAAa;QACb,WAAW;QAEX,IAAI;YACF,2CAA2C;YAC3C,MAAM,wIAAA,CAAA,aAAU,CAAC,aAAa,CAAC,oBAAoB,CAAC;gBAClD,OAAO;gBACP,MAAM;gBACN,eAAe;gBACf,cAAc;gBACd,cAAc;gBACd,eAAe;YACjB;YAEA,WAAW;gBACT,MAAM;gBACN,MAAM;YACR;YACA,SAAS;QACX,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sBAAsB;YACpC,WAAW;gBACT,MAAM;gBACN,MAAM,MAAM,OAAO,IAAI;YACzB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyC;;;;;;8CACvD,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAG1C,8OAAC;oCAAK,UAAU;oCAAyB,WAAU;;sDACjD,8OAAC,iIAAA,CAAA,QAAK;4CACJ,MAAK;4CACL,aAAY;4CACZ,WAAU;4CACV,OAAO;4CACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;4CACxC,UAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,MAAK;4CACL,WAAU;4CACV,UAAU;;gDAET,0BACC,8OAAC;oDAAI,WAAU;;;;;yEAEf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAElB,8OAAC;oDAAK,WAAU;8DAAU;;;;;;;;;;;;;;;;;;gCAI7B,yBACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,YAAY,iCAAiC,4BAA4B;8CAClH,cAAA,8OAAC;wCAAI,WAAU;;4CACZ,QAAQ,IAAI,KAAK,0BAChB,8OAAC,2NAAA,CAAA,cAAW;gDAAC,WAAU;;;;;qEAEvB,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DAEzB,8OAAC,iIAAA,CAAA,mBAAgB;gDAAC,WAAW,QAAQ,IAAI,KAAK,YAAY,mBAAmB;0DAC1E,QAAQ,IAAI;;;;;;;;;;;;;;;;;8CAKrB,8OAAC;oCAAI,WAAU;;;;;;;;;;;;sCAEjB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAE,MAAK;4CAAS,WAAU;sDAA6C;;;;;;sDAGxE,8OAAC;4CAAE,MAAK;4CAAY,WAAU;sDAA6C;;;;;;sDAG3E,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;sDAGnE,8OAAC;4CAAE,MAAK;4CAAI,WAAU;sDAA6C;;;;;;;;;;;;;;;;;;sCAKvE,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;sDAAE;;;;;;sDACH,8OAAC;sDAAE;;;;;;;;;;;;;;;;;;sCAGP,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAC3C,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,mIAAA,CAAA,kBAAe;kDACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;8DACN,8OAAC,mIAAA,CAAA,iBAAc;oDAAC,OAAO;8DACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAU;wDACV,OAAO;kEAEP,cAAA,8OAAC;4DAAE,MAAK;4DAA4C,QAAO;4DAAS,KAAI;;8EACtE,8OAAC,sMAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;8EAClB,8OAAC;oEAAK,WAAU;8EAAU;;;;;;;;;;;;;;;;;;;;;;8DAIhC,8OAAC,mIAAA,CAAA,iBAAc;8DACb,cAAA,8OAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOf,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAAgC;;;;;;sCAG7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;8CAG7D,8OAAC;oCAAE,MAAK;oCAAI,WAAU;8CAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzE", "debugId": null}}, {"offset": {"line": 1271, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\r\nimport { Check, ChevronRight, Circle } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst DropdownMenu = DropdownMenuPrimitive.Root\r\n\r\nconst DropdownMenuTrigger = DropdownMenuPrimitive.Trigger\r\n\r\nconst DropdownMenuGroup = DropdownMenuPrimitive.Group\r\n\r\nconst DropdownMenuPortal = DropdownMenuPrimitive.Portal\r\n\r\nconst DropdownMenuSub = DropdownMenuPrimitive.Sub\r\n\r\nconst DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup\r\n\r\nconst DropdownMenuSubTrigger = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubTrigger\r\n    ref={ref}\r\n    className={cn(\r\n      \"flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    {children}\r\n    <ChevronRight className=\"ml-auto h-4 w-4\" />\r\n  </DropdownMenuPrimitive.SubTrigger>\r\n))\r\nDropdownMenuSubTrigger.displayName =\r\n  DropdownMenuPrimitive.SubTrigger.displayName\r\n\r\nconst DropdownMenuSubContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.SubContent\r\n    ref={ref}\r\n    className={cn(\r\n      \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSubContent.displayName =\r\n  DropdownMenuPrimitive.SubContent.displayName\r\n\r\nconst DropdownMenuContent = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Content>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>\r\n>(({ className, sideOffset = 4, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Portal>\r\n    <DropdownMenuPrimitive.Content\r\n      ref={ref}\r\n      sideOffset={sideOffset}\r\n      className={cn(\r\n        \"z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\r\n        className\r\n      )}\r\n      {...props}\r\n    />\r\n  </DropdownMenuPrimitive.Portal>\r\n))\r\nDropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName\r\n\r\nconst DropdownMenuItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Item>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Item\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName\r\n\r\nconst DropdownMenuCheckboxItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>\r\n>(({ className, children, checked, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.CheckboxItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    checked={checked}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Check className=\"h-4 w-4\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.CheckboxItem>\r\n))\r\nDropdownMenuCheckboxItem.displayName =\r\n  DropdownMenuPrimitive.CheckboxItem.displayName\r\n\r\nconst DropdownMenuRadioItem = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>\r\n>(({ className, children, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.RadioItem\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\r\n      <DropdownMenuPrimitive.ItemIndicator>\r\n        <Circle className=\"h-2 w-2 fill-current\" />\r\n      </DropdownMenuPrimitive.ItemIndicator>\r\n    </span>\r\n    {children}\r\n  </DropdownMenuPrimitive.RadioItem>\r\n))\r\nDropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName\r\n\r\nconst DropdownMenuLabel = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Label>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {\r\n    inset?: boolean\r\n  }\r\n>(({ className, inset, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Label\r\n    ref={ref}\r\n    className={cn(\r\n      \"px-2 py-1.5 text-sm font-semibold\",\r\n      inset && \"pl-8\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName\r\n\r\nconst DropdownMenuSeparator = React.forwardRef<\r\n  React.ElementRef<typeof DropdownMenuPrimitive.Separator>,\r\n  React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>\r\n>(({ className, ...props }, ref) => (\r\n  <DropdownMenuPrimitive.Separator\r\n    ref={ref}\r\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\r\n    {...props}\r\n  />\r\n))\r\nDropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName\r\n\r\nconst DropdownMenuShortcut = ({\r\n  className,\r\n  ...props\r\n}: React.HTMLAttributes<HTMLSpanElement>) => {\r\n  return (\r\n    <span\r\n      className={cn(\"ml-auto text-xs tracking-widest opacity-60\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nDropdownMenuShortcut.displayName = \"DropdownMenuShortcut\"\r\n\r\nexport {\r\n  DropdownMenu,\r\n  DropdownMenuTrigger,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuCheckboxItem,\r\n  DropdownMenuRadioItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuShortcut,\r\n  DropdownMenuGroup,\r\n  DropdownMenuPortal,\r\n  DropdownMenuSub,\r\n  DropdownMenuSubContent,\r\n  DropdownMenuSubTrigger,\r\n  DropdownMenuRadioGroup,\r\n} "], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,eAAe,4KAAA,CAAA,OAA0B;AAE/C,MAAM,sBAAsB,4KAAA,CAAA,UAA6B;AAEzD,MAAM,oBAAoB,4KAAA,CAAA,QAA2B;AAErD,MAAM,qBAAqB,4KAAA,CAAA,SAA4B;AAEvD,MAAM,kBAAkB,4KAAA,CAAA,MAAyB;AAEjD,MAAM,yBAAyB,4KAAA,CAAA,aAAgC;AAE/D,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAK5C,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBAC3C,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wIACA,SAAS,QACT;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,sNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;;;;;;;AAG5B,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;QAED,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,4KAAA,CAAA,aAAgC,CAAC,WAAW;AAE9C,MAAM,oCAAsB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGzC,CAAC,EAAE,SAAS,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC1C,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,KAAK;YACL,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ybACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,oBAAoB,WAAW,GAAG,4KAAA,CAAA,UAA6B,CAAC,WAAW;AAE3E,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,OAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mOACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,4KAAA,CAAA,OAA0B,CAAC,WAAW;AAErE,MAAM,yCAA2B,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBAC7C,8OAAC,4KAAA,CAAA,eAAkC;QACjC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGpB;;;;;;;AAGL,yBAAyB,WAAW,GAClC,4KAAA,CAAA,eAAkC,CAAC,WAAW;AAEhD,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wOACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,sMAAA,CAAA,SAAM;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGrB;;;;;;;AAGL,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,4KAAA,CAAA,QAA2B,CAAC,WAAW;AAEvE,MAAM,sCAAwB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG,4KAAA,CAAA,YAA+B,CAAC,WAAW;AAE/E,MAAM,uBAAuB,CAAC,EAC5B,SAAS,EACT,GAAG,OACmC;IACtC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8CAA8C;QAC3D,GAAG,KAAK;;;;;;AAGf;AACA,qBAAqB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1469, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Avatar = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatar.displayName = AvatarPrimitive.Root.displayName\n\nconst AvatarImage = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Image>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Image>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Image\n    ref={ref}\n    className={cn(\"aspect-square h-full w-full\", className)}\n    {...props}\n  />\n))\nAvatarImage.displayName = AvatarPrimitive.Image.displayName\n\nconst AvatarFallback = React.forwardRef<\n  React.ElementRef<typeof AvatarPrimitive.Fallback>,\n  React.ComponentPropsWithoutRef<typeof AvatarPrimitive.Fallback>\n>(({ className, ...props }, ref) => (\n  <AvatarPrimitive.Fallback\n    ref={ref}\n    className={cn(\n      \"flex h-full w-full items-center justify-center rounded-full bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nAvatarFallback.displayName = AvatarPrimitive.Fallback.displayName\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,WAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wEACA;QAED,GAAG,KAAK;;;;;;AAGb,eAAe,WAAW,GAAG,kKAAA,CAAA,WAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 1520, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport * as React from \"react\";\r\nimport { LogOut, User, Setting<PERSON>, BarChart3, UserCircle } from \"lucide-react\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { Button } from \"@/components/ui/button\";\r\nimport { ApiService } from \"@/lib/services/api-service\";\r\nimport {\r\n  Tooltip,\r\n  TooltipContent,\r\n  TooltipProvider,\r\n  TooltipTrigger,\r\n} from \"@/components/ui/tooltip\";\r\nimport {\r\n  DropdownMenu,\r\n  DropdownMenuContent,\r\n  DropdownMenuItem,\r\n  DropdownMenuLabel,\r\n  DropdownMenuSeparator,\r\n  DropdownMenuTrigger,\r\n} from \"@/components/ui/dropdown-menu\";\r\nimport { Avatar, AvatarFallback, AvatarImage } from \"@/components/ui/avatar\";\r\n\r\nexport function Header() {\r\n  const router = useRouter();\r\n  const [user, setUser] = React.useState<any>(null);\r\n\r\n  React.useEffect(() => {\r\n    const fetchUser = async () => {\r\n      try {\r\n        const userData = await ApiService.auth.getCurrentUser();\r\n        setUser(userData);\r\n      } catch (error) {\r\n        console.error('Lỗi lấy thông tin user:', error);\r\n      }\r\n    };\r\n\r\n    fetchUser();\r\n  }, []);\r\n\r\n\r\n\r\n  const handleLogout = () => {\r\n    ApiService.auth.logout();\r\n    router.push('/login');\r\n  };\r\n\r\n  const handleProfile = () => {\r\n    router.push('/profile');\r\n  };\r\n\r\n  const handleSettings = () => {\r\n    router.push('/settings');\r\n  };\r\n\r\n  const handleStatistics = () => {\r\n    router.push('/statistics');\r\n  };\r\n\r\n  // Tạo initials từ tên user\r\n  const getInitials = (name: string) => {\r\n    return name\r\n      .split(' ')\r\n      .map(word => word.charAt(0))\r\n      .join('')\r\n      .toUpperCase()\r\n      .slice(0, 2);\r\n  };\r\n\r\n  return (\r\n    <header className=\"sticky top-0 z-10 backdrop-blur-sm bg-background/80 border-b\">\r\n      <div className=\"container flex h-14 items-center justify-between\">\r\n        {/* Logo cho mobile */}\r\n        <div className=\"flex items-center md:hidden\">\r\n          <div className=\"h-5 w-6 bg-primary rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0 mr-2\" />\r\n          <span className=\"font-medium text-xl\">QLTime</span>\r\n        </div>\r\n\r\n        <div className=\"mr-4 hidden md:flex\">\r\n          {/* Có thể thêm các phần tử khác ở đây nếu cần */}\r\n        </div>\r\n\r\n        <div className=\"flex items-center gap-2\">\r\n\r\n          <DropdownMenu>\r\n            <DropdownMenuTrigger asChild>\r\n              <Button\r\n                variant=\"ghost\"\r\n                className=\"relative h-8 w-8 rounded-full hover:bg-background/80 transition-colors\"\r\n              >\r\n                <Avatar className=\"h-8 w-8\">\r\n                  <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />\r\n                  <AvatarFallback className=\"bg-primary text-primary-foreground\">\r\n                    {user?.name ? getInitials(user.name) : <UserCircle className=\"h-4 w-4\" />}\r\n                  </AvatarFallback>\r\n                </Avatar>\r\n              </Button>\r\n            </DropdownMenuTrigger>\r\n            <DropdownMenuContent\r\n              className=\"w-56 backdrop-blur-sm border border-border/40 bg-background/90 shadow-lg animate-in fade-in-0 zoom-in-95\"\r\n              align=\"end\"\r\n              forceMount\r\n              sideOffset={5}\r\n            >\r\n              <DropdownMenuLabel className=\"font-normal\">\r\n                <div className=\"flex flex-col space-y-1\">\r\n                  <p className=\"text-sm font-medium leading-none\">\r\n                    {user?.name || 'Người dùng'}\r\n                  </p>\r\n                  <p className=\"text-xs leading-none text-muted-foreground\">\r\n                    {user?.email || '<EMAIL>'}\r\n                  </p>\r\n                </div>\r\n              </DropdownMenuLabel>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleProfile}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <User className=\"mr-2 h-4 w-4\" />\r\n                <span>Hồ sơ cá nhân</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleStatistics}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <BarChart3 className=\"mr-2 h-4 w-4\" />\r\n                <span>Thống kê</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuItem\r\n                onClick={handleSettings}\r\n                className=\"cursor-pointer hover:bg-background/60 transition-colors\"\r\n              >\r\n                <Settings className=\"mr-2 h-4 w-4\" />\r\n                <span>Cài đặt</span>\r\n              </DropdownMenuItem>\r\n              <DropdownMenuSeparator />\r\n              <DropdownMenuItem\r\n                onClick={handleLogout}\r\n                className=\"cursor-pointer hover:bg-destructive/10 hover:text-destructive transition-colors\"\r\n              >\r\n                <LogOut className=\"mr-2 h-4 w-4\" />\r\n                <span>Đăng xuất</span>\r\n              </DropdownMenuItem>\r\n            </DropdownMenuContent>\r\n          </DropdownMenu>\r\n        </div>\r\n      </div>\r\n    </header>\r\n  );\r\n} "], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAOA;AAQA;AArBA;;;;;;;;;AAuBO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAc,AAAD,EAAO;IAE5C,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,YAAY;YAChB,IAAI;gBACF,MAAM,WAAW,MAAM,wIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,cAAc;gBACrD,QAAQ;YACV,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2BAA2B;YAC3C;QACF;QAEA;IACF,GAAG,EAAE;IAIL,MAAM,eAAe;QACnB,wIAAA,CAAA,aAAU,CAAC,IAAI,CAAC,MAAM;QACtB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,gBAAgB;QACpB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,mBAAmB;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,2BAA2B;IAC3B,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAK,WAAU;sCAAsB;;;;;;;;;;;;8BAGxC,8OAAC;oBAAI,WAAU;;;;;;8BAIf,8OAAC;oBAAI,WAAU;8BAEb,cAAA,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;8CAEV,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,8OAAC,kIAAA,CAAA,cAAW;gDAAC,KAAK,MAAM;gDAAQ,KAAK,MAAM,QAAQ;;;;;;0DACnD,8OAAC,kIAAA,CAAA,iBAAc;gDAAC,WAAU;0DACvB,MAAM,OAAO,YAAY,KAAK,IAAI,kBAAI,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAKrE,8OAAC,4IAAA,CAAA,sBAAmB;gCAClB,WAAU;gCACV,OAAM;gCACN,UAAU;gCACV,YAAY;;kDAEZ,8OAAC,4IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;8DACV,MAAM,QAAQ;;;;;;8DAEjB,8OAAC;oDAAE,WAAU;8DACV,MAAM,SAAS;;;;;;;;;;;;;;;;;kDAItB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,kNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCACf,SAAS;wCACT,WAAU;;0DAEV,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB", "debugId": null}}, {"offset": {"line": 1840, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/ai-service.ts"], "sourcesContent": ["import { GoogleGenerativeAI } from \"@google/generative-ai\";\r\nimport { Task } from '../types';\r\n\r\n// Khởi tạo Gemini AI\r\nlet genAI: GoogleGenerativeAI | null = null;\r\nlet model: any = null;\r\n\r\n// Lấy API key từ API route\r\nconst getApiKeyFromServer = async (): Promise<string | null> => {\r\n  try {\r\n    const response = await fetch('/api/ai/api-key');\r\n    if (!response.ok) {\r\n      console.error('Failed to get API key from server');\r\n      return null;\r\n    }\r\n    const data = await response.json();\r\n    return data.apiKey || null;\r\n  } catch (error) {\r\n    console.error('Error fetching API key:', error);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Khởi tạo AI service với API key từ server\r\nconst initializeAI = async () => {\r\n  if (model) return true; // Đã khởi tạo rồi\r\n\r\n  const apiKey = await getApiKeyFromServer();\r\n  if (apiKey && !genAI) {\r\n    genAI = new GoogleGenerativeAI(apiKey);\r\n    model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n  }\r\n  return !!model;\r\n};\r\n\r\n// Chat history interface\r\nexport interface ChatMessage {\r\n  role: 'user' | 'assistant';\r\n  content: string;\r\n  timestamp: Date;\r\n}\r\n\r\nexport const AIService = {\r\n  // Phân tích nội dung công việc và đề xuất độ ưu tiên bằng AI\r\n  suggestPriority: async (title: string, description?: string): Promise<'high' | 'medium' | 'low'> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        // Fallback to keyword-based logic if AI not available\r\n        return AIService.fallbackSuggestPriority(title, description);\r\n      }\r\n\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất độ ưu tiên (high/medium/low):\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\n\r\nHãy trả về chỉ một từ: \"high\", \"medium\", hoặc \"low\" dựa trên:\r\n- Tính khẩn cấp của công việc\r\n- Tầm quan trọng\r\n- Deadline ngầm định\r\n- Từ khóa chỉ độ ưu tiên\r\n\r\nChỉ trả về một từ, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().toLowerCase().trim();\r\n\r\n      if (['high', 'medium', 'low'].includes(response)) {\r\n        return response as 'high' | 'medium' | 'low';\r\n      }\r\n\r\n      return 'medium';\r\n    } catch (error) {\r\n      console.error(\"Error suggesting priority with AI:\", error);\r\n      return AIService.fallbackSuggestPriority(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for priority suggestion\r\n  fallbackSuggestPriority: (title: string, description?: string): 'high' | 'medium' | 'low' => {\r\n    if (!title) return 'medium';\r\n\r\n    const lowerTitle = title.toLowerCase();\r\n    const lowerDesc = description?.toLowerCase() || '';\r\n\r\n    const highPriorityKeywords = ['gấp', 'khẩn', 'ngay', 'quan trọng', 'deadline', 'hạn chót'];\r\n    const lowPriorityKeywords = ['nhẹ nhàng', 'khi rảnh', 'không gấp', 'sau này', 'phụ'];\r\n\r\n    for (const keyword of highPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'high';\r\n      }\r\n    }\r\n\r\n    for (const keyword of lowPriorityKeywords) {\r\n      if (lowerTitle.includes(keyword) || lowerDesc.includes(keyword)) {\r\n        return 'low';\r\n      }\r\n    }\r\n\r\n    return 'medium';\r\n  },\r\n  \r\n  // Đề xuất thời gian hoàn thành dựa trên nội dung bằng AI\r\n  suggestDueDate: async (title: string, description?: string): Promise<string | null> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const today = new Date();\r\n      const prompt = `\r\nPhân tích công việc sau và đề xuất số ngày cần để hoàn thành:\r\n\r\nTiêu đề: ${title}\r\nMô tả: ${description || 'Không có mô tả'}\r\nNgày hiện tại: ${today.toLocaleDateString('vi-VN')}\r\n\r\nHãy trả về chỉ một số nguyên (1-365) đại diện cho số ngày cần để hoàn thành công việc này.\r\nXem xét:\r\n- Độ phức tạp của công việc\r\n- Thời gian thông thường cần thiết\r\n- Từ khóa về thời gian (gấp, khẩn, tuần này, tháng này, etc.)\r\n\r\nChỉ trả về số nguyên, không giải thích.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n      const daysToAdd = parseInt(response);\r\n\r\n      if (isNaN(daysToAdd) || daysToAdd < 1 || daysToAdd > 365) {\r\n        return AIService.fallbackSuggestDueDate(title, description);\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error suggesting due date with AI:\", error);\r\n      return AIService.fallbackSuggestDueDate(title, description);\r\n    }\r\n  },\r\n\r\n  // Fallback method for due date suggestion\r\n  fallbackSuggestDueDate: (title: string, description?: string): string | null => {\r\n    try {\r\n      const lowerTitle = title.toLowerCase();\r\n      const lowerDesc = description?.toLowerCase() || '';\r\n\r\n      const today = new Date();\r\n      let daysToAdd = 7;\r\n\r\n      if (lowerTitle.includes('gấp') || lowerTitle.includes('khẩn') || lowerDesc.includes('gấp')) {\r\n        daysToAdd = 1;\r\n      } else if (lowerTitle.includes('tuần này') || lowerDesc.includes('tuần này')) {\r\n        daysToAdd = 5;\r\n      } else if (lowerTitle.includes('tháng') || lowerDesc.includes('tháng')) {\r\n        daysToAdd = 30;\r\n      }\r\n\r\n      const dueDate = new Date(today);\r\n      dueDate.setDate(today.getDate() + daysToAdd);\r\n      return dueDate.toISOString().split('T')[0];\r\n    } catch (error) {\r\n      console.error(\"Error in fallback due date suggestion:\", error);\r\n      return null;\r\n    }\r\n  },\r\n  \r\n  // Tóm tắt danh sách công việc bằng AI\r\n  summarizeTasks: async (tasks: Task[]): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return AIService.fallbackSummarizeTasks(tasks);\r\n      }\r\n\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const pending = tasks.length - completed;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      // Tạo context cho AI\r\n      const taskSummary = tasks.map(task =>\r\n        `- ${task.title} (${task.priority} priority, ${task.completed ? 'completed' : 'pending'})`\r\n      ).join('\\n');\r\n\r\n      const prompt = `\r\nPhân tích danh sách công việc sau và tạo một bản tóm tắt thông minh bằng tiếng Việt với Markdown format:\r\n\r\n## Thống kê hiện tại:\r\n- **Tổng số công việc**: ${tasks.length}\r\n- **Đã hoàn thành**: ${completed}\r\n- **Chưa hoàn thành**: ${pending}\r\n- **Tỷ lệ hoàn thành**: ${completionRate}%\r\n- **Công việc ưu tiên cao chưa hoàn thành**: ${highPriorityPending}\r\n\r\n## Danh sách công việc:\r\n${taskSummary || 'Không có công việc nào'}\r\n\r\nHãy tạo một bản tóm tắt với Markdown format bao gồm:\r\n1. **Đánh giá hiện trạng** - sử dụng emoji và bold\r\n2. **Lời khuyên hoặc động viên** - sử dụng blockquote\r\n3. **Gợi ý hành động tiếp theo** - sử dụng danh sách có dấu đầu dòng\r\n\r\nSử dụng:\r\n- **Bold** cho các điểm quan trọng\r\n- > Blockquote cho lời khuyên\r\n- • Bullet points cho các bước hành động\r\n- 📊 📈 ✅ ⚡ 🎯 emoji phù hợp\r\n\r\nGiữ giọng điệu thân thiện và tích cực.`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      const response = result.response.text().trim();\r\n\r\n      return response || AIService.fallbackSummarizeTasks(tasks);\r\n    } catch (error) {\r\n      console.error(\"Error summarizing tasks with AI:\", error);\r\n      return AIService.fallbackSummarizeTasks(tasks);\r\n    }\r\n  },\r\n\r\n  // Fallback method for task summarization\r\n  fallbackSummarizeTasks: (tasks: Task[]): string => {\r\n    try {\r\n      const completed = tasks.filter(task => task.completed).length;\r\n      const completionRate = tasks.length > 0 ? Math.round((completed / tasks.length) * 100) : 0;\r\n      const highPriorityPending = tasks.filter(task => task.priority === 'high' && !task.completed).length;\r\n\r\n      let summary = `Bạn đã hoàn thành ${completed}/${tasks.length} công việc (${completionRate}%). `;\r\n\r\n      if (highPriorityPending > 0) {\r\n        summary += `Hiện có ${highPriorityPending} công việc ưu tiên cao cần hoàn thành. `;\r\n      }\r\n\r\n      if (completionRate >= 80) {\r\n        summary += 'Bạn đang làm rất tốt! Hãy tiếp tục phát huy.';\r\n      } else if (completionRate >= 50) {\r\n        summary += 'Bạn đang tiến triển tốt. Cố gắng hoàn thành các công việc còn lại nhé.';\r\n      } else if (tasks.length > 0) {\r\n        summary += 'Bạn cần tập trung hơn để hoàn thành nhiều công việc hơn.';\r\n      } else {\r\n        summary = 'Bạn chưa có công việc nào. Hãy thêm một số công việc để bắt đầu.';\r\n      }\r\n\r\n      return summary;\r\n    } catch (error) {\r\n      console.error(\"Error in fallback task summarization:\", error);\r\n      return \"Không thể tạo bản tóm tắt tại thời điểm này.\";\r\n    }\r\n  },\r\n  \r\n  // Chat với AI (chỉ sử dụng client-side)\r\n  chat: async (message: string, chatHistory: ChatMessage[] = []): Promise<string> => {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        return \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n      }\r\n\r\n      // Tạo context từ lịch sử chat\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContent(prompt);\r\n      return result.response.text().trim();\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat:\", error);\r\n      return \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Chat streaming với Gemini\r\n  chatStream: async function* (message: string, chatHistory: ChatMessage[] = []): AsyncGenerator<string, void, unknown> {\r\n    try {\r\n      if (!(await initializeAI())) {\r\n        yield \"Xin lỗi, AI hiện không khả dụng. Vui lòng kiểm tra cấu hình API key.\";\r\n        return;\r\n      }\r\n\r\n      const historyContext = chatHistory.length > 0\r\n        ? chatHistory.map(msg => `${msg.role === 'user' ? 'Người dùng' : 'AI'}: ${msg.content}`).join('\\n')\r\n        : '';\r\n\r\n      const prompt = `\r\nBạn là Dr.AITime, một trợ lý AI thông minh chuyên về quản lý thời gian và công việc.\r\n\r\n${historyContext ? `Lịch sử cuộc trò chuyện:\\n${historyContext}\\n` : ''}\r\n\r\nNgười dùng: ${message}\r\n\r\nHãy trả lời một cách thân thiện, hữu ích và chuyên nghiệp. Tập trung vào:\r\n- Quản lý thời gian\r\n- Tổ chức công việc\r\n- Tăng năng suất\r\n- Lời khuyên thực tế\r\n\r\nTrả lời bằng tiếng Việt và sử dụng Markdown format để làm cho câu trả lời dễ đọc hơn:\r\n- Sử dụng **bold** cho các điểm quan trọng\r\n- Sử dụng danh sách có dấu đầu dòng cho các bước hoặc gợi ý\r\n- Sử dụng > blockquote cho các lời khuyên đặc biệt\r\n- Sử dụng \\`code\\` cho các thuật ngữ kỹ thuật\r\n- Sử dụng ### cho tiêu đề phụ nếu cần`;\r\n\r\n      const result = await model.generateContentStream(prompt);\r\n\r\n      for await (const chunk of result.stream) {\r\n        const chunkText = chunk.text();\r\n        if (chunkText) {\r\n          yield chunkText;\r\n        }\r\n      }\r\n    } catch (error) {\r\n      console.error(\"Error in AI chat stream:\", error);\r\n      yield \"Xin lỗi, đã có lỗi xảy ra. Vui lòng kiểm tra API key và thử lại.\";\r\n    }\r\n  },\r\n\r\n  // Kích hoạt API Gemini khi có API key\r\n  initWithApiKey: (apiKey: string) => {\r\n    try {\r\n      genAI = new GoogleGenerativeAI(apiKey);\r\n      model = genAI.getGenerativeModel({ model: \"gemini-2.5-flash-preview-05-20\" });\r\n      console.log(\"AI service initialized with API key\");\r\n      return true;\r\n    } catch (error) {\r\n      console.error(\"Error initializing AI service:\", error);\r\n      return false;\r\n    }\r\n  },\r\n\r\n  // Kiểm tra trạng thái AI\r\n  isAvailable: (): boolean => {\r\n    return !!model;\r\n  }\r\n};"], "names": [], "mappings": ";;;AAAA;;AAGA,qBAAqB;AACrB,IAAI,QAAmC;AACvC,IAAI,QAAa;AAEjB,2BAA2B;AAC3B,MAAM,sBAAsB;IAC1B,IAAI;QACF,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,QAAQ,KAAK,CAAC;YACd,OAAO;QACT;QACA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,OAAO,KAAK,MAAM,IAAI;IACxB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;IACT;AACF;AAEA,4CAA4C;AAC5C,MAAM,eAAe;IACnB,IAAI,OAAO,OAAO,MAAM,kBAAkB;IAE1C,MAAM,SAAS,MAAM;IACrB,IAAI,UAAU,CAAC,OAAO;QACpB,QAAQ,IAAI,8JAAA,CAAA,qBAAkB,CAAC;QAC/B,QAAQ,MAAM,kBAAkB,CAAC;YAAE,OAAO;QAAiC;IAC7E;IACA,OAAO,CAAC,CAAC;AACX;AASO,MAAM,YAAY;IACvB,6DAA6D;IAC7D,iBAAiB,OAAO,OAAe;QACrC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,sDAAsD;gBACtD,OAAO,UAAU,uBAAuB,CAAC,OAAO;YAClD;YAEA,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;;;;;;;;oCAQL,CAAC;YAE/B,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,WAAW,GAAG,IAAI;YAE1D,IAAI;gBAAC;gBAAQ;gBAAU;aAAM,CAAC,QAAQ,CAAC,WAAW;gBAChD,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,uBAAuB,CAAC,OAAO;QAClD;IACF;IAEA,0CAA0C;IAC1C,yBAAyB,CAAC,OAAe;QACvC,IAAI,CAAC,OAAO,OAAO;QAEnB,MAAM,aAAa,MAAM,WAAW;QACpC,MAAM,YAAY,aAAa,iBAAiB;QAEhD,MAAM,uBAAuB;YAAC;YAAO;YAAQ;YAAQ;YAAc;YAAY;SAAW;QAC1F,MAAM,sBAAsB;YAAC;YAAa;YAAY;YAAa;YAAW;SAAM;QAEpF,KAAK,MAAM,WAAW,qBAAsB;YAC1C,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,KAAK,MAAM,WAAW,oBAAqB;YACzC,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBAC/D,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,yDAAyD;IACzD,gBAAgB,OAAO,OAAe;QACpC,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,QAAQ,IAAI;YAClB,MAAM,SAAS,CAAC;;;SAGb,EAAE,MAAM;OACV,EAAE,eAAe,iBAAiB;eAC1B,EAAE,MAAM,kBAAkB,CAAC,SAAS;;;;;;;;uCAQZ,CAAC;YAElC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAC5C,MAAM,YAAY,SAAS;YAE3B,IAAI,MAAM,cAAc,YAAY,KAAK,YAAY,KAAK;gBACxD,OAAO,UAAU,sBAAsB,CAAC,OAAO;YACjD;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,OAAO,UAAU,sBAAsB,CAAC,OAAO;QACjD;IACF;IAEA,0CAA0C;IAC1C,wBAAwB,CAAC,OAAe;QACtC,IAAI;YACF,MAAM,aAAa,MAAM,WAAW;YACpC,MAAM,YAAY,aAAa,iBAAiB;YAEhD,MAAM,QAAQ,IAAI;YAClB,IAAI,YAAY;YAEhB,IAAI,WAAW,QAAQ,CAAC,UAAU,WAAW,QAAQ,CAAC,WAAW,UAAU,QAAQ,CAAC,QAAQ;gBAC1F,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,eAAe,UAAU,QAAQ,CAAC,aAAa;gBAC5E,YAAY;YACd,OAAO,IAAI,WAAW,QAAQ,CAAC,YAAY,UAAU,QAAQ,CAAC,UAAU;gBACtE,YAAY;YACd;YAEA,MAAM,UAAU,IAAI,KAAK;YACzB,QAAQ,OAAO,CAAC,MAAM,OAAO,KAAK;YAClC,OAAO,QAAQ,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0CAA0C;YACxD,OAAO;QACT;IACF;IAEA,sCAAsC;IACtC,gBAAgB,OAAO;QACrB,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO,UAAU,sBAAsB,CAAC;YAC1C;YAEA,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,UAAU,MAAM,MAAM,GAAG;YAC/B,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,qBAAqB;YACrB,MAAM,cAAc,MAAM,GAAG,CAAC,CAAA,OAC5B,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,SAAS,GAAG,cAAc,UAAU,CAAC,CAAC,EAC1F,IAAI,CAAC;YAEP,MAAM,SAAS,CAAC;;;;yBAIG,EAAE,MAAM,MAAM,CAAC;qBACnB,EAAE,UAAU;uBACV,EAAE,QAAQ;wBACT,EAAE,eAAe;6CACI,EAAE,oBAAoB;;;AAGnE,EAAE,eAAe,yBAAyB;;;;;;;;;;;;;sCAaJ,CAAC;YAEjC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,MAAM,WAAW,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;YAE5C,OAAO,YAAY,UAAU,sBAAsB,CAAC;QACtD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO,UAAU,sBAAsB,CAAC;QAC1C;IACF;IAEA,yCAAyC;IACzC,wBAAwB,CAAC;QACvB,IAAI;YACF,MAAM,YAAY,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC7D,MAAM,iBAAiB,MAAM,MAAM,GAAG,IAAI,KAAK,KAAK,CAAC,AAAC,YAAY,MAAM,MAAM,GAAI,OAAO;YACzF,MAAM,sBAAsB,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK,UAAU,CAAC,KAAK,SAAS,EAAE,MAAM;YAEpG,IAAI,UAAU,CAAC,kBAAkB,EAAE,UAAU,CAAC,EAAE,MAAM,MAAM,CAAC,YAAY,EAAE,eAAe,IAAI,CAAC;YAE/F,IAAI,sBAAsB,GAAG;gBAC3B,WAAW,CAAC,QAAQ,EAAE,oBAAoB,uCAAuC,CAAC;YACpF;YAEA,IAAI,kBAAkB,IAAI;gBACxB,WAAW;YACb,OAAO,IAAI,kBAAkB,IAAI;gBAC/B,WAAW;YACb,OAAO,IAAI,MAAM,MAAM,GAAG,GAAG;gBAC3B,WAAW;YACb,OAAO;gBACL,UAAU;YACZ;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO;QACT;IACF;IAEA,wCAAwC;IACxC,MAAM,OAAO,SAAiB,cAA6B,EAAE;QAC3D,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,OAAO;YACT;YAEA,8BAA8B;YAC9B,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,eAAe,CAAC;YAC3C,OAAO,OAAO,QAAQ,CAAC,IAAI,GAAG,IAAI;QACpC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,OAAO;QACT;IACF;IAEA,4BAA4B;IAC5B,YAAY,gBAAiB,OAAe,EAAE,cAA6B,EAAE;QAC3E,IAAI;YACF,IAAI,CAAE,MAAM,gBAAiB;gBAC3B,MAAM;gBACN;YACF;YAEA,MAAM,iBAAiB,YAAY,MAAM,GAAG,IACxC,YAAY,GAAG,CAAC,CAAA,MAAO,GAAG,IAAI,IAAI,KAAK,SAAS,eAAe,KAAK,EAAE,EAAE,IAAI,OAAO,EAAE,EAAE,IAAI,CAAC,QAC5F;YAEJ,MAAM,SAAS,CAAC;;;AAGtB,EAAE,iBAAiB,CAAC,0BAA0B,EAAE,eAAe,EAAE,CAAC,GAAG,GAAG;;YAE5D,EAAE,QAAQ;;;;;;;;;;;;;qCAae,CAAC;YAEhC,MAAM,SAAS,MAAM,MAAM,qBAAqB,CAAC;YAEjD,WAAW,MAAM,SAAS,OAAO,MAAM,CAAE;gBACvC,MAAM,YAAY,MAAM,IAAI;gBAC5B,IAAI,WAAW;oBACb,MAAM;gBACR;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR;IACF;IAEA,sCAAsC;IACtC,gBAAgB,CAAC;QACf,IAAI;YACF,QAAQ,IAAI,8JAAA,CAAA,qBAAkB,CAAC;YAC/B,QAAQ,MAAM,kBAAkB,CAAC;gBAAE,OAAO;YAAiC;YAC3E,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,OAAO;QACT;IACF;IAEA,yBAAyB;IACzB,aAAa;QACX,OAAO,CAAC,CAAC;IACX;AACF", "debugId": null}}, {"offset": {"line": 2168, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/task-service.ts"], "sourcesContent": ["import { Task } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Tạo một lớp cache đơn giản để lưu trữ tạm thởi\r\nlet tasksCache: Task[] = [];\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\nexport const TaskService = {\r\n  getTasks: async (): Promise<Task[]> => {\r\n    const now = Date.now();\r\n    // Kiểm tra xem cache có hết hạn chưa\r\n    if (now - lastFetchTime > CACHE_DURATION || tasksCache.length === 0) {\r\n      try {\r\n        const tasks = await ApiService.tasks.getAll();\r\n        tasksCache = tasks;\r\n        lastFetchTime = now;\r\n        return tasks;\r\n      } catch (error) {\r\n        console.error('Lỗi khi lấy danh sách công việc:', error);\r\n        // Nếu có lỗi, trả về cache hiện tại hoặc mảng rỗng\r\n        return tasksCache.length > 0 ? tasksCache : [];\r\n      }\r\n    }\r\n    return tasksCache;\r\n  },\r\n  \r\n  getTask: async (id: string): Promise<Task | undefined> => {\r\n    try {\r\n      // Kiểm tra trong cache trước\r\n      const cachedTask = tasksCache.find(task => task.id === id);\r\n      if (cachedTask) return cachedTask;\r\n\r\n      // Nếu không có trong cache, gọi API\r\n      const task = await ApiService.tasks.getById(id);\r\n\r\n      // API trả về null cho 404, không throw error\r\n      if (task === null) {\r\n        console.debug(`Task với ID ${id} không tồn tại (đã bị xóa hoặc không hợp lệ)`);\r\n        return undefined;\r\n      }\r\n\r\n      return task;\r\n    } catch (error: any) {\r\n      // Chỉ log lỗi cho các lỗi khác (không phải 404)\r\n      console.error(`Lỗi khi lấy công việc với id ${id}:`, error);\r\n      return undefined;\r\n    }\r\n  },\r\n  \r\n  createTask: async (task: Omit<Task, 'id' | 'createdAt' | 'updatedAt'>): Promise<Task> => {\r\n    try {\r\n      const newTask = await ApiService.tasks.create(task);\r\n      // Cập nhật cache\r\n      tasksCache = [...tasksCache, newTask];\r\n      return newTask;\r\n    } catch (error) {\r\n      console.error('Lỗi khi tạo công việc mới:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  updateTask: async (id: string, updates: Partial<Omit<Task, 'id' | 'createdAt' | 'updatedAt'>>): Promise<Task> => {\r\n    try {\r\n      console.log(`TaskService: Đang cập nhật task ${id} với data:`, updates);\r\n      const updatedTask = await ApiService.tasks.update(id, updates);\r\n      console.log(`TaskService: Task ${id} đã được cập nhật:`, updatedTask);\r\n\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`TaskService: Lỗi khi cập nhật công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  deleteTask: async (id: string): Promise<void> => {\r\n    try {\r\n      await ApiService.tasks.delete(id);\r\n      // Cập nhật cache\r\n      tasksCache = tasksCache.filter(task => task.id !== id);\r\n    } catch (error) {\r\n      console.error(`Lỗi khi xóa công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  toggleTaskCompletion: async (id: string): Promise<Task> => {\r\n    try {\r\n      const updatedTask = await ApiService.tasks.toggleCompletion(id);\r\n      // Cập nhật cache\r\n      const taskIndex = tasksCache.findIndex(task => task.id === id);\r\n      if (taskIndex !== -1) {\r\n        tasksCache[taskIndex] = updatedTask;\r\n      }\r\n      return updatedTask;\r\n    } catch (error) {\r\n      console.error(`Lỗi khi chuyển trạng thái hoàn thành của công việc với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Phương thức để xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    tasksCache = [];\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshTasks: async (): Promise<Task[]> => {\r\n    try {\r\n      const tasks = await ApiService.tasks.getAll();\r\n      tasksCache = tasks;\r\n      lastFetchTime = Date.now();\r\n      return tasks;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới danh sách công việc:', error);\r\n      return tasksCache;\r\n    }\r\n  }\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,iDAAiD;AACjD,IAAI,aAAqB,EAAE;AAC3B,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEhC,MAAM,cAAc;IACzB,UAAU;QACR,MAAM,MAAM,KAAK,GAAG;QACpB,qCAAqC;QACrC,IAAI,MAAM,gBAAgB,kBAAkB,WAAW,MAAM,KAAK,GAAG;YACnE,IAAI;gBACF,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;gBAC3C,aAAa;gBACb,gBAAgB;gBAChB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,mDAAmD;gBACnD,OAAO,WAAW,MAAM,GAAG,IAAI,aAAa,EAAE;YAChD;QACF;QACA,OAAO;IACT;IAEA,SAAS,OAAO;QACd,IAAI;YACF,6BAA6B;YAC7B,MAAM,aAAa,WAAW,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YACvD,IAAI,YAAY,OAAO;YAEvB,oCAAoC;YACpC,MAAM,OAAO,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,OAAO,CAAC;YAE5C,6CAA6C;YAC7C,IAAI,SAAS,MAAM;gBACjB,QAAQ,KAAK,CAAC,CAAC,YAAY,EAAE,GAAG,4CAA4C,CAAC;gBAC7E,OAAO;YACT;YAEA,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,gDAAgD;YAChD,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,OAAO;QACT;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,UAAU,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9C,iBAAiB;YACjB,aAAa;mBAAI;gBAAY;aAAQ;YACrC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,MAAM;QACR;IACF;IAEA,YAAY,OAAO,IAAY;QAC7B,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,GAAG,UAAU,CAAC,EAAE;YAC/D,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI;YACtD,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,GAAG,kBAAkB,CAAC,EAAE;YAEzD,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,+CAA+C,EAAE,GAAG,CAAC,CAAC,EAAE;YACvE,MAAM;QACR;IACF;IAEA,YAAY,OAAO;QACjB,IAAI;YACF,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM,CAAC;YAC9B,iBAAiB;YACjB,aAAa,WAAW,MAAM,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QACrD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6BAA6B,EAAE,GAAG,CAAC,CAAC,EAAE;YACrD,MAAM;QACR;IACF;IAEA,sBAAsB,OAAO;QAC3B,IAAI;YACF,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,gBAAgB,CAAC;YAC5D,iBAAiB;YACjB,MAAM,YAAY,WAAW,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;YAC3D,IAAI,cAAc,CAAC,GAAG;gBACpB,UAAU,CAAC,UAAU,GAAG;YAC1B;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0DAA0D,EAAE,GAAG,CAAC,CAAC,EAAE;YAClF,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,YAAY;QACV,aAAa,EAAE;QACf,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,cAAc;QACZ,IAAI;YACF,MAAM,QAAQ,MAAM,wIAAA,CAAA,aAAU,CAAC,KAAK,CAAC,MAAM;YAC3C,aAAa;YACb,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;AACF", "debugId": null}}, {"offset": {"line": 2292, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport * as React from \"react\";\n\nimport { cn } from \"@/lib/utils\";\nimport { Cross2Icon } from \"@radix-ui/react-icons\";\n\nconst Dialog = DialogPrimitive.Root;\n\nconst DialogTrigger = DialogPrimitive.Trigger;\n\nconst DialogPortal = DialogPrimitive.Portal;\n\nconst DialogClose = DialogPrimitive.Close;\n\nconst DialogOverlay = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Overlay\n    ref={ref}\n    className={cn(\n      \"fixed inset-0 z-[51] data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className,\n    )}\n    {...props}\n  />\n));\nDialogOverlay.displayName = DialogPrimitive.Overlay.displayName;\n\nconst DialogContent = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>\n>(({ className, children, ...props }, ref) => (\n  <DialogPortal>\n    <DialogOverlay />\n    <DialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-1/2 top-1/2 z-[51] grid max-h-[calc(100%-4rem)] w-full -translate-x-1/2 -translate-y-1/2 gap-4 overflow-y-auto border bg-background/90 backdrop-blur-md p-6 shadow-lg shadow-black/10 duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:max-w-[400px] sm:rounded-xl\",\n        className,\n      )}\n      {...props}\n    >\n      {children}\n      <DialogPrimitive.Close className=\"group absolute right-3 top-3 flex size-7 items-center justify-center rounded-lg outline-offset-2 transition-colors focus-visible:outline focus-visible:outline-2 focus-visible:outline-ring/70 disabled:pointer-events-none\">\n        <Cross2Icon\n          width={16}\n          height={16}\n          strokeWidth={2}\n          className=\"opacity-60 transition-opacity group-hover:opacity-100\"\n        />\n        <span className=\"sr-only\">Close</span>\n      </DialogPrimitive.Close>\n    </DialogPrimitive.Content>\n  </DialogPortal>\n));\nDialogContent.displayName = DialogPrimitive.Content.displayName;\n\nconst DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div className={cn(\"flex flex-col space-y-1.5 text-center sm:text-left\", className)} {...props} />\n);\nDialogHeader.displayName = \"DialogHeader\";\n\nconst DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end sm:gap-3\", className)}\n    {...props}\n  />\n);\nDialogFooter.displayName = \"DialogFooter\";\n\nconst DialogTitle = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold tracking-tight\", className)}\n    {...props}\n  />\n));\nDialogTitle.displayName = DialogPrimitive.Title.displayName;\n\nconst DialogDescription = React.forwardRef<\n  React.ElementRef<typeof DialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <DialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nDialogDescription.displayName = DialogPrimitive.Description.displayName;\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,gBAAgB,kKAAA,CAAA,UAAuB;AAE7C,MAAM,eAAe,kKAAA,CAAA,SAAsB;AAE3C,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gJACA;QAED,GAAG,KAAK;;;;;;AAGb,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAuB;gBACtB,KAAK;gBACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6kBACA;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAqB;wBAAC,WAAU;;0CAC/B,8OAAC,gLAAA,CAAA,aAAU;gCACT,OAAO;gCACP,QAAQ;gCACR,aAAa;gCACb,WAAU;;;;;;0CAEZ,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKlC,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sDAAsD;QAAa,GAAG,KAAK;;;;;;AAEhG,aAAa,WAAW,GAAG;AAE3B,MAAM,eAAe,CAAC,EAAE,SAAS,EAAE,GAAG,OAA6C,iBACjF,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG;AAE3B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,kCAAoB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,cAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kKAAA,CAAA,cAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2427, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport * as TabsPrimitive from \"@radix-ui/react-tabs\";\nimport { cn } from \"@/lib/utils\";\n\nconst Tabs = TabsPrimitive.Root;\n\nconst TabsList = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.List>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.List>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.List\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsList.displayName = TabsPrimitive.List.displayName;\n\nconst TabsTrigger = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Trigger>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsTrigger.displayName = TabsPrimitive.Trigger.displayName;\n\nconst TabsContent = React.forwardRef<\n  React.ElementRef<typeof TabsPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof TabsPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <TabsPrimitive.Content\n    ref={ref}\n    className={cn(\n      \"mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2\",\n      className\n    )}\n    {...props}\n  />\n));\nTabsContent.displayName = TabsPrimitive.Content.displayName;\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent };\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,OAAO,gKAAA,CAAA,OAAkB;AAE/B,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,OAAkB;QACjB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8FACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG,gKAAA,CAAA,OAAkB,CAAC,WAAW;AAErD,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uYACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW;AAE3D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,gKAAA,CAAA,UAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mIACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,gKAAA,CAAA,UAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 2558, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/markdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport ReactMarkdown from 'react-markdown';\r\nimport remarkGfm from 'remark-gfm';\r\nimport rehypeHighlight from 'rehype-highlight';\r\nimport { cn } from '@/lib/utils';\r\n\r\n// Import highlight.js styles\r\nimport 'highlight.js/styles/github-dark.css';\r\n\r\ninterface MarkdownProps {\r\n  content: string;\r\n  className?: string;\r\n}\r\n\r\nexport function Markdown({ content, className }: MarkdownProps) {\r\n  return (\r\n    <div className={cn(\"max-w-none\", className)}>\r\n      <ReactMarkdown\r\n        remarkPlugins={[remarkGfm]}\r\n        rehypePlugins={[rehypeHighlight]}\r\n        components={{\r\n          // Custom styling for different elements\r\n          h1: ({ children }) => (\r\n            <h1 className=\"text-lg font-bold mb-2 text-foreground\">{children}</h1>\r\n          ),\r\n          h2: ({ children }) => (\r\n            <h2 className=\"text-base font-semibold mb-2 text-foreground\">{children}</h2>\r\n          ),\r\n          h3: ({ children }) => (\r\n            <h3 className=\"text-sm font-medium mb-1 text-foreground\">{children}</h3>\r\n          ),\r\n          // Sử dụng div thay vì p để tránh nesting issues với code blocks\r\n          p: ({ children }) => (\r\n            <div className=\"mb-2 text-foreground leading-relaxed\">{children}</div>\r\n          ),\r\n          ul: ({ children }) => (\r\n            <ul className=\"list-disc list-inside mb-2 space-y-1 text-foreground\">{children}</ul>\r\n          ),\r\n          ol: ({ children }) => (\r\n            <ol className=\"list-decimal list-inside mb-2 space-y-1 text-foreground\">{children}</ol>\r\n          ),\r\n          li: ({ children }) => (\r\n            <li className=\"text-foreground\">{children}</li>\r\n          ),\r\n          blockquote: ({ children }) => (\r\n            <blockquote className=\"border-l-4 border-blue-500 pl-4 italic my-2 text-muted-foreground\">\r\n              {children}\r\n            </blockquote>\r\n          ),\r\n          code: ({ className, children, ...props }: any) => {\r\n            const match = /language-(\\w+)/.exec(className || '');\r\n            const inline = props.inline;\r\n            return !inline ? (\r\n              <div className=\"relative my-3\">\r\n                <pre className=\"bg-muted rounded-md p-3 overflow-x-auto text-sm\">\r\n                  <code className={className} {...props}>\r\n                    {children}\r\n                  </code>\r\n                </pre>\r\n                {match && (\r\n                  <span className=\"absolute top-2 right-2 text-xs text-muted-foreground bg-background px-2 py-1 rounded\">\r\n                    {match[1]}\r\n                  </span>\r\n                )}\r\n              </div>\r\n            ) : (\r\n              <code className=\"bg-muted px-1 py-0.5 rounded text-sm font-mono\" {...props}>\r\n                {children}\r\n              </code>\r\n            );\r\n          },\r\n          table: ({ children }) => (\r\n            <div className=\"overflow-x-auto my-2\">\r\n              <table className=\"min-w-full border-collapse border border-border\">\r\n                {children}\r\n              </table>\r\n            </div>\r\n          ),\r\n          th: ({ children }) => (\r\n            <th className=\"border border-border bg-muted px-3 py-2 text-left font-medium\">\r\n              {children}\r\n            </th>\r\n          ),\r\n          td: ({ children }) => (\r\n            <td className=\"border border-border px-3 py-2\">{children}</td>\r\n          ),\r\n          a: ({ children, href }) => (\r\n            <a \r\n              href={href} \r\n              target=\"_blank\" \r\n              rel=\"noopener noreferrer\"\r\n              className=\"text-blue-600 hover:text-blue-800 underline\"\r\n            >\r\n              {children}\r\n            </a>\r\n          ),\r\n          strong: ({ children }) => (\r\n            <strong className=\"font-semibold text-foreground\">{children}</strong>\r\n          ),\r\n          em: ({ children }) => (\r\n            <em className=\"italic text-foreground\">{children}</em>\r\n          ),\r\n          hr: () => (\r\n            <hr className=\"my-4 border-border\" />\r\n          ),\r\n        }}\r\n      >\r\n        {content}\r\n      </ReactMarkdown>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AANA;;;;;;;AAgBO,SAAS,SAAS,EAAE,OAAO,EAAE,SAAS,EAAiB;IAC5D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;kBAC/B,cAAA,8OAAC,wLAAA,CAAA,UAAa;YACZ,eAAe;gBAAC,6IAAA,CAAA,UAAS;aAAC;YAC1B,eAAe;gBAAC,mJAAA,CAAA,UAAe;aAAC;YAChC,YAAY;gBACV,wCAAwC;gBACxC,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA0C;;;;;;gBAE1D,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAgD;;;;;;gBAEhE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA4C;;;;;;gBAE5D,gEAAgE;gBAChE,GAAG,CAAC,EAAE,QAAQ,EAAE,iBACd,8OAAC;wBAAI,WAAU;kCAAwC;;;;;;gBAEzD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAwD;;;;;;gBAExE,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA2D;;;;;;gBAE3E,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAmB;;;;;;gBAEnC,YAAY,CAAC,EAAE,QAAQ,EAAE,iBACvB,8OAAC;wBAAW,WAAU;kCACnB;;;;;;gBAGL,MAAM,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAY;oBAC3C,MAAM,QAAQ,iBAAiB,IAAI,CAAC,aAAa;oBACjD,MAAM,SAAS,MAAM,MAAM;oBAC3B,OAAO,CAAC,uBACN,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAW;oCAAY,GAAG,KAAK;8CAClC;;;;;;;;;;;4BAGJ,uBACC,8OAAC;gCAAK,WAAU;0CACb,KAAK,CAAC,EAAE;;;;;;;;;;;+CAKf,8OAAC;wBAAK,WAAU;wBAAkD,GAAG,KAAK;kCACvE;;;;;;gBAGP;gBACA,OAAO,CAAC,EAAE,QAAQ,EAAE,iBAClB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAM,WAAU;sCACd;;;;;;;;;;;gBAIP,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCACX;;;;;;gBAGL,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAAkC;;;;;;gBAElD,GAAG,CAAC,EAAE,QAAQ,EAAE,IAAI,EAAE,iBACpB,8OAAC;wBACC,MAAM;wBACN,QAAO;wBACP,KAAI;wBACJ,WAAU;kCAET;;;;;;gBAGL,QAAQ,CAAC,EAAE,QAAQ,EAAE,iBACnB,8OAAC;wBAAO,WAAU;kCAAiC;;;;;;gBAErD,IAAI,CAAC,EAAE,QAAQ,EAAE,iBACf,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;gBAE1C,IAAI,kBACF,8OAAC;wBAAG,WAAU;;;;;;YAElB;sBAEC;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 2779, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-assistant.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { AIService } from '@/lib/services/ai-service';\r\nimport { But<PERSON> } from '@/components/ui/button';\r\nimport { Lightbulb, Loader2, MessageCircle, Brain } from 'lucide-react';\r\nimport { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { TaskService } from '@/lib/services/task-service';\r\nimport {\r\n  Dialog,\r\n  DialogContent,\r\n  DialogHeader,\r\n  DialogTitle,\r\n  DialogDescription,\r\n  DialogFooter\r\n} from '@/components/ui/dialog';\r\nimport { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';\r\nimport { Markdown } from '@/components/ui/markdown';\r\n\r\nexport function AIAssistant() {\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [summary, setSummary] = useState<string | null>(null);\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [activeTab, setActiveTab] = useState('summary');\r\n\r\n  const generateSummary = async () => {\r\n    setIsLoading(true);\r\n    try {\r\n      const tasks = await TaskService.getTasks();\r\n      const summaryText = await AIService.summarizeTasks(tasks);\r\n      setSummary(summaryText);\r\n    } catch (error) {\r\n      console.error(\"Error generating summary:\", error);\r\n      setSummary(\"Đã xảy ra lỗi khi tạo báo cáo. Vui lòng thử lại sau.\");\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed bottom-4 right-4 z-50\">\r\n      <Button\r\n        onClick={() => setIsOpen(true)}\r\n        size=\"icon\"\r\n        className=\"h-12 w-12 rounded-full shadow-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700\"\r\n      >\r\n        <Brain className=\"h-6 w-6\" />\r\n      </Button>\r\n\r\n      <Dialog open={isOpen} onOpenChange={setIsOpen}>\r\n        <DialogContent className=\"sm:max-w-[480px] p-0 overflow-hidden\">\r\n          <DialogHeader className=\"px-6 pt-6 pb-2\">\r\n            <DialogTitle className=\"flex items-center gap-2\">\r\n              <Brain className=\"h-5 w-5\" />\r\n              Dr.AITime\r\n            </DialogTitle>\r\n            <DialogDescription>Trợ lý AI thông minh cho quản lý thời gian</DialogDescription>\r\n          </DialogHeader>\r\n\r\n          <Tabs value={activeTab} onValueChange={setActiveTab} className=\"px-6\">\r\n            <TabsList className=\"grid w-full grid-cols-2\">\r\n              <TabsTrigger value=\"summary\" className=\"flex items-center gap-2\">\r\n                <Lightbulb className=\"h-4 w-4\" />\r\n                Phân tích\r\n              </TabsTrigger>\r\n              <TabsTrigger value=\"chat\" className=\"flex items-center gap-2\">\r\n                <MessageCircle className=\"h-4 w-4\" />\r\n                Chat AI\r\n              </TabsTrigger>\r\n            </TabsList>\r\n\r\n            <TabsContent value=\"summary\" className=\"py-4 text-sm\">\r\n              {isLoading ? (\r\n                <div className=\"flex justify-center py-6\">\r\n                  <Loader2 className=\"h-6 w-6 animate-spin text-muted-foreground\" />\r\n                </div>\r\n              ) : summary ? (\r\n                <div className=\"space-y-2\">\r\n                  <Markdown content={summary} className=\"text-foreground\" />\r\n                </div>\r\n              ) : (\r\n                <p className=\"text-muted-foreground\">\r\n                  AI có thể phân tích công việc của bạn và đưa ra đề xuất thông minh.\r\n                </p>\r\n              )}\r\n            </TabsContent>\r\n\r\n            <TabsContent value=\"chat\" className=\"py-4\">\r\n              <div className=\"text-center text-muted-foreground\">\r\n                <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                <p className=\"text-sm\">Tính năng chat AI sẽ có sẵn trong chatbox riêng biệt.</p>\r\n                <p className=\"text-xs mt-1\">Nhấn vào biểu tượng chat ở góc trái màn hình.</p>\r\n              </div>\r\n            </TabsContent>\r\n          </Tabs>\r\n\r\n          <DialogFooter className=\"flex justify-between border-t p-4\">\r\n            <Button variant=\"outline\" size=\"sm\" onClick={() => setIsOpen(false)}>\r\n              Đóng\r\n            </Button>\r\n            {activeTab === 'summary' && (\r\n              <Button\r\n                size=\"sm\"\r\n                onClick={generateSummary}\r\n                disabled={isLoading}\r\n                className=\"gap-1\"\r\n              >\r\n                {isLoading && <Loader2 className=\"h-3 w-3 animate-spin\" />}\r\n                Phân tích ngay\r\n              </Button>\r\n            )}\r\n          </DialogFooter>\r\n        </DialogContent>\r\n      </Dialog>\r\n    </div>\r\n  );\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAEA;AACA;AAQA;AACA;AAjBA;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,MAAM,kBAAkB;QACtB,aAAa;QACb,IAAI;YACF,MAAM,QAAQ,MAAM,yIAAA,CAAA,cAAW,CAAC,QAAQ;YACxC,MAAM,cAAc,MAAM,uIAAA,CAAA,YAAS,CAAC,cAAc,CAAC;YACnD,WAAW;QACb,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,WAAW;QACb,SAAU;YACR,aAAa;QACf;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,oMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;;;;;;0BAGnB,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAM;gBAAQ,cAAc;0BAClC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;oBAAC,WAAU;;sCACvB,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAY;;;;;;;8CAG/B,8OAAC,kIAAA,CAAA,oBAAiB;8CAAC;;;;;;;;;;;;sCAGrB,8OAAC,gIAAA,CAAA,OAAI;4BAAC,OAAO;4BAAW,eAAe;4BAAc,WAAU;;8CAC7D,8OAAC,gIAAA,CAAA,WAAQ;oCAAC,WAAU;;sDAClB,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAU,WAAU;;8DACrC,8OAAC,4MAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAGnC,8OAAC,gIAAA,CAAA,cAAW;4CAAC,OAAM;4CAAO,WAAU;;8DAClC,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAY;;;;;;;;;;;;;8CAKzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAU,WAAU;8CACpC,0BACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;;;;;+CAEnB,wBACF,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,SAAS;4CAAS,WAAU;;;;;;;;;;6DAGxC,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;8CAMzC,8OAAC,gIAAA,CAAA,cAAW;oCAAC,OAAM;oCAAO,WAAU;8CAClC,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAE,WAAU;0DAAU;;;;;;0DACvB,8OAAC;gDAAE,WAAU;0DAAe;;;;;;;;;;;;;;;;;;;;;;;sCAKlC,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,SAAS,IAAM,UAAU;8CAAQ;;;;;;gCAGpE,cAAc,2BACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,UAAU;oCACV,WAAU;;wCAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3E", "debugId": null}}, {"offset": {"line": 3082, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\";\nimport { cn } from \"@/lib/utils\";\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\n      className\n    )}\n    {...props}\n  />\n));\nCard.displayName = \"Card\";\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-2xl font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent };\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AACA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3163, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,0KAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,8OAAC,0KAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,8OAAC;;;;;0BACD,8OAAC,0KAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,0KAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,8OAAC,0KAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,0KAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,0KAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 3231, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ai/ai-chatbox.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useRef, useEffect } from 'react';\r\nimport { AIService, ChatMessage } from '@/lib/services/ai-service';\r\n\r\nimport { Button } from '@/components/ui/button';\r\nimport { Input } from '@/components/ui/input';\r\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';\r\nimport { ScrollArea } from '@/components/ui/scroll-area';\r\nimport { MessageCircle, Send, Loader2, X, Minimize2, Maximize2 } from 'lucide-react';\r\nimport { cn } from '@/lib/utils';\r\nimport { Markdown } from '@/components/ui/markdown';\r\nimport { safeLocalStorageGet, safeLocalStorageSet, safeLocalStorageRemove, cleanupLocalStorage } from '@/lib/utils/json-utils';\r\n\r\ninterface AIChatboxProps {\r\n  className?: string;\r\n}\r\n\r\nconst CHAT_HISTORY_KEY = 'dr-aitime-chat-history';\r\nconst CHAT_SETTINGS_KEY = 'dr-aitime-chat-settings';\r\n\r\nexport function AIChatbox({ className }: AIChatboxProps) {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [isMinimized, setIsMinimized] = useState(false);\r\n  const [messages, setMessages] = useState<ChatMessage[]>([]);\r\n  const [inputMessage, setInputMessage] = useState('');\r\n  const [isLoading, setIsLoading] = useState(false);\r\n  const [streamingMessage, setStreamingMessage] = useState('');\r\n  const messagesEndRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n\r\n  // Load chat history from localStorage\r\n  const loadChatHistory = (): ChatMessage[] => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_HISTORY_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        const parsed = JSON.parse(saved);\r\n        // Convert timestamp strings back to Date objects\r\n        return parsed.map((msg: any) => ({\r\n          ...msg,\r\n          timestamp: new Date(msg.timestamp)\r\n        }));\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat history:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    }\r\n    return [];\r\n  };\r\n\r\n  // Save chat history to localStorage\r\n  const saveChatHistory = (newMessages: ChatMessage[]) => {\r\n    try {\r\n      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(newMessages));\r\n    } catch (error) {\r\n      console.error('Error saving chat history:', error);\r\n    }\r\n  };\r\n\r\n  // Load settings from localStorage\r\n  const loadChatSettings = () => {\r\n    try {\r\n      const saved = localStorage.getItem(CHAT_SETTINGS_KEY);\r\n      if (saved && saved !== 'undefined' && saved !== 'null') {\r\n        return JSON.parse(saved);\r\n      }\r\n    } catch (error) {\r\n      console.error('Error loading chat settings:', error);\r\n      // Clear invalid data\r\n      localStorage.removeItem(CHAT_SETTINGS_KEY);\r\n    }\r\n    return { isOpen: false, isMinimized: false };\r\n  };\r\n\r\n  // Initialize from localStorage\r\n  useEffect(() => {\r\n    // Clean up any invalid localStorage items first\r\n    cleanupLocalStorage();\r\n\r\n    const savedMessages = loadChatHistory();\r\n    const savedSettings = loadChatSettings();\r\n\r\n    setMessages(savedMessages);\r\n    setIsOpen(savedSettings.isOpen || false);\r\n    setIsMinimized(savedSettings.isMinimized || false);\r\n  }, []);\r\n\r\n  // Save settings whenever they change\r\n  useEffect(() => {\r\n    const settings = { isOpen, isMinimized };\r\n    try {\r\n      localStorage.setItem(CHAT_SETTINGS_KEY, JSON.stringify(settings));\r\n    } catch (error) {\r\n      console.error('Error saving chat settings:', error);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  // Save messages whenever they change\r\n  useEffect(() => {\r\n    if (messages.length > 0) {\r\n      saveChatHistory(messages);\r\n    }\r\n  }, [messages]);\r\n\r\n  // Auto scroll to bottom when new messages arrive\r\n  useEffect(() => {\r\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\r\n  }, [messages, streamingMessage]);\r\n\r\n  // Focus input when chat opens\r\n  useEffect(() => {\r\n    if (isOpen && !isMinimized) {\r\n      setTimeout(() => inputRef.current?.focus(), 100);\r\n    }\r\n  }, [isOpen, isMinimized]);\r\n\r\n  const handleSendMessage = async () => {\r\n    if (!inputMessage.trim() || isLoading) return;\r\n\r\n    const userMessage: ChatMessage = {\r\n      role: 'user',\r\n      content: inputMessage.trim(),\r\n      timestamp: new Date()\r\n    };\r\n\r\n    setMessages(prev => [...prev, userMessage]);\r\n    setInputMessage('');\r\n    setIsLoading(true);\r\n    setStreamingMessage('');\r\n\r\n    try {\r\n      // Use streaming chat\r\n      const stream = AIService.chatStream(userMessage.content, messages);\r\n      let fullResponse = '';\r\n\r\n      for await (const chunk of stream) {\r\n        fullResponse += chunk;\r\n        setStreamingMessage(fullResponse);\r\n      }\r\n\r\n      // Add complete response to messages\r\n      const assistantMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: fullResponse,\r\n        timestamp: new Date()\r\n      };\r\n\r\n      setMessages(prev => [...prev, assistantMessage]);\r\n      setStreamingMessage('');\r\n    } catch (error) {\r\n      console.error('Error sending message:', error);\r\n      const errorMessage: ChatMessage = {\r\n        role: 'assistant',\r\n        content: 'Xin lỗi, đã có lỗi xảy ra. Vui lòng thử lại sau.',\r\n        timestamp: new Date()\r\n      };\r\n      setMessages(prev => [...prev, errorMessage]);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleKeyPress = (e: React.KeyboardEvent) => {\r\n    if (e.key === 'Enter' && !e.shiftKey) {\r\n      e.preventDefault();\r\n      handleSendMessage();\r\n    }\r\n  };\r\n\r\n  const clearChat = () => {\r\n    setMessages([]);\r\n    setStreamingMessage('');\r\n    // Clear from localStorage\r\n    try {\r\n      localStorage.removeItem(CHAT_HISTORY_KEY);\r\n    } catch (error) {\r\n      console.error('Error clearing chat history:', error);\r\n    }\r\n  };\r\n\r\n  if (!isOpen) {\r\n    return (\r\n      <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n        <Button\r\n          onClick={() => setIsOpen(true)}\r\n          size=\"icon\"\r\n          className=\"h-12 w-12 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700\"\r\n        >\r\n          <MessageCircle className=\"h-6 w-6\" />\r\n        </Button>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className={cn(\"fixed bottom-4 left-4 z-50\", className)}>\r\n      <Card className={cn(\r\n        \"w-80 shadow-xl transition-all duration-200 relative overflow-hidden\",\r\n        isMinimized ? \"h-12\" : \"h-96\"\r\n      )}>\r\n        <CardHeader className=\"flex flex-row items-center justify-between p-3 bg-blue-600 text-white rounded-t-lg\">\r\n          <CardTitle className=\"text-sm font-medium flex items-center gap-2\">\r\n            <MessageCircle className=\"h-4 w-4\" />\r\n            Dr.AITime Chat\r\n            {messages.length > 0 && (\r\n              <span className=\"bg-blue-500 text-xs px-2 py-1 rounded-full\">\r\n                {messages.length}\r\n              </span>\r\n            )}\r\n          </CardTitle>\r\n          <div className=\"flex items-center gap-1\">\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsMinimized(!isMinimized)}\r\n            >\r\n              {isMinimized ? <Maximize2 className=\"h-3 w-3\" /> : <Minimize2 className=\"h-3 w-3\" />}\r\n            </Button>\r\n            <Button\r\n              variant=\"ghost\"\r\n              size=\"icon\"\r\n              className=\"h-6 w-6 text-white hover:bg-blue-700\"\r\n              onClick={() => setIsOpen(false)}\r\n            >\r\n              <X className=\"h-3 w-3\" />\r\n            </Button>\r\n          </div>\r\n        </CardHeader>\r\n\r\n        {!isMinimized && (\r\n          <CardContent className=\"p-0 flex flex-col h-80 relative\">\r\n            {/* Backdrop blur overlay chỉ trong khung chat */}\r\n            <div className=\"absolute inset-0 bg-background/70 backdrop-blur-sm z-10 pointer-events-none\" />\r\n            {/* Messages Area */}\r\n            <ScrollArea className=\"flex-1 p-3 relative z-20\">\r\n              <div className=\"space-y-3\">\r\n                {messages.length === 0 && (\r\n                  <div className=\"text-center text-muted-foreground text-sm py-8\">\r\n                    <MessageCircle className=\"h-8 w-8 mx-auto mb-2 opacity-50\" />\r\n                    <p className=\"font-medium\">Xin chào! Tôi là Dr.AITime 👋</p>\r\n                    <p className=\"mt-1\">Hãy hỏi tôi về quản lý thời gian nhé!</p>\r\n                    <div className=\"mt-3 text-xs opacity-70\">\r\n                      <p>💡 Lịch sử chat sẽ được lưu tự động</p>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                {messages.map((message, index) => (\r\n                  <div\r\n                    key={index}\r\n                    className={cn(\r\n                      \"flex\",\r\n                      message.role === 'user' ? \"justify-end\" : \"justify-start\"\r\n                    )}\r\n                  >\r\n                    <div\r\n                      className={cn(\r\n                        \"max-w-[80%] rounded-lg px-3 py-2 text-sm\",\r\n                        message.role === 'user'\r\n                          ? \"bg-blue-600 text-white\"\r\n                          : \"bg-muted\"\r\n                      )}\r\n                    >\r\n                      {message.role === 'user' ? (\r\n                        <p className=\"whitespace-pre-wrap text-white\">{message.content}</p>\r\n                      ) : (\r\n                        <Markdown content={message.content} className=\"text-foreground\" />\r\n                      )}\r\n                      <p className={cn(\r\n                        \"text-xs opacity-70 mt-1\",\r\n                        message.role === 'user' ? \"text-white\" : \"text-muted-foreground\"\r\n                      )}>\r\n                        {message.timestamp.toLocaleTimeString('vi-VN', {\r\n                          hour: '2-digit',\r\n                          minute: '2-digit'\r\n                        })}\r\n                      </p>\r\n                    </div>\r\n                  </div>\r\n                ))}\r\n\r\n                {/* Streaming message */}\r\n                {streamingMessage && (\r\n                  <div className=\"flex justify-start\">\r\n                    <div className=\"max-w-[80%] rounded-lg px-3 py-2 text-sm bg-muted\">\r\n                      <Markdown content={streamingMessage} className=\"text-foreground\" />\r\n                      <div className=\"flex items-center gap-1 mt-1\">\r\n                        <Loader2 className=\"h-3 w-3 animate-spin\" />\r\n                        <span className=\"text-xs opacity-70 text-muted-foreground\">Đang trả lời...</span>\r\n                      </div>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n\r\n                <div ref={messagesEndRef} />\r\n              </div>\r\n            </ScrollArea>\r\n\r\n            {/* Input Area */}\r\n            <div className=\"border-t p-3 relative z-20 bg-background\">\r\n              <div className=\"flex gap-2\">\r\n                <Input\r\n                  ref={inputRef}\r\n                  value={inputMessage}\r\n                  onChange={(e) => setInputMessage(e.target.value)}\r\n                  onKeyDown={handleKeyPress}\r\n                  placeholder=\"Nhập tin nhắn...\"\r\n                  disabled={isLoading}\r\n                  className=\"flex-1\"\r\n                />\r\n                <Button\r\n                  onClick={handleSendMessage}\r\n                  disabled={isLoading || !inputMessage.trim()}\r\n                  size=\"icon\"\r\n                  className=\"shrink-0\"\r\n                >\r\n                  {isLoading ? (\r\n                    <Loader2 className=\"h-4 w-4 animate-spin\" />\r\n                  ) : (\r\n                    <Send className=\"h-4 w-4\" />\r\n                  )}\r\n                </Button>\r\n              </div>\r\n              \r\n              {messages.length > 0 && (\r\n                <Button\r\n                  variant=\"ghost\"\r\n                  size=\"sm\"\r\n                  onClick={() => {\r\n                    if (window.confirm('Bạn có chắc chắn muốn xóa toàn bộ lịch sử chat? Hành động này không thể hoàn tác.')) {\r\n                      clearChat();\r\n                    }\r\n                  }}\r\n                  className=\"w-full mt-2 text-xs text-red-600 hover:text-red-700 hover:bg-red-50\"\r\n                >\r\n                  🗑️ Xóa lịch sử chat ({messages.length} tin nhắn)\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </CardContent>\r\n        )}\r\n      </Card>\r\n    </div>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;AAkBA,MAAM,mBAAmB;AACzB,MAAM,oBAAoB;AAEnB,SAAS,UAAU,EAAE,SAAS,EAAkB;IACrD,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC1D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC9C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sCAAsC;IACtC,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,MAAM,SAAS,KAAK,KAAK,CAAC;gBAC1B,iDAAiD;gBACjD,OAAO,OAAO,GAAG,CAAC,CAAC,MAAa,CAAC;wBAC/B,GAAG,GAAG;wBACN,WAAW,IAAI,KAAK,IAAI,SAAS;oBACnC,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;YAC7C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO,EAAE;IACX;IAEA,oCAAoC;IACpC,MAAM,kBAAkB,CAAC;QACvB,IAAI;YACF,aAAa,OAAO,CAAC,kBAAkB,KAAK,SAAS,CAAC;QACxD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAEA,kCAAkC;IAClC,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,SAAS,UAAU,eAAe,UAAU,QAAQ;gBACtD,OAAO,KAAK,KAAK,CAAC;YACpB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,qBAAqB;YACrB,aAAa,UAAU,CAAC;QAC1B;QACA,OAAO;YAAE,QAAQ;YAAO,aAAa;QAAM;IAC7C;IAEA,+BAA+B;IAC/B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,gDAAgD;QAChD,CAAA,GAAA,oIAAA,CAAA,sBAAmB,AAAD;QAElB,MAAM,gBAAgB;QACtB,MAAM,gBAAgB;QAEtB,YAAY;QACZ,UAAU,cAAc,MAAM,IAAI;QAClC,eAAe,cAAc,WAAW,IAAI;IAC9C,GAAG,EAAE;IAEL,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YAAE;YAAQ;QAAY;QACvC,IAAI;YACF,aAAa,OAAO,CAAC,mBAAmB,KAAK,SAAS,CAAC;QACzD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,qCAAqC;IACrC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,gBAAgB;QAClB;IACF,GAAG;QAAC;KAAS;IAEb,iDAAiD;IACjD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D,GAAG;QAAC;QAAU;KAAiB;IAE/B,8BAA8B;IAC9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU,CAAC,aAAa;YAC1B,WAAW,IAAM,SAAS,OAAO,EAAE,SAAS;QAC9C;IACF,GAAG;QAAC;QAAQ;KAAY;IAExB,MAAM,oBAAoB;QACxB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAA2B;YAC/B,MAAM;YACN,SAAS,aAAa,IAAI;YAC1B,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QACb,oBAAoB;QAEpB,IAAI;YACF,qBAAqB;YACrB,MAAM,SAAS,uIAAA,CAAA,YAAS,CAAC,UAAU,CAAC,YAAY,OAAO,EAAE;YACzD,IAAI,eAAe;YAEnB,WAAW,MAAM,SAAS,OAAQ;gBAChC,gBAAgB;gBAChB,oBAAoB;YACtB;YAEA,oCAAoC;YACpC,MAAM,mBAAgC;gBACpC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAiB;YAC/C,oBAAoB;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM,eAA4B;gBAChC,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,YAAY;QAChB,YAAY,EAAE;QACd,oBAAoB;QACpB,0BAA0B;QAC1B,IAAI;YACF,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD;IACF;IAEA,IAAI,CAAC,QAAQ;QACX,qBACE,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;sBAC/C,cAAA,8OAAC,kIAAA,CAAA,SAAM;gBACL,SAAS,IAAM,UAAU;gBACzB,MAAK;gBACL,WAAU;0BAEV,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;;;;;;;;;;;IAIjC;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;kBAC/C,cAAA,8OAAC,gIAAA,CAAA,OAAI;YAAC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAChB,uEACA,cAAc,SAAS;;8BAEvB,8OAAC,gIAAA,CAAA,aAAU;oBAAC,WAAU;;sCACpB,8OAAC,gIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,8OAAC,wNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;gCAAY;gCAEpC,SAAS,MAAM,GAAG,mBACjB,8OAAC;oCAAK,WAAU;8CACb,SAAS,MAAM;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,eAAe,CAAC;8CAE9B,4BAAc,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;6DAAe,8OAAC,gNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;8CAEzB,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;gBAKlB,CAAC,6BACA,8OAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCAErB,8OAAC;4BAAI,WAAU;;;;;;sCAEf,8OAAC,0IAAA,CAAA,aAAU;4BAAC,WAAU;sCACpB,cAAA,8OAAC;gCAAI,WAAU;;oCACZ,SAAS,MAAM,KAAK,mBACnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,wNAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;0DACzB,8OAAC;gDAAE,WAAU;0DAAc;;;;;;0DAC3B,8OAAC;gDAAE,WAAU;0DAAO;;;;;;0DACpB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC;8DAAE;;;;;;;;;;;;;;;;;oCAKR,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC;4CAEC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,QAAQ,IAAI,KAAK,SAAS,gBAAgB;sDAG5C,cAAA,8OAAC;gDACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4CACA,QAAQ,IAAI,KAAK,SACb,2BACA;;oDAGL,QAAQ,IAAI,KAAK,uBAChB,8OAAC;wDAAE,WAAU;kEAAkC,QAAQ,OAAO;;;;;6EAE9D,8OAAC,oIAAA,CAAA,WAAQ;wDAAC,SAAS,QAAQ,OAAO;wDAAE,WAAU;;;;;;kEAEhD,8OAAC;wDAAE,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,2BACA,QAAQ,IAAI,KAAK,SAAS,eAAe;kEAExC,QAAQ,SAAS,CAAC,kBAAkB,CAAC,SAAS;4DAC7C,MAAM;4DACN,QAAQ;wDACV;;;;;;;;;;;;2CA1BC;;;;;oCAiCR,kCACC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,SAAS;oDAAkB,WAAU;;;;;;8DAC/C,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,iNAAA,CAAA,UAAO;4DAAC,WAAU;;;;;;sEACnB,8OAAC;4DAAK,WAAU;sEAA2C;;;;;;;;;;;;;;;;;;;;;;;kDAMnE,8OAAC;wCAAI,KAAK;;;;;;;;;;;;;;;;;sCAKd,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,iIAAA,CAAA,QAAK;4CACJ,KAAK;4CACL,OAAO;4CACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;4CAC/C,WAAW;4CACX,aAAY;4CACZ,UAAU;4CACV,WAAU;;;;;;sDAEZ,8OAAC,kIAAA,CAAA,SAAM;4CACL,SAAS;4CACT,UAAU,aAAa,CAAC,aAAa,IAAI;4CACzC,MAAK;4CACL,WAAU;sDAET,0BACC,8OAAC,iNAAA,CAAA,UAAO;gDAAC,WAAU;;;;;qEAEnB,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;gCAKrB,SAAS,MAAM,GAAG,mBACjB,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,MAAK;oCACL,SAAS;wCACP,IAAI,OAAO,OAAO,CAAC,sFAAsF;4CACvG;wCACF;oCACF;oCACA,WAAU;;wCACX;wCACwB,SAAS,MAAM;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzD", "debugId": null}}, {"offset": {"line": 3811, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/layout/layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport React from \"react\";\nimport { Sidebar } from \"@/components/ui/sidebar\";\nimport SidebarMenu from \"@/components/ui/SidebarMenu\";\nimport Link from \"next/link\";\nimport { motion } from \"framer-motion\";\nimport { ThemeToggle } from \"@/components/theme/theme-toggle\";\nimport { ClearDataButton } from \"@/components/clear-data-button\";\nimport { Footerdemo } from \"@/components/ui/footer-section\";\nimport { useSidebar } from \"@/components/ui/sidebar\";\nimport { Header } from \"@/components/layout/header\";\nimport { AIAssistant } from '@/components/ai/ai-assistant';\nimport { AIChatbox } from '@/components/ai/ai-chatbox';\n\nexport function Layout({ children }: { children: React.ReactNode }) {\n  return (\n    <div className=\"flex h-screen flex-col md:flex-row\">\n      <Sidebar>\n        <div className=\"flex h-full flex-col justify-between\">\n          <div className=\"flex flex-col gap-2\">\n            <div className=\"flex items-center gap-2 px-2 py-2 md:block hidden\">\n              <Logo />\n            </div>\n            {/* Logo cho mobile sẽ hiển thị trong sidebar panel */}\n            <div className=\"flex items-center gap-2 px-2 py-2 md:hidden\">\n              <Logo />\n            </div>\n            <SidebarMenu />\n          </div>\n          <div className=\"flex flex-col gap-2 px-2\">\n            <ClearDataButton />\n          </div>\n        </div>\n      </Sidebar>\n      <div className=\"flex-1 overflow-auto\">\n        <div className=\"min-h-screen flex flex-col\">\n          <Header />\n          <main className=\"flex-grow px-4 py-6 md:px-8 lg:px-12\">\n            {children}\n          </main>\n          <Footerdemo />\n        </div>\n      </div>\n\n      <AIAssistant />\n      <AIChatbox />\n    </div>\n  );\n}\n\nconst Logo = () => {\n  const { open, animate } = useSidebar();\n\n  return (\n    <Link\n      href=\"/\"\n      className=\"font-normal flex space-x-2 items-center text-sm text-black dark:text-white py-1 relative z-20\"\n    >\n      <div className=\"h-5 w-6 bg-primary rounded-br-lg rounded-tr-sm rounded-tl-lg rounded-bl-sm flex-shrink-0\" />\n      <motion.span\n        initial={{ opacity: 0 }}\n        animate={{ \n          opacity: 1,\n          marginLeft: animate ? (open ? \"8px\" : \"40px\") : \"8px\",\n          display: animate ? \"inline-block\" : \"inline-block\"\n        }}\n        transition={{ duration: 0.3 }}\n        className=\"font-medium whitespace-pre text-xl\"\n      >\n        QLTime\n      </motion.span>\n    </Link>\n  );\n}; "], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AAEA;AACA;AAEA;AACA;AACA;AAbA;;;;;;;;;;;;AAeO,SAAS,OAAO,EAAE,QAAQ,EAAiC;IAChE,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,UAAO;0BACN,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;;;;;;;;;8CAGH,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;;;;;;;;;;8CAEH,8OAAC,uIAAA,CAAA,UAAW;;;;;;;;;;;sCAEd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,6IAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;;;;;;0BAItB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sIAAA,CAAA,SAAM;;;;;sCACP,8OAAC;4BAAK,WAAU;sCACb;;;;;;sCAEH,8OAAC,6IAAA,CAAA,aAAU;;;;;;;;;;;;;;;;0BAIf,8OAAC,2IAAA,CAAA,cAAW;;;;;0BACZ,8OAAC,yIAAA,CAAA,YAAS;;;;;;;;;;;AAGhB;AAEA,MAAM,OAAO;IACX,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,aAAU,AAAD;IAEnC,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAU;;0BAEV,8OAAC;gBAAI,WAAU;;;;;;0BACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBACP,SAAS;oBACT,YAAY,UAAW,OAAO,QAAQ,SAAU;oBAChD,SAAS,UAAU,iBAAiB;gBACtC;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 4001, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { Check } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Checkbox = React.forwardRef<\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\n>(({ className, ...props }, ref) => (\n  <CheckboxPrimitive.Root\n    ref={ref}\n    className={cn(\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\n      className,\n    )}\n    {...props}\n  >\n    <CheckboxPrimitive.Indicator\n      className={cn(\"flex items-center justify-center text-current\")}\n    >\n      <Check className=\"h-4 w-4\" />\n    </CheckboxPrimitive.Indicator>\n  </CheckboxPrimitive.Root>\n))\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4046, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/lib/services/category-service.ts"], "sourcesContent": ["import { Category } from '../types';\r\nimport { ApiService } from './api-service';\r\n\r\n// Tạo một lớp cache đơn giản để lưu trữ tạm thời\r\nlet categoriesCache: Category[] = [];\r\nlet lastFetchTime = 0;\r\nconst CACHE_DURATION = 60000; // 1 phút\r\n\r\n// Danh sách các danh mục mặc định\r\nconst DEFAULT_CATEGORIES: Category[] = [\r\n  { id: '1', name: '<PERSON><PERSON><PERSON> việ<PERSON>', color: '#ff4757' },\r\n  { id: '2', name: '<PERSON><PERSON> nhân', color: '#1e90ff' },\r\n  { id: '3', name: '<PERSON>ọ<PERSON> tập', color: '#ffa502' },\r\n  { id: '4', name: 'Sức khỏe', color: '#2ed573' },\r\n];\r\n\r\nexport const CategoryService = {\r\n  getCategories: async (): Promise<Category[]> => {\r\n    try {\r\n      console.log('CategoryService: Đang gọi API categories...');\r\n      const categories = await ApiService.categories.getAll();\r\n      console.log('CategoryService: Nhận được categories từ API:', categories);\r\n      categoriesCache = categories;\r\n      lastFetchTime = Date.now();\r\n      return categories;\r\n    } catch (error: any) {\r\n      console.error('CategoryService: Lỗi khi lấy danh sách danh mục:', error);\r\n\r\n      // Nếu lỗi 401 (Unauthorized), có nghĩa là chưa đăng nhập\r\n      if (error.message?.includes('401') || error.message?.includes('Unauthorized')) {\r\n        console.log('CategoryService: Chưa đăng nhập, sử dụng danh mục mặc định');\r\n        return DEFAULT_CATEGORIES;\r\n      }\r\n\r\n      // Nếu có lỗi khác, trả về cache hoặc danh mục mặc định\r\n      if (categoriesCache.length > 0) {\r\n        console.log('CategoryService: Sử dụng cache:', categoriesCache);\r\n        return categoriesCache;\r\n      }\r\n      console.log('CategoryService: Sử dụng danh mục mặc định:', DEFAULT_CATEGORIES);\r\n      return DEFAULT_CATEGORIES;\r\n    }\r\n  },\r\n  \r\n  getCategory: async (id: string): Promise<Category | undefined> => {\r\n    try {\r\n      console.log(`CategoryService: Đang tìm danh mục với ID: ${id}`);\r\n\r\n      // Kiểm tra trong danh sách mặc định trước\r\n      const defaultCategory = DEFAULT_CATEGORIES.find(category => category.id === id);\r\n      if (defaultCategory) {\r\n        console.log(`CategoryService: Tìm thấy danh mục mặc định:`, defaultCategory);\r\n        return defaultCategory;\r\n      }\r\n\r\n      // Nếu cache rỗng, thử load categories trước\r\n      if (categoriesCache.length === 0) {\r\n        console.log('CategoryService: Cache rỗng, đang load categories...');\r\n        try {\r\n          await CategoryService.getCategories();\r\n        } catch (error) {\r\n          console.warn('CategoryService: Không thể load categories:', error);\r\n        }\r\n      }\r\n\r\n      // Kiểm tra trong cache sau khi load\r\n      const cachedCategory = categoriesCache.find(category => category.id === id);\r\n      if (cachedCategory) {\r\n        console.log(`CategoryService: Tìm thấy trong cache:`, cachedCategory);\r\n        return cachedCategory;\r\n      }\r\n\r\n      // Chỉ gọi API nếu ID có vẻ như MongoDB ObjectId (24 ký tự hex)\r\n      if (id.length === 24 && /^[0-9a-fA-F]{24}$/.test(id)) {\r\n        console.log(`CategoryService: Gọi API cho ObjectId: ${id}`);\r\n        const apiCategory = await ApiService.categories.getById(id);\r\n        if (apiCategory) {\r\n          // Thêm vào cache\r\n          categoriesCache = [...categoriesCache, apiCategory];\r\n        }\r\n        return apiCategory;\r\n      }\r\n\r\n      // Nếu không phải ObjectId hợp lệ, trả về undefined\r\n      console.warn(`CategoryService: ID danh mục không hợp lệ: ${id}`);\r\n      return undefined;\r\n    } catch (error) {\r\n      console.error(`CategoryService: Lỗi khi lấy danh mục với id ${id}:`, error);\r\n      // Kiểm tra trong danh sách mặc định\r\n      return DEFAULT_CATEGORIES.find(category => category.id === id);\r\n    }\r\n  },\r\n  \r\n  createCategory: async (category: Omit<Category, 'id'>): Promise<Category> => {\r\n    try {\r\n      const newCategory = await ApiService.categories.create(category);\r\n      // Cập nhật cache\r\n      categoriesCache = [...categoriesCache, newCategory];\r\n      return newCategory;\r\n    } catch (error) {\r\n      console.error('Lỗi khi tạo danh mục mới:', error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  updateCategory: async (id: string, updates: Partial<Omit<Category, 'id'>>): Promise<Category> => {\r\n    try {\r\n      // Kiểm tra xem có phải là danh mục mặc định không\r\n      if (DEFAULT_CATEGORIES.some(c => c.id === id)) {\r\n        throw new Error('Không thể cập nhật danh mục mặc định');\r\n      }\r\n      \r\n      const updatedCategory = await ApiService.categories.update(id, updates);\r\n      // Cập nhật cache\r\n      const categoryIndex = categoriesCache.findIndex(category => category.id === id);\r\n      if (categoryIndex !== -1) {\r\n        categoriesCache[categoryIndex] = updatedCategory;\r\n      }\r\n      return updatedCategory;\r\n    } catch (error) {\r\n      console.error(`Lỗi khi cập nhật danh mục với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  deleteCategory: async (id: string): Promise<void> => {\r\n    try {\r\n      // Kiểm tra xem có phải là danh mục mặc định không\r\n      if (DEFAULT_CATEGORIES.some(c => c.id === id)) {\r\n        throw new Error('Không thể xóa danh mục mặc định');\r\n      }\r\n      \r\n      await ApiService.categories.delete(id);\r\n      // Cập nhật cache\r\n      categoriesCache = categoriesCache.filter(category => category.id !== id);\r\n    } catch (error) {\r\n      console.error(`Lỗi khi xóa danh mục với id ${id}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n  \r\n  // Phương thức để xóa cache khi cần thiết\r\n  clearCache: () => {\r\n    categoriesCache = [];\r\n    lastFetchTime = 0;\r\n  },\r\n  \r\n  // Phương thức để tải lại dữ liệu từ API\r\n  refreshCategories: async (): Promise<Category[]> => {\r\n    try {\r\n      const categories = await ApiService.categories.getAll();\r\n      categoriesCache = categories;\r\n      lastFetchTime = Date.now();\r\n      return categories;\r\n    } catch (error) {\r\n      console.error('Lỗi khi làm mới danh sách danh mục:', error);\r\n      return categoriesCache.length > 0 ? categoriesCache : DEFAULT_CATEGORIES;\r\n    }\r\n  }\r\n};"], "names": [], "mappings": ";;;AACA;;AAEA,iDAAiD;AACjD,IAAI,kBAA8B,EAAE;AACpC,IAAI,gBAAgB;AACpB,MAAM,iBAAiB,OAAO,SAAS;AAEvC,kCAAkC;AAClC,MAAM,qBAAiC;IACrC;QAAE,IAAI;QAAK,MAAM;QAAa,OAAO;IAAU;IAC/C;QAAE,IAAI;QAAK,MAAM;QAAW,OAAO;IAAU;IAC7C;QAAE,IAAI;QAAK,MAAM;QAAW,OAAO;IAAU;IAC7C;QAAE,IAAI;QAAK,MAAM;QAAY,OAAO;IAAU;CAC/C;AAEM,MAAM,kBAAkB;IAC7B,eAAe;QACb,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;YACrD,QAAQ,GAAG,CAAC,iDAAiD;YAC7D,kBAAkB;YAClB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oDAAoD;YAElE,yDAAyD;YACzD,IAAI,MAAM,OAAO,EAAE,SAAS,UAAU,MAAM,OAAO,EAAE,SAAS,iBAAiB;gBAC7E,QAAQ,GAAG,CAAC;gBACZ,OAAO;YACT;YAEA,uDAAuD;YACvD,IAAI,gBAAgB,MAAM,GAAG,GAAG;gBAC9B,QAAQ,GAAG,CAAC,mCAAmC;gBAC/C,OAAO;YACT;YACA,QAAQ,GAAG,CAAC,+CAA+C;YAC3D,OAAO;QACT;IACF;IAEA,aAAa,OAAO;QAClB,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,IAAI;YAE9D,0CAA0C;YAC1C,MAAM,kBAAkB,mBAAmB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;YAC5E,IAAI,iBAAiB;gBACnB,QAAQ,GAAG,CAAC,CAAC,4CAA4C,CAAC,EAAE;gBAC5D,OAAO;YACT;YAEA,4CAA4C;YAC5C,IAAI,gBAAgB,MAAM,KAAK,GAAG;gBAChC,QAAQ,GAAG,CAAC;gBACZ,IAAI;oBACF,MAAM,gBAAgB,aAAa;gBACrC,EAAE,OAAO,OAAO;oBACd,QAAQ,IAAI,CAAC,+CAA+C;gBAC9D;YACF;YAEA,oCAAoC;YACpC,MAAM,iBAAiB,gBAAgB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;YACxE,IAAI,gBAAgB;gBAClB,QAAQ,GAAG,CAAC,CAAC,sCAAsC,CAAC,EAAE;gBACtD,OAAO;YACT;YAEA,+DAA+D;YAC/D,IAAI,GAAG,MAAM,KAAK,MAAM,oBAAoB,IAAI,CAAC,KAAK;gBACpD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,IAAI;gBAC1D,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,OAAO,CAAC;gBACxD,IAAI,aAAa;oBACf,iBAAiB;oBACjB,kBAAkB;2BAAI;wBAAiB;qBAAY;gBACrD;gBACA,OAAO;YACT;YAEA,mDAAmD;YACnD,QAAQ,IAAI,CAAC,CAAC,2CAA2C,EAAE,IAAI;YAC/D,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,6CAA6C,EAAE,GAAG,CAAC,CAAC,EAAE;YACrE,oCAAoC;YACpC,OAAO,mBAAmB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QAC7D;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,MAAM,cAAc,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;YACvD,iBAAiB;YACjB,kBAAkB;mBAAI;gBAAiB;aAAY;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO,IAAY;QACjC,IAAI;YACF,kDAAkD;YAClD,IAAI,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK;gBAC7C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,kBAAkB,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI;YAC/D,iBAAiB;YACjB,MAAM,gBAAgB,gBAAgB,SAAS,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;YAC5E,IAAI,kBAAkB,CAAC,GAAG;gBACxB,eAAe,CAAC,cAAc,GAAG;YACnC;YACA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,GAAG,CAAC,CAAC,EAAE;YACzD,MAAM;QACR;IACF;IAEA,gBAAgB,OAAO;QACrB,IAAI;YACF,kDAAkD;YAClD,IAAI,mBAAmB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,KAAK;gBAC7C,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM,CAAC;YACnC,iBAAiB;YACjB,kBAAkB,gBAAgB,MAAM,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK;QACvE,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,GAAG,CAAC,CAAC,EAAE;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,YAAY;QACV,kBAAkB,EAAE;QACpB,gBAAgB;IAClB;IAEA,wCAAwC;IACxC,mBAAmB;QACjB,IAAI;YACF,MAAM,aAAa,MAAM,wIAAA,CAAA,aAAU,CAAC,UAAU,CAAC,MAAM;YACrD,kBAAkB;YAClB,gBAAgB,KAAK,GAAG;YACxB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;YACrD,OAAO,gBAAgB,MAAM,GAAG,IAAI,kBAAkB;QACxD;IACF;AACF", "debugId": null}}, {"offset": {"line": 4219, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  ({ className, ...props }, ref) => {\n    return (\n      <textarea\n        className={cn(\n          \"flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50\",\n          className\n        )}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nTextarea.displayName = \"Textarea\"\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAKA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACxB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wSACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4247, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check, ChevronDown, ChevronUp } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Select = SelectPrimitive.Root\n\nconst SelectGroup = SelectPrimitive.Group\n\nconst SelectValue = SelectPrimitive.Value\n\nconst SelectTrigger = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Trigger>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Trigger>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Trigger\n    ref={ref}\n    className={cn(\n      \"flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1\",\n      className,\n    )}\n    {...props}\n  >\n    {children}\n    <SelectPrimitive.Icon asChild>\n      <ChevronDown className=\"h-4 w-4 opacity-50\" />\n    </SelectPrimitive.Icon>\n  </SelectPrimitive.Trigger>\n))\nSelectTrigger.displayName = SelectPrimitive.Trigger.displayName\n\nconst SelectScrollUpButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollUpButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollUpButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollUpButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronUp className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollUpButton>\n))\nSelectScrollUpButton.displayName = SelectPrimitive.ScrollUpButton.displayName\n\nconst SelectScrollDownButton = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.ScrollDownButton>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.ScrollDownButton>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.ScrollDownButton\n    ref={ref}\n    className={cn(\n      \"flex cursor-default items-center justify-center py-1\",\n      className,\n    )}\n    {...props}\n  >\n    <ChevronDown className=\"h-4 w-4\" />\n  </SelectPrimitive.ScrollDownButton>\n))\nSelectScrollDownButton.displayName =\n  SelectPrimitive.ScrollDownButton.displayName\n\nconst SelectContent = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Content>\n>(({ className, children, position = \"popper\", ...props }, ref) => (\n  <SelectPrimitive.Portal>\n    <SelectPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"relative z-[999] max-h-96 min-w-[8rem] overflow-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        position === \"popper\" &&\n          \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n        className,\n      )}\n      position={position}\n      {...props}\n    >\n      <SelectScrollUpButton />\n      <SelectPrimitive.Viewport\n        className={cn(\n          \"p-1\",\n          position === \"popper\" &&\n            \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]\",\n        )}\n      >\n        {children}\n      </SelectPrimitive.Viewport>\n      <SelectScrollDownButton />\n    </SelectPrimitive.Content>\n  </SelectPrimitive.Portal>\n))\nSelectContent.displayName = SelectPrimitive.Content.displayName\n\nconst SelectLabel = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Label>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Label>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Label\n    ref={ref}\n    className={cn(\"py-1.5 pl-8 pr-2 text-sm font-semibold\", className)}\n    {...props}\n  />\n))\nSelectLabel.displayName = SelectPrimitive.Label.displayName\n\nconst SelectItem = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Item>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Item>\n>(({ className, children, ...props }, ref) => (\n  <SelectPrimitive.Item\n    ref={ref}\n    className={cn(\n      \"relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50\",\n      className,\n    )}\n    {...props}\n  >\n    <span className=\"absolute left-2 flex h-3.5 w-3.5 items-center justify-center\">\n      <SelectPrimitive.ItemIndicator>\n        <Check className=\"h-4 w-4\" />\n      </SelectPrimitive.ItemIndicator>\n    </span>\n\n    <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n  </SelectPrimitive.Item>\n))\nSelectItem.displayName = SelectPrimitive.Item.displayName\n\nconst SelectSeparator = React.forwardRef<\n  React.ElementRef<typeof SelectPrimitive.Separator>,\n  React.ComponentPropsWithoutRef<typeof SelectPrimitive.Separator>\n>(({ className, ...props }, ref) => (\n  <SelectPrimitive.Separator\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nSelectSeparator.displayName = SelectPrimitive.Separator.displayName\n\nexport {\n  Select,\n  SelectGroup,\n  SelectValue,\n  SelectTrigger,\n  SelectContent,\n  SelectLabel,\n  SelectItem,\n  SelectSeparator,\n  SelectScrollUpButton,\n  SelectScrollDownButton,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAEA;AACA;AACA;AAAA;AAAA;AAEA;AANA;;;;;;AAQA,MAAM,SAAS,kKAAA,CAAA,OAAoB;AAEnC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,cAAc,kKAAA,CAAA,QAAqB;AAEzC,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,UAAuB;QACtB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mTACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,oNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI7B,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,qCAAuB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,gNAAA,CAAA,YAAS;YAAC,WAAU;;;;;;;;;;;AAGzB,qBAAqB,WAAW,GAAG,kKAAA,CAAA,iBAA8B,CAAC,WAAW;AAE7E,MAAM,uCAAyB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,cAAW;YAAC,WAAU;;;;;;;;;;;AAG3B,uBAAuB,WAAW,GAChC,kKAAA,CAAA,mBAAgC,CAAC,WAAW;AAE9C,MAAM,8BAAgB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGnC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,WAAW,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACzD,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,KAAK;YACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0cACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIP,cAAc,WAAW,GAAG,kKAAA,CAAA,UAAuB,CAAC,WAAW;AAE/D,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,QAAqB;QACpB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,0CAA0C;QACvD,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG,kKAAA,CAAA,QAAqB,CAAC,WAAW;AAE3D,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,8OAAC,kKAAA,CAAA,OAAoB;QACnB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6NACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,oMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAIrB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,kKAAA,CAAA,OAAoB,CAAC,WAAW;AAEzD,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,YAAyB;QACxB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG,kKAAA,CAAA,YAAyB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4439, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/popover.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as PopoverPrimitive from \"@radix-ui/react-popover\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Popover = PopoverPrimitive.Root\n\nconst PopoverTrigger = PopoverPrimitive.Trigger\n\nconst PopoverContent = React.forwardRef<\n  React.ElementRef<typeof PopoverPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof PopoverPrimitive.Content>\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => (\n  <PopoverPrimitive.Portal>\n    <PopoverPrimitive.Content\n      ref={ref}\n      align={align}\n      sideOffset={sideOffset}\n      className={cn(\n        \"z-[999] w-72 rounded-md border bg-popover/90 backdrop-blur-sm p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2\",\n        className\n      )}\n      {...props}\n    />\n  </PopoverPrimitive.Portal>\n))\nPopoverContent.displayName = PopoverPrimitive.Content.displayName\n\nexport { Popover, PopoverTrigger, PopoverContent } "], "names": [], "mappings": ";;;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,UAAU,mKAAA,CAAA,OAAqB;AAErC,MAAM,iBAAiB,mKAAA,CAAA,UAAwB;AAE/C,MAAM,+BAAiB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGpC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE,oBAC5D,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,KAAK;YACL,OAAO;YACP,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qcACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIf,eAAe,WAAW,GAAG,mKAAA,CAAA,UAAwB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4480, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/calendar.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport { ChevronLeft, ChevronRight } from \"lucide-react\"\r\nimport { DayPicker } from \"react-day-picker\"\r\nimport { vi } from \"date-fns/locale\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { buttonVariants } from \"@/components/ui/button\"\r\n\r\nexport type CalendarProps = React.ComponentProps<typeof DayPicker>\r\n\r\nfunction Calendar({\r\n  className,\r\n  classNames,\r\n  showOutsideDays = true,\r\n  ...props\r\n}: CalendarProps) {\r\n  return (\r\n    <DayPicker\r\n      locale={vi}\r\n      showOutsideDays={showOutsideDays}\r\n      className={cn(\"p-3\", className)}\r\n      classNames={{\r\n        months: \"flex flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0\",\r\n        month: \"space-y-4\",\r\n        caption: \"flex justify-center pt-1 relative items-center\",\r\n        caption_label: \"text-sm font-medium\",\r\n        nav: \"space-x-1 flex items-center\",\r\n        nav_button: cn(\r\n          buttonVariants({ variant: \"outline\" }),\r\n          \"h-7 w-7 bg-transparent p-0 opacity-50 hover:opacity-100\"\r\n        ),\r\n        nav_button_previous: \"absolute left-1\",\r\n        nav_button_next: \"absolute right-1\",\r\n        table: \"w-full border-collapse space-y-1\",\r\n        head_row: \"flex\",\r\n        head_cell:\r\n          \"text-muted-foreground rounded-md w-9 font-normal text-[0.8rem]\",\r\n        row: \"flex w-full mt-2\",\r\n        cell: \"h-9 w-9 text-center text-sm p-0 relative [&:has([aria-selected].day-range-end)]:rounded-r-md [&:has([aria-selected].day-outside)]:bg-accent/50 [&:has([aria-selected])]:bg-accent first:[&:has([aria-selected])]:rounded-l-md last:[&:has([aria-selected])]:rounded-r-md focus-within:relative focus-within:z-20\",\r\n        day: cn(\r\n          buttonVariants({ variant: \"ghost\" }),\r\n          \"h-9 w-9 p-0 font-normal aria-selected:opacity-100\"\r\n        ),\r\n        day_range_end: \"day-range-end\",\r\n        day_selected:\r\n          \"bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground focus:bg-primary focus:text-primary-foreground\",\r\n        day_today: \"bg-accent text-accent-foreground\",\r\n        day_outside:\r\n          \"day-outside text-muted-foreground opacity-50 aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30\",\r\n        day_disabled: \"text-muted-foreground opacity-50\",\r\n        day_range_middle:\r\n          \"aria-selected:bg-accent aria-selected:text-accent-foreground\",\r\n        day_hidden: \"invisible\",\r\n        ...classNames,\r\n      }}\r\n      components={{\r\n        Chevron: ({ orientation, ...props }) => {\r\n          if (orientation === 'left') {\r\n            return <ChevronLeft className=\"h-4 w-4\" {...props} />;\r\n          }\r\n          return <ChevronRight className=\"h-4 w-4\" {...props} />;\r\n        }\r\n      }}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\nCalendar.displayName = \"Calendar\"\r\n\r\nexport { Calendar } "], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AAEA;AACA;AARA;;;;;;;AAYA,SAAS,SAAS,EAChB,SAAS,EACT,UAAU,EACV,kBAAkB,IAAI,EACtB,GAAG,OACW;IACd,qBACE,8OAAC,kKAAA,CAAA,YAAS;QACR,QAAQ,2IAAA,CAAA,KAAE;QACV,iBAAiB;QACjB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;QACrB,YAAY;YACV,QAAQ;YACR,OAAO;YACP,SAAS;YACT,eAAe;YACf,KAAK;YACL,YAAY,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACX,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAU,IACpC;YAEF,qBAAqB;YACrB,iBAAiB;YACjB,OAAO;YACP,UAAU;YACV,WACE;YACF,KAAK;YACL,MAAM;YACN,KAAK,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACJ,CAAA,GAAA,kIAAA,CAAA,iBAAc,AAAD,EAAE;gBAAE,SAAS;YAAQ,IAClC;YAEF,eAAe;YACf,cACE;YACF,WAAW;YACX,aACE;YACF,cAAc;YACd,kBACE;YACF,YAAY;YACZ,GAAG,UAAU;QACf;QACA,YAAY;YACV,SAAS,CAAC,EAAE,WAAW,EAAE,GAAG,OAAO;gBACjC,IAAI,gBAAgB,QAAQ;oBAC1B,qBAAO,8OAAC,oNAAA,CAAA,cAAW;wBAAC,WAAU;wBAAW,GAAG,KAAK;;;;;;gBACnD;gBACA,qBAAO,8OAAC,sNAAA,CAAA,eAAY;oBAAC,WAAU;oBAAW,GAAG,KAAK;;;;;;YACpD;QACF;QACC,GAAG,KAAK;;;;;;AAGf;AACA,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4567, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/skeleton.tsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\"\n\nfunction Skeleton({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) {\n  return (\n    <div\n      className={cn(\"animate-pulse rounded-md bg-muted\", className)}\n      {...props}\n    />\n  )\n}\n\nexport { Skeleton }\n"], "names": [], "mappings": ";;;;AAAA;;;AAEA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACkC;IACrC,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,qCAAqC;QAClD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4591, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/task/task-form.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Task, Category } from '@/lib/types';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Textarea } from '@/components/ui/textarea';\nimport { \n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue\n} from '@/components/ui/select';\nimport { TaskService } from '@/lib/services/task-service';\nimport { CategoryService } from '@/lib/services/category-service';\nimport {\n  Dialog,\n  DialogContent,\n  DialogDescription,\n  DialogHeader,\n  DialogTitle,\n  DialogFooter\n} from '@/components/ui/dialog';\nimport { AlertCircle, CalendarIcon, Lightbulb, Loader2, RefreshCw } from 'lucide-react';\nimport { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';\nimport { Calendar } from '@/components/ui/calendar';\nimport { format } from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport { cn } from '@/lib/utils';\nimport { AIService } from '@/lib/services/ai-service';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { useStatisticsRefresh } from '@/lib/contexts/statistics-context';\n\ninterface TaskFormProps {\n  isOpen: boolean;\n  onClose: () => void;\n  onAdded?: (task?: Task) => void;\n  task?: Task;\n}\n\nexport function TaskForm({ isOpen, onClose, onAdded, task }: TaskFormProps) {\n  const refreshStatistics = useStatisticsRefresh();\n  const [isLoading, setIsLoading] = useState(false);\n  const [isAiLoading, setIsAiLoading] = useState(false);\n  const [isCategoriesLoading, setIsCategoriesLoading] = useState(true);\n  const [categoriesError, setCategoriesError] = useState<string | null>(null);\n  const [saveError, setSaveError] = useState<string | null>(null);\n  const [title, setTitle] = useState(task?.title || '');\n  const [description, setDescription] = useState(task?.description || '');\n  const [dueDate, setDueDate] = useState<Date | undefined>(task?.dueDate ? new Date(task.dueDate) : undefined);\n  const [priority, setPriority] = useState<'high' | 'medium' | 'low'>(task?.priority || 'medium');\n  const [category, setCategory] = useState<string | undefined>(task?.category);\n  const [categories, setCategories] = useState<Category[]>([]);\n  \n  // Tải danh sách danh mục khi component được mount\n  useEffect(() => {\n    const fetchCategories = async () => {\n      setIsCategoriesLoading(true);\n      setCategoriesError(null);\n      try {\n        const fetchedCategories = await CategoryService.getCategories();\n        console.log('TaskForm: Đã tải categories:', fetchedCategories);\n\n        // Kiểm tra duplicate IDs\n        const ids = fetchedCategories.map(cat => cat.id);\n        const uniqueIds = [...new Set(ids)];\n        if (ids.length !== uniqueIds.length) {\n          console.warn('TaskForm: Phát hiện duplicate category IDs:', ids);\n          console.warn('TaskForm: Categories chi tiết:', fetchedCategories);\n        } else {\n          console.log('TaskForm: Không có duplicate IDs:', ids);\n        }\n\n        setCategories(fetchedCategories);\n\n        // Nếu chỉ có danh mục mặc định, hiển thị thông báo\n        if (fetchedCategories.length === 4 && fetchedCategories[0].id === '1') {\n          setCategoriesError('Đang sử dụng danh mục mặc định. Vào trang \"Danh mục\" để tạo danh mục tùy chỉnh.');\n        }\n      } catch (error) {\n        console.error('TaskForm: Lỗi khi tải danh mục:', error);\n        setCategoriesError('Không thể tải danh sách danh mục. Vui lòng thử lại.');\n      } finally {\n        setIsCategoriesLoading(false);\n      }\n    };\n\n    if (isOpen) {\n      fetchCategories();\n    }\n  }, [isOpen]);\n  \n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setIsLoading(true);\n    setSaveError(null);\n    \n    try {\n      const taskData = {\n        title,\n        description,\n        dueDate: dueDate ? dueDate.toISOString() : undefined,\n        priority,\n        category: category === 'none' ? undefined : category,\n        completed: task?.completed || false,\n      };\n      \n      let updatedTask: Task;\n      \n      if (task) {\n        updatedTask = await TaskService.updateTask(task.id, taskData);\n      } else {\n        updatedTask = await TaskService.createTask(taskData);\n      }\n      \n      if (onAdded) onAdded(updatedTask);\n\n      // Trigger statistics refresh\n      refreshStatistics();\n\n      onClose();\n    } catch (error) {\n      console.error('Lỗi khi lưu task:', error);\n      setSaveError('Không thể lưu công việc. Vui lòng thử lại sau.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  \n  // Hàm xử lý gợi ý từ AI\n  const handleAiSuggestions = async () => {\n    if (!title) {\n      alert(\"Vui lòng nhập tiêu đề công việc trước\");\n      return;\n    }\n    \n    setIsAiLoading(true);\n    try {\n      // Gợi ý độ ưu tiên\n      const suggestedPriority = await AIService.suggestPriority(title, description);\n      setPriority(suggestedPriority);\n      \n      // Gợi ý ngày hoàn thành nếu chưa chọn\n      if (!dueDate) {\n        const suggestedDate = await AIService.suggestDueDate(title, description);\n        if (suggestedDate) {\n          setDueDate(new Date(suggestedDate));\n        }\n      }\n    } catch (error) {\n      console.error(\"Error getting AI suggestions:\", error);\n    } finally {\n      setIsAiLoading(false);\n    }\n  };\n\n  // Hàm tải lại danh mục\n  const handleRefreshCategories = async () => {\n    setIsCategoriesLoading(true);\n    setCategoriesError(null);\n    try {\n      const refreshedCategories = await CategoryService.refreshCategories();\n      setCategories(refreshedCategories);\n    } catch (error) {\n      console.error('Lỗi khi tải lại danh mục:', error);\n      setCategoriesError('Không thể tải danh sách danh mục. Vui lòng thử lại.');\n    } finally {\n      setIsCategoriesLoading(false);\n    }\n  };\n  \n  return (\n    <Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>\n      <DialogContent className=\"sm:max-w-[500px] backdrop-blur-sm border border-border/40 bg-background/90\">\n        <DialogHeader>\n          <DialogTitle>{task ? 'Chỉnh sửa công việc' : 'Thêm công việc mới'}</DialogTitle>\n          <DialogDescription>\n            {task ? 'Cập nhật thông tin công việc của bạn' : 'Tạo công việc mới để quản lý thời gian hiệu quả'}\n          </DialogDescription>\n        </DialogHeader>\n        \n        {saveError && (\n          <Alert variant=\"destructive\" className=\"mb-4\">\n            <AlertCircle className=\"h-4 w-4\" />\n            <AlertDescription>{saveError}</AlertDescription>\n          </Alert>\n        )}\n        \n        <form onSubmit={handleSubmit} className=\"space-y-4\">\n          <div>\n            <label htmlFor=\"title\" className=\"block text-sm font-medium mb-1\">\n              Tiêu đề <span className=\"text-red-500\">*</span>\n            </label>\n            <Input\n              id=\"title\"\n              value={title}\n              onChange={(e) => setTitle(e.target.value)}\n              placeholder=\"Nhập tiêu đề công việc\"\n              className=\"bg-background/80\"\n              required\n            />\n          </div>\n          \n          <div>\n            <label htmlFor=\"description\" className=\"block text-sm font-medium mb-1\">\n              Mô tả\n            </label>\n            <Textarea\n              id=\"description\"\n              value={description}\n              onChange={(e) => setDescription(e.target.value)}\n              placeholder=\"Nhập mô tả chi tiết (không bắt buộc)\"\n              className=\"resize-none h-20 bg-background/80\"\n            />\n          </div>\n          \n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label htmlFor=\"dueDate\" className=\"block text-sm font-medium mb-1\">\n                Hạn hoàn thành\n              </label>\n              <div className=\"relative\">\n                <Popover>\n                  <PopoverTrigger asChild>\n                    <Button\n                      variant=\"outline\"\n                      className={cn(\n                        \"w-full justify-start text-left font-normal bg-background/80\",\n                        !dueDate && \"text-muted-foreground\"\n                      )}\n                    >\n                      <CalendarIcon className=\"mr-2 h-4 w-4\" />\n                      {dueDate ? format(dueDate, 'PPP', { locale: vi }) : <span>Chọn ngày</span>}\n                    </Button>\n                  </PopoverTrigger>\n                  <PopoverContent className=\"w-auto p-0 backdrop-blur-sm border border-border/40 bg-background/90\">\n                    <Calendar\n                      mode=\"single\"\n                      selected={dueDate}\n                      onSelect={setDueDate}\n                      initialFocus\n                    />\n                  </PopoverContent>\n                </Popover>\n              </div>\n            </div>\n            \n            <div>\n              <label htmlFor=\"priority\" className=\"block text-sm font-medium mb-1\">\n                Mức độ ưu tiên\n              </label>\n              <Select value={priority} onValueChange={(value) => setPriority(value as 'high' | 'medium' | 'low')}>\n                <SelectTrigger className=\"bg-background/80\">\n                  <SelectValue placeholder=\"Chọn mức độ ưu tiên\" />\n                </SelectTrigger>\n                <SelectContent className=\"backdrop-blur-sm border border-border/40 bg-background/90\">\n                  <SelectItem key=\"priority-high\" value=\"high\">\n                    Cao\n                  </SelectItem>\n                  <SelectItem key=\"priority-medium\" value=\"medium\">\n                    Trung bình\n                  </SelectItem>\n                  <SelectItem key=\"priority-low\" value=\"low\">\n                    Thấp\n                  </SelectItem>\n                </SelectContent>\n              </Select>\n            </div>\n          </div>\n          \n          <div>\n            <label htmlFor=\"category\" className=\"block text-sm font-medium mb-1 flex justify-between\">\n              <span>Danh mục</span>\n              {isCategoriesLoading ? (\n                <span className=\"text-xs text-muted-foreground\">Đang tải...</span>\n              ) : categoriesError ? (\n                <Button \n                  type=\"button\" \n                  variant=\"ghost\" \n                  size=\"sm\" \n                  onClick={handleRefreshCategories} \n                  className=\"h-5 text-xs gap-1 text-muted-foreground hover:text-primary\"\n                >\n                  <RefreshCw className=\"h-3 w-3\" /> Tải lại\n                </Button>\n              ) : null}\n            </label>\n            \n            {categoriesError ? (\n              <Alert variant=\"destructive\" className=\"py-2\">\n                <AlertCircle className=\"h-4 w-4\" />\n                <AlertDescription className=\"text-xs\">{categoriesError}</AlertDescription>\n              </Alert>\n            ) : isCategoriesLoading ? (\n              <Skeleton className=\"h-10 w-full\" />\n            ) : (\n              <Select value={category || 'none'} onValueChange={setCategory}>\n                <SelectTrigger className=\"bg-background/80\">\n                  <SelectValue placeholder=\"Chọn danh mục\" />\n                </SelectTrigger>\n                <SelectContent className=\"backdrop-blur-sm border border-border/40 bg-background/90\">\n                  <SelectItem key=\"category-none\" value=\"none\">\n                    Không có danh mục\n                  </SelectItem>\n                  {categories.map((cat, index) => {\n                    const uniqueKey = `category-${cat.id}-${index}-${Date.now()}`;\n                    return (\n                      <SelectItem\n                        key={uniqueKey}\n                        value={cat.id}\n                      >\n                        {cat.name}\n                      </SelectItem>\n                    );\n                  })}\n                </SelectContent>\n              </Select>\n            )}\n          </div>\n          \n          <div className=\"flex justify-end mb-2\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleAiSuggestions}\n              disabled={isAiLoading || !title.trim()}\n              className=\"gap-1\"\n            >\n              {isAiLoading ? <Loader2 className=\"h-3 w-3 animate-spin\" /> : <Lightbulb className=\"h-3 w-3\" />}\n              Gợi ý từ AI\n            </Button>\n          </div>\n          \n          <DialogFooter className=\"mt-6\">\n            <Button \n              type=\"button\" \n              variant=\"outline\" \n              onClick={onClose}\n              disabled={isLoading}\n            >\n              Hủy\n            </Button>\n            <Button \n              type=\"submit\"\n              disabled={isLoading || !title.trim()}\n              className=\"gap-1\"\n            >\n              {isLoading && <Loader2 className=\"h-4 w-4 animate-spin\" />}\n              {isLoading ? 'Đang lưu...' : task ? 'Cập nhật' : 'Thêm mới'}\n            </Button>\n          </DialogFooter>\n        </form>\n      </DialogContent>\n    </Dialog>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AAOA;AACA;AACA;AAQA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAjCA;;;;;;;;;;;;;;;;;;;;AA0CO,SAAS,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAiB;IACxE,MAAM,oBAAoB,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAC1D,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,SAAS;IAClD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,eAAe;IACpE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB,MAAM,UAAU,IAAI,KAAK,KAAK,OAAO,IAAI;IAClG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA6B,MAAM,YAAY;IACtF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAsB,MAAM;IACnE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IAE3D,kDAAkD;IAClD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,uBAAuB;YACvB,mBAAmB;YACnB,IAAI;gBACF,MAAM,oBAAoB,MAAM,6IAAA,CAAA,kBAAe,CAAC,aAAa;gBAC7D,QAAQ,GAAG,CAAC,gCAAgC;gBAE5C,yBAAyB;gBACzB,MAAM,MAAM,kBAAkB,GAAG,CAAC,CAAA,MAAO,IAAI,EAAE;gBAC/C,MAAM,YAAY;uBAAI,IAAI,IAAI;iBAAK;gBACnC,IAAI,IAAI,MAAM,KAAK,UAAU,MAAM,EAAE;oBACnC,QAAQ,IAAI,CAAC,+CAA+C;oBAC5D,QAAQ,IAAI,CAAC,kCAAkC;gBACjD,OAAO;oBACL,QAAQ,GAAG,CAAC,qCAAqC;gBACnD;gBAEA,cAAc;gBAEd,mDAAmD;gBACnD,IAAI,kBAAkB,MAAM,KAAK,KAAK,iBAAiB,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK;oBACrE,mBAAmB;gBACrB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,mBAAmB;YACrB,SAAU;gBACR,uBAAuB;YACzB;QACF;QAEA,IAAI,QAAQ;YACV;QACF;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,aAAa;QACb,aAAa;QAEb,IAAI;YACF,MAAM,WAAW;gBACf;gBACA;gBACA,SAAS,UAAU,QAAQ,WAAW,KAAK;gBAC3C;gBACA,UAAU,aAAa,SAAS,YAAY;gBAC5C,WAAW,MAAM,aAAa;YAChC;YAEA,IAAI;YAEJ,IAAI,MAAM;gBACR,cAAc,MAAM,yIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,EAAE,EAAE;YACtD,OAAO;gBACL,cAAc,MAAM,yIAAA,CAAA,cAAW,CAAC,UAAU,CAAC;YAC7C;YAEA,IAAI,SAAS,QAAQ;YAErB,6BAA6B;YAC7B;YAEA;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;YACnC,aAAa;QACf,SAAU;YACR,aAAa;QACf;IACF;IAEA,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B,IAAI,CAAC,OAAO;YACV,MAAM;YACN;QACF;QAEA,eAAe;QACf,IAAI;YACF,mBAAmB;YACnB,MAAM,oBAAoB,MAAM,uIAAA,CAAA,YAAS,CAAC,eAAe,CAAC,OAAO;YACjE,YAAY;YAEZ,sCAAsC;YACtC,IAAI,CAAC,SAAS;gBACZ,MAAM,gBAAgB,MAAM,uIAAA,CAAA,YAAS,CAAC,cAAc,CAAC,OAAO;gBAC5D,IAAI,eAAe;oBACjB,WAAW,IAAI,KAAK;gBACtB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,uBAAuB;IACvB,MAAM,0BAA0B;QAC9B,uBAAuB;QACvB,mBAAmB;QACnB,IAAI;YACF,MAAM,sBAAsB,MAAM,6IAAA,CAAA,kBAAe,CAAC,iBAAiB;YACnE,cAAc;QAChB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,mBAAmB;QACrB,SAAU;YACR,uBAAuB;QACzB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAQ,cAAc,CAAC,OAAS,CAAC,QAAQ;kBACrD,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YAAC,WAAU;;8BACvB,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;sCAAE,OAAO,wBAAwB;;;;;;sCAC7C,8OAAC,kIAAA,CAAA,oBAAiB;sCACf,OAAO,yCAAyC;;;;;;;;;;;;gBAIpD,2BACC,8OAAC,iIAAA,CAAA,QAAK;oBAAC,SAAQ;oBAAc,WAAU;;sCACrC,8OAAC,oNAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;sCACvB,8OAAC,iIAAA,CAAA,mBAAgB;sCAAE;;;;;;;;;;;;8BAIvB,8OAAC;oBAAK,UAAU;oBAAc,WAAU;;sCACtC,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAAiC;sDACxD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEzC,8OAAC,iIAAA,CAAA,QAAK;oCACJ,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oCACxC,aAAY;oCACZ,WAAU;oCACV,QAAQ;;;;;;;;;;;;sCAIZ,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;8CAAiC;;;;;;8CAGxE,8OAAC,oIAAA,CAAA,WAAQ;oCACP,IAAG;oCACH,OAAO;oCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;oCAC9C,aAAY;oCACZ,WAAU;;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAU,WAAU;sDAAiC;;;;;;sDAGpE,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;;kEACN,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,OAAO;kEACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4DACL,SAAQ;4DACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,CAAC,WAAW;;8EAGd,8OAAC,8MAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;gEACvB,UAAU,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,OAAO;oEAAE,QAAQ,2IAAA,CAAA,KAAE;gEAAC,mBAAK,8OAAC;8EAAK;;;;;;;;;;;;;;;;;kEAG9D,8OAAC,mIAAA,CAAA,iBAAc;wDAAC,WAAU;kEACxB,cAAA,8OAAC,oIAAA,CAAA,WAAQ;4DACP,MAAK;4DACL,UAAU;4DACV,UAAU;4DACV,YAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CAOtB,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAiC;;;;;;sDAGrE,8OAAC,kIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAU,eAAe,CAAC,QAAU,YAAY;;8DAC7D,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;8DACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,8OAAC,kIAAA,CAAA,gBAAa;oDAAC,WAAU;;sEACvB,8OAAC,kIAAA,CAAA,aAAU;4DAAqB,OAAM;sEAAO;2DAA7B;;;;;sEAGhB,8OAAC,kIAAA,CAAA,aAAU;4DAAuB,OAAM;sEAAS;2DAAjC;;;;;sEAGhB,8OAAC,kIAAA,CAAA,aAAU;4DAAoB,OAAM;sEAAM;2DAA3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQxB,8OAAC;;8CACC,8OAAC;oCAAM,SAAQ;oCAAW,WAAU;;sDAClC,8OAAC;sDAAK;;;;;;wCACL,oCACC,8OAAC;4CAAK,WAAU;sDAAgC;;;;;mDAC9C,gCACF,8OAAC,kIAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,WAAU;;8DAEV,8OAAC,gNAAA,CAAA,YAAS;oDAAC,WAAU;;;;;;gDAAY;;;;;;mDAEjC;;;;;;;gCAGL,gCACC,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAc,WAAU;;sDACrC,8OAAC,oNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;sDACvB,8OAAC,iIAAA,CAAA,mBAAgB;4CAAC,WAAU;sDAAW;;;;;;;;;;;2CAEvC,oCACF,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;yDAEpB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,OAAO,YAAY;oCAAQ,eAAe;;sDAChD,8OAAC,kIAAA,CAAA,gBAAa;4CAAC,WAAU;sDACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;gDAAC,aAAY;;;;;;;;;;;sDAE3B,8OAAC,kIAAA,CAAA,gBAAa;4CAAC,WAAU;;8DACvB,8OAAC,kIAAA,CAAA,aAAU;oDAAqB,OAAM;8DAAO;mDAA7B;;;;;gDAGf,WAAW,GAAG,CAAC,CAAC,KAAK;oDACpB,MAAM,YAAY,CAAC,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,GAAG,IAAI;oDAC7D,qBACE,8OAAC,kIAAA,CAAA,aAAU;wDAET,OAAO,IAAI,EAAE;kEAEZ,IAAI,IAAI;uDAHJ;;;;;gDAMX;;;;;;;;;;;;;;;;;;;sCAMR,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,UAAU,eAAe,CAAC,MAAM,IAAI;gCACpC,WAAU;;oCAET,4BAAc,8OAAC,iNAAA,CAAA,UAAO;wCAAC,WAAU;;;;;6DAA4B,8OAAC,4MAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAa;;;;;;;;;;;;sCAKpG,8OAAC,kIAAA,CAAA,eAAY;4BAAC,WAAU;;8CACtB,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS;oCACT,UAAU;8CACX;;;;;;8CAGD,8OAAC,kIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,UAAU,aAAa,CAAC,MAAM,IAAI;oCAClC,WAAU;;wCAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAChC,YAAY,gBAAgB,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/D", "debugId": null}}, {"offset": {"line": 5276, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/task/task-item.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Task, Category } from '@/lib/types';\nimport { Checkbox } from '@/components/ui/checkbox';\nimport { Button } from '@/components/ui/button';\nimport { TaskService } from '@/lib/services/task-service';\nimport { CategoryService } from '@/lib/services/category-service';\nimport { format } from 'date-fns';\nimport { vi } from 'date-fns/locale';\nimport { \n  Tooltip,\n  TooltipContent,\n  TooltipProvider,\n  TooltipTrigger,\n} from '@/components/ui/tooltip';\nimport { Edit, Trash2, CalendarClock, Tag, Loader2 } from 'lucide-react';\nimport { TaskForm } from './task-form';\nimport { Dialog, DialogContent, DialogDescription, DialogHeader, DialogFooter, DialogTitle } from '@/components/ui/dialog';\nimport { useStatisticsRefresh } from '@/lib/contexts/statistics-context';\n\ninterface TaskItemProps {\n  task: Task;\n  onDelete: (id: string) => void;\n}\n\nexport function TaskItem({ task: initialTask, onDelete }: TaskItemProps) {\n  const refreshStatistics = useStatisticsRefresh();\n  // Thêm state để theo dõi trạng thái của task bên trong component\n  const [task, setTask] = useState<Task>(initialTask);\n  const [isLoading, setIsLoading] = useState(false);\n  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);\n  const [category, setCategory] = useState<Category | undefined>(undefined);\n  const [categoryLoading, setCategoryLoading] = useState(false);\n\n  // Debug: Log task data khi component mount\n  useEffect(() => {\n    console.log(`TaskItem: Task data for \"${task.title}\":`, {\n      id: task.id,\n      title: task.title,\n      category: task.category,\n      priority: task.priority,\n      dueDate: task.dueDate,\n      status: task.status\n    });\n  }, [task.id]);\n\n  // Tải thông tin danh mục nếu task có category\n  useEffect(() => {\n    const loadCategory = async () => {\n      console.log(`TaskItem: useEffect triggered for task \"${task.title}\" with category: ${task.category}`);\n\n      if (!task.category) {\n        console.log(`TaskItem: Task \"${task.title}\" không có category`);\n        setCategory(undefined);\n        return;\n      }\n\n      console.log(`TaskItem: Đang tải danh mục với ID: ${task.category} cho task \"${task.title}\"`);\n      setCategoryLoading(true);\n      try {\n        const categoryData = await CategoryService.getCategory(task.category);\n        console.log(`TaskItem: Đã tải danh mục cho task \"${task.title}\":`, categoryData);\n        setCategory(categoryData);\n      } catch (error) {\n        console.error(`TaskItem: Lỗi khi tải thông tin danh mục với ID ${task.category} cho task \"${task.title}\":`, error);\n        setCategory(undefined);\n      } finally {\n        setCategoryLoading(false);\n      }\n    };\n\n    loadCategory();\n  }, [task.category, task.title]);\n  \n  const priorityColors = {\n    high: 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400 border-red-200 dark:border-red-900/30',\n    medium: 'bg-amber-50 text-amber-800 dark:bg-amber-900/20 dark:text-amber-400 border-amber-200 dark:border-amber-900/30',\n    low: 'bg-green-50 text-green-800 dark:bg-green-900/20 dark:text-green-400 border-green-200 dark:border-green-900/30',\n  };\n\n  const priorityLabels = {\n    high: 'Cao',\n    medium: 'Trung bình',\n    low: 'Thấp',\n  };\n  \n  const handleToggleComplete = async () => {\n    setIsLoading(true);\n    try {\n      // Lấy ID từ task.id hoặc task._id\n      const taskId = task.id || (task as any)._id;\n      console.log('TaskItem: Toggle completion for task:', task);\n      console.log('TaskItem: Task ID:', taskId);\n\n      if (!taskId) {\n        throw new Error('Không tìm thấy ID của công việc');\n      }\n\n      // Gọi API để cập nhật trạng thái hoàn thành\n      const updatedTask = await TaskService.toggleTaskCompletion(taskId);\n\n      // Cập nhật trạng thái local để UI cập nhật ngay lập tức\n      setTask(updatedTask);\n\n      // Trigger statistics refresh\n      refreshStatistics();\n    } catch (error) {\n      console.error('TaskItem: Lỗi khi cập nhật task:', error);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n  \n  const [showConfirm, setShowConfirm] = useState(false);\n  const handleDelete = async () => {\n    setIsLoading(true);\n    try {\n      // Xóa task qua API\n      await TaskService.deleteTask(task.id);\n\n      // Trigger statistics refresh\n      refreshStatistics();\n\n      // Gọi callback để cha cập nhật state\n      onDelete(task.id);\n    } catch (error) {\n      console.error('Lỗi khi xóa task:', error);\n    } finally {\n      setIsLoading(false);\n      setShowConfirm(false);\n    }\n  };\n\n  // Cập nhật task từ form edit\n  const handleTaskUpdated = (updatedTask?: Task) => {\n    if (updatedTask) {\n      setTask(updatedTask);\n    }\n    setIsEditDialogOpen(false);\n  };\n  \n  return (\n    <>\n      <div className={`rounded-lg border p-4 backdrop-blur-sm transition-all duration-200 hover:shadow-md ${\n        task.completed \n          ? 'bg-muted/40' \n          : 'bg-background/80'\n      }`}>\n        <div className=\"flex items-start gap-4\">\n          <Checkbox \n            id={`task-${task.id}`} \n            checked={task.completed}\n            onCheckedChange={handleToggleComplete}\n            disabled={isLoading}\n            className=\"mt-1\"\n          />\n          <div className=\"grid flex-1 gap-1\">\n            <div className=\"flex flex-wrap items-start justify-between gap-2\">\n              <h3 className={`font-medium ${task.completed ? 'line-through text-muted-foreground' : ''}`}>\n                {task.title}\n              </h3>\n              <div className=\"flex flex-shrink-0 gap-1\">\n                {isLoading && (\n                  <div className=\"flex items-center\">\n                    <Loader2 className=\"h-4 w-4 animate-spin mr-2\" />\n                  </div>\n                )}\n                \n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <Button\n                        variant=\"ghost\" \n                        size=\"icon\"\n                        onClick={() => setIsEditDialogOpen(true)}\n                        disabled={isLoading}\n                        className=\"h-8 w-8 text-muted-foreground hover:bg-accent/50\"\n                      >\n                        <Edit className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Chỉnh sửa</span>\n                      </Button>\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Chỉnh sửa công việc</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n                \n                <TooltipProvider>\n                  <Tooltip>\n                    <TooltipTrigger asChild>\n                      <Button\n                        variant=\"ghost\"\n                        size=\"icon\"\n                        onClick={() => setShowConfirm(true)}\n                        disabled={isLoading}\n                        className=\"h-8 w-8 text-muted-foreground hover:text-destructive hover:bg-destructive/10\"\n                      >\n                        <Trash2 className=\"h-4 w-4\" />\n                        <span className=\"sr-only\">Xóa</span>\n                      </Button>\n                    </TooltipTrigger>\n                    <TooltipContent>\n                      <p>Xóa công việc</p>\n                    </TooltipContent>\n                  </Tooltip>\n                </TooltipProvider>\n                <Dialog open={showConfirm} onOpenChange={setShowConfirm}>\n                  <DialogContent>\n                    <DialogHeader>\n                      <DialogTitle>Bạn có chắc chắn muốn xóa công việc này?</DialogTitle>\n                      <DialogDescription>\n                        Hành động này không thể hoàn tác. Công việc sẽ bị xóa vĩnh viễn.\n                      </DialogDescription>\n                    </DialogHeader>\n                    <DialogFooter>\n                      <Button variant=\"ghost\" onClick={() => setShowConfirm(false)}>Hủy</Button>\n                      <Button \n                        variant=\"destructive\" \n                        onClick={handleDelete} \n                        disabled={isLoading}\n                        className=\"gap-2\"\n                      >\n                        {isLoading && <Loader2 className=\"h-4 w-4 animate-spin\" />}\n                        Xóa\n                      </Button>\n                    </DialogFooter>\n                  </DialogContent>\n                </Dialog>\n              </div>\n            </div>\n            \n            {task.description && (\n              <p className=\"text-sm text-muted-foreground\">\n                {task.description}\n              </p>\n            )}\n            \n            <div className=\"mt-2 flex flex-wrap gap-2\">\n              <div className={`inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold ${priorityColors[task.priority]}`}>\n                {priorityLabels[task.priority]}\n              </div>\n              \n              {categoryLoading ? (\n                <div className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold\">\n                  <Loader2 className=\"mr-1 h-3 w-3 animate-spin\" />\n                  Đang tải...\n                </div>\n              ) : category ? (\n                <div \n                  className=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold\"\n                  style={{ \n                    backgroundColor: `${category.color}10`, \n                    color: category.color,\n                    borderColor: `${category.color}30`\n                  }}\n                >\n                  <Tag className=\"mr-1 h-3 w-3\" />\n                  {category.name}\n                </div>\n              ) : null}\n              \n              {task.dueDate && (\n                <div className=\"inline-flex items-center rounded-full border border-blue-200 bg-blue-50 px-2.5 py-0.5 text-xs font-semibold text-blue-800 dark:border-blue-900/30 dark:bg-blue-900/20 dark:text-blue-400\">\n                  <CalendarClock className=\"mr-1 h-3 w-3\" />\n                  {format(new Date(task.dueDate), 'PPP', { locale: vi })}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n      \n      <TaskForm\n        isOpen={isEditDialogOpen}\n        onClose={() => setIsEditDialogOpen(false)}\n        task={task}\n        onAdded={handleTaskUpdated}\n      />\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAMA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAnBA;;;;;;;;;;;;;;AA0BO,SAAS,SAAS,EAAE,MAAM,WAAW,EAAE,QAAQ,EAAiB;IACrE,MAAM,oBAAoB,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD;IAC7C,iEAAiE;IACjE,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAQ;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAwB;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,2CAA2C;IAC3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;YACtD,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,UAAU,KAAK,QAAQ;YACvB,UAAU,KAAK,QAAQ;YACvB,SAAS,KAAK,OAAO;YACrB,QAAQ,KAAK,MAAM;QACrB;IACF,GAAG;QAAC,KAAK,EAAE;KAAC;IAEZ,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,QAAQ,GAAG,CAAC,CAAC,wCAAwC,EAAE,KAAK,KAAK,CAAC,iBAAiB,EAAE,KAAK,QAAQ,EAAE;YAEpG,IAAI,CAAC,KAAK,QAAQ,EAAE;gBAClB,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,KAAK,KAAK,CAAC,mBAAmB,CAAC;gBAC9D,YAAY;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,CAAC,CAAC;YAC3F,mBAAmB;YACnB,IAAI;gBACF,MAAM,eAAe,MAAM,6IAAA,CAAA,kBAAe,CAAC,WAAW,CAAC,KAAK,QAAQ;gBACpE,QAAQ,GAAG,CAAC,CAAC,oCAAoC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;gBACnE,YAAY;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,CAAC,gDAAgD,EAAE,KAAK,QAAQ,CAAC,WAAW,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,EAAE;gBAC5G,YAAY;YACd,SAAU;gBACR,mBAAmB;YACrB;QACF;QAEA;IACF,GAAG;QAAC,KAAK,QAAQ;QAAE,KAAK,KAAK;KAAC;IAE9B,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,KAAK;IACP;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,QAAQ;QACR,KAAK;IACP;IAEA,MAAM,uBAAuB;QAC3B,aAAa;QACb,IAAI;YACF,kCAAkC;YAClC,MAAM,SAAS,KAAK,EAAE,IAAI,AAAC,KAAa,GAAG;YAC3C,QAAQ,GAAG,CAAC,yCAAyC;YACrD,QAAQ,GAAG,CAAC,sBAAsB;YAElC,IAAI,CAAC,QAAQ;gBACX,MAAM,IAAI,MAAM;YAClB;YAEA,4CAA4C;YAC5C,MAAM,cAAc,MAAM,yIAAA,CAAA,cAAW,CAAC,oBAAoB,CAAC;YAE3D,wDAAwD;YACxD,QAAQ;YAER,6BAA6B;YAC7B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,eAAe;QACnB,aAAa;QACb,IAAI;YACF,mBAAmB;YACnB,MAAM,yIAAA,CAAA,cAAW,CAAC,UAAU,CAAC,KAAK,EAAE;YAEpC,6BAA6B;YAC7B;YAEA,qCAAqC;YACrC,SAAS,KAAK,EAAE;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qBAAqB;QACrC,SAAU;YACR,aAAa;YACb,eAAe;QACjB;IACF;IAEA,6BAA6B;IAC7B,MAAM,oBAAoB,CAAC;QACzB,IAAI,aAAa;YACf,QAAQ;QACV;QACA,oBAAoB;IACtB;IAEA,qBACE;;0BACE,8OAAC;gBAAI,WAAW,CAAC,mFAAmF,EAClG,KAAK,SAAS,GACV,gBACA,oBACJ;0BACA,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,oIAAA,CAAA,WAAQ;4BACP,IAAI,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;4BACrB,SAAS,KAAK,SAAS;4BACvB,iBAAiB;4BACjB,UAAU;4BACV,WAAU;;;;;;sCAEZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAW,CAAC,YAAY,EAAE,KAAK,SAAS,GAAG,uCAAuC,IAAI;sDACvF,KAAK,KAAK;;;;;;sDAEb,8OAAC;4CAAI,WAAU;;gDACZ,2BACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,iNAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;;;;;;8DAIvB,8OAAC,mIAAA,CAAA,kBAAe;8DACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0EACN,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,oBAAoB;oEACnC,UAAU;oEACV,WAAU;;sFAEV,8OAAC,2MAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;sFAChB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;0EAG9B,8OAAC,mIAAA,CAAA,iBAAc;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;8DAKT,8OAAC,mIAAA,CAAA,kBAAe;8DACd,cAAA,8OAAC,mIAAA,CAAA,UAAO;;0EACN,8OAAC,mIAAA,CAAA,iBAAc;gEAAC,OAAO;0EACrB,cAAA,8OAAC,kIAAA,CAAA,SAAM;oEACL,SAAQ;oEACR,MAAK;oEACL,SAAS,IAAM,eAAe;oEAC9B,UAAU;oEACV,WAAU;;sFAEV,8OAAC,0MAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;sFAClB,8OAAC;4EAAK,WAAU;sFAAU;;;;;;;;;;;;;;;;;0EAG9B,8OAAC,mIAAA,CAAA,iBAAc;0EACb,cAAA,8OAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;8DAIT,8OAAC,kIAAA,CAAA,SAAM;oDAAC,MAAM;oDAAa,cAAc;8DACvC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,eAAY;;kFACX,8OAAC,kIAAA,CAAA,cAAW;kFAAC;;;;;;kFACb,8OAAC,kIAAA,CAAA,oBAAiB;kFAAC;;;;;;;;;;;;0EAIrB,8OAAC,kIAAA,CAAA,eAAY;;kFACX,8OAAC,kIAAA,CAAA,SAAM;wEAAC,SAAQ;wEAAQ,SAAS,IAAM,eAAe;kFAAQ;;;;;;kFAC9D,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,SAAS;wEACT,UAAU;wEACV,WAAU;;4EAET,2BAAa,8OAAC,iNAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;4EAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAStE,KAAK,WAAW,kBACf,8OAAC;oCAAE,WAAU;8CACV,KAAK,WAAW;;;;;;8CAIrB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,iFAAiF,EAAE,cAAc,CAAC,KAAK,QAAQ,CAAC,EAAE;sDAChI,cAAc,CAAC,KAAK,QAAQ,CAAC;;;;;;wCAG/B,gCACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,iNAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;gDAA8B;;;;;;mDAGjD,yBACF,8OAAC;4CACC,WAAU;4CACV,OAAO;gDACL,iBAAiB,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC;gDACtC,OAAO,SAAS,KAAK;gDACrB,aAAa,GAAG,SAAS,KAAK,CAAC,EAAE,CAAC;4CACpC;;8DAEA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDACd,SAAS,IAAI;;;;;;mDAEd;wCAEH,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDACxB,CAAA,GAAA,qJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,KAAK,KAAK,OAAO,GAAG,OAAO;oDAAE,QAAQ,2IAAA,CAAA,KAAE;gDAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhE,8OAAC,0IAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,oBAAoB;gBACnC,MAAM;gBACN,SAAS;;;;;;;;AAIjB", "debugId": null}}, {"offset": {"line": 5792, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SwitchPrimitives from \"@radix-ui/react-switch\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Switch = React.forwardRef<\n  React.ElementRef<typeof SwitchPrimitives.Root>,\n  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>\n>(({ className, ...props }, ref) => (\n  <SwitchPrimitives.Root\n    className={cn(\n      \"peer inline-flex h-6 w-11 shrink-0 cursor-pointer items-center rounded-full border-2 border-muted-foreground/30 transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:border-primary data-[state=unchecked]:bg-muted/20 dark:data-[state=unchecked]:bg-muted/10\",\n      className,\n    )}\n    {...props}\n    ref={ref}\n  >\n    <SwitchPrimitives.Thumb\n      className={cn(\n        \"pointer-events-none block h-4 w-4 rounded-full bg-white shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-5 data-[state=unchecked]:translate-x-0\",\n      )}\n    />\n  </SwitchPrimitives.Root>\n))\nSwitch.displayName = SwitchPrimitives.Root.displayName\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG5B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,kKAAA,CAAA,OAAqB;QACpB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6cACA;QAED,GAAG,KAAK;QACT,KAAK;kBAEL,cAAA,8OAAC,kKAAA,CAAA,QAAsB;YACrB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV;;;;;;;;;;;AAKR,OAAO,WAAW,GAAG,kKAAA,CAAA,OAAqB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5828, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst labelVariants = cva(\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\",\n)\n\nconst Label = React.forwardRef<\n  React.ElementRef<typeof LabelPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\n    VariantProps<typeof labelVariants>\n>(({ className, ...props }, ref) => (\n  <LabelPrimitive.Root\n    ref={ref}\n    className={cn(labelVariants(), className)}\n    {...props}\n  />\n))\nLabel.displayName = LabelPrimitive.Root.displayName\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 5860, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/components/task/task-list.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { Task } from '@/lib/types';\nimport { TaskItem } from './task-item';\nimport { TaskForm } from './task-form';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { TaskService } from '@/lib/services/task-service';\nimport { PreferenceService } from '@/lib/services/preference-service';\nimport { Switch } from '@/components/ui/switch';\nimport { Label } from '@/components/ui/label';\nimport { PlusCircle, Search, Filter, Loader2, RefreshCw } from 'lucide-react';\nimport { Skeleton } from '@/components/ui/skeleton';\nimport { Alert, AlertDescription } from '@/components/ui/alert';\n\ninterface TaskListProps {\n  tasks?: Task[];\n  setTasks?: (tasks: Task[]) => void;\n  isLoading?: boolean;\n  error?: string | null;\n  onRefresh?: () => void;\n}\n\nexport function TaskList({\n  tasks: propTasks,\n  setTasks: propSetTasks,\n  isLoading: propIsLoading,\n  error: propError,\n  onRefresh\n}: TaskListProps = {}) {\n  const [internalTasks, setInternalTasks] = useState<Task[]>([]);\n  const tasks = propTasks ?? internalTasks;\n  const setTasks = propSetTasks ?? setInternalTasks;\n  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);\n  const [isDialogOpen, setIsDialogOpen] = useState(false);\n  const [searchTerm, setSearchTerm] = useState('');\n  const [preferences, setPreferences] = useState({ showCompletedTasks: true });\n  const [internalIsLoading, setInternalIsLoading] = useState(true);\n  const [internalError, setInternalError] = useState<string | null>(null);\n  const [isPreferencesLoading, setIsPreferencesLoading] = useState(true);\n\n  // Sử dụng props hoặc internal state\n  const isLoading = propIsLoading ?? internalIsLoading;\n  const error = propError ?? internalError;\n\n  // Tải preferences từ API\n  useEffect(() => {\n    const loadPreferences = async () => {\n      setIsPreferencesLoading(true);\n      try {\n        // Sử dụng phiên bản đồng bộ cho lần render đầu tiên\n        const cachedPrefs = PreferenceService.getPreferencesSync();\n        setPreferences(cachedPrefs);\n        \n        // Sau đó tải bản mới nhất từ API\n        const prefs = await PreferenceService.getPreferences();\n        setPreferences(prefs);\n      } catch (error) {\n        console.error('Lỗi khi tải preferences:', error);\n      } finally {\n        setIsPreferencesLoading(false);\n      }\n    };\n    \n    loadPreferences();\n  }, []);\n  \n  // Tải danh sách công việc từ API\n  useEffect(() => {\n    if (!propTasks) {\n      loadTasks();\n    }\n  }, [propTasks]);\n  \n  // Lọc và sắp xếp công việc\n  useEffect(() => {\n    filterTasks();\n  }, [tasks, searchTerm, preferences.showCompletedTasks]);\n  \n  const loadTasks = async () => {\n    if (onRefresh) {\n      // Nếu có onRefresh từ parent, sử dụng nó\n      onRefresh();\n    } else {\n      // Nếu không, sử dụng internal logic\n      setInternalIsLoading(true);\n      setInternalError(null);\n      try {\n        const fetchedTasks = await TaskService.getTasks();\n        setTasks(fetchedTasks);\n      } catch (err) {\n        console.error('Lỗi khi tải danh sách công việc:', err);\n        setInternalError('Không thể tải danh sách công việc. Vui lòng thử lại sau.');\n      } finally {\n        setInternalIsLoading(false);\n      }\n    }\n  };\n  \n  const filterTasks = () => {\n    let filtered = [...tasks];\n    \n    // Lọc theo trạng thái hoàn thành\n    if (!preferences.showCompletedTasks) {\n      filtered = filtered.filter(task => !task.completed);\n    }\n    \n    // Lọc theo từ khóa tìm kiếm\n    if (searchTerm) {\n      const lowercaseSearch = searchTerm.toLowerCase();\n      filtered = filtered.filter(task => \n        task.title.toLowerCase().includes(lowercaseSearch) || \n        (task.description && task.description.toLowerCase().includes(lowercaseSearch))\n      );\n    }\n    \n    // Sắp xếp theo thời gian tạo (mới nhất lên đầu)\n    filtered.sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());\n    \n    setFilteredTasks(filtered);\n  };\n  \n  const handleToggleShowCompleted = async () => {\n    setIsPreferencesLoading(true);\n    try {\n      const updatedPreferences = await PreferenceService.updatePreferences({\n        showCompletedTasks: !preferences.showCompletedTasks\n      });\n      setPreferences(updatedPreferences);\n    } catch (error) {\n      console.error('Lỗi khi cập nhật preferences:', error);\n    } finally {\n      setIsPreferencesLoading(false);\n    }\n  };\n  \n  const handleDeleteTask = async (id: string) => {\n    // TaskItem đã xử lý việc gọi API để xóa task\n    // Chỉ cần cập nhật state local\n    setTasks(tasks.filter(t => t.id !== id));\n  };\n  \n  return (\n    <div className=\"max-w-4xl mx-auto space-y-6\">\n      <div className=\"flex flex-wrap items-center justify-between gap-4\">\n        <h1 className=\"text-3xl font-bold tracking-tight\">Danh sách công việc</h1>\n        <Button onClick={() => setIsDialogOpen(true)} className=\"flex items-center gap-2\">\n          <PlusCircle className=\"h-4 w-4\" />\n          Thêm công việc\n        </Button>\n      </div>\n      \n      {/* Công cụ tìm kiếm và lọc */}\n      <div className=\"flex flex-col sm:flex-row gap-4 sm:items-center justify-between\">\n        <div className=\"relative flex-1 max-w-md\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n          <Input\n            type=\"text\"\n            placeholder=\"Tìm kiếm công việc...\"\n            className=\"w-full pl-9\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n          />\n        </div>\n        \n        <div className=\"flex items-center gap-2\">\n          <div className=\"flex items-center p-2 px-3 border rounded-lg bg-background/80 backdrop-blur-sm\">\n            <Switch\n              id=\"show-completed\"\n              checked={preferences.showCompletedTasks}\n              onCheckedChange={handleToggleShowCompleted}\n              disabled={isPreferencesLoading}\n              className=\"mr-3\"\n            />\n            <Label htmlFor=\"show-completed\" className=\"text-sm font-medium cursor-pointer flex items-center\">\n              {isPreferencesLoading && <Loader2 className=\"h-3 w-3 animate-spin mr-2\" />}\n              Hiển thị công việc đã hoàn thành\n            </Label>\n          </div>\n        </div>\n      </div>\n      \n      {/* Hiển thị lỗi nếu có */}\n      {error && (\n        <Alert variant=\"destructive\" className=\"mb-4\">\n          <AlertDescription className=\"flex items-center justify-between\">\n            <span>{error}</span>\n            <Button \n              variant=\"outline\" \n              size=\"sm\" \n              onClick={loadTasks}\n              className=\"flex items-center gap-1\"\n            >\n              <RefreshCw className=\"h-3 w-3\" />\n              Thử lại\n            </Button>\n          </AlertDescription>\n        </Alert>\n      )}\n      \n      {/* Trạng thái loading */}\n      {isLoading ? (\n        <div className=\"grid grid-cols-1 gap-4\">\n          {[1, 2, 3].map((i) => (\n            <div key={i} className=\"rounded-lg border p-4\">\n              <div className=\"flex items-start gap-4\">\n                <Skeleton className=\"h-4 w-4 rounded-sm mt-1\" />\n                <div className=\"grid flex-1 gap-2\">\n                  <div className=\"flex items-center justify-between\">\n                    <Skeleton className=\"h-5 w-40\" />\n                    <div className=\"flex gap-2\">\n                      <Skeleton className=\"h-8 w-8 rounded-md\" />\n                      <Skeleton className=\"h-8 w-8 rounded-md\" />\n                    </div>\n                  </div>\n                  <Skeleton className=\"h-4 w-full max-w-[250px]\" />\n                  <div className=\"flex gap-2 mt-1\">\n                    <Skeleton className=\"h-5 w-16 rounded-full\" />\n                    <Skeleton className=\"h-5 w-20 rounded-full\" />\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : filteredTasks.length === 0 ? (\n        <div className=\"rounded-lg border backdrop-blur-sm bg-background/70 shadow-sm\" data-v0-t=\"card\">\n          <div className=\"flex flex-col items-center justify-center space-y-3 p-12 text-center\">\n            <div className=\"rounded-full bg-primary/10 p-3\">\n              <svg\n                xmlns=\"http://www.w3.org/2000/svg\"\n                width=\"24\"\n                height=\"24\"\n                viewBox=\"0 0 24 24\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                strokeWidth=\"2\"\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                className=\"h-6 w-6 text-primary\"\n              >\n                <path d=\"M11 12H3\" />\n                <path d=\"M16 6H3\" />\n                <path d=\"M16 18H3\" />\n                <path d=\"M18 9v6\" />\n                <path d=\"M21 12h-6\" />\n              </svg>\n            </div>\n            <div className=\"space-y-1\">\n              <h3 className=\"text-lg font-semibold\">Không có công việc nào</h3>\n              <p className=\"text-sm text-muted-foreground\">\n                {searchTerm \n                  ? 'Không tìm thấy công việc phù hợp với từ khóa tìm kiếm.' \n                  : 'Bạn chưa có công việc nào. Hãy thêm công việc mới để bắt đầu.'}\n              </p>\n            </div>\n            <Button onClick={() => setIsDialogOpen(true)} className=\"flex items-center gap-2\">\n              <PlusCircle className=\"h-4 w-4\" />\n              Thêm công việc mới\n            </Button>\n          </div>\n        </div>\n      ) : (\n        <div className=\"grid grid-cols-1 gap-4\">\n          {filteredTasks.map((task, index) => {\n            const taskId = task.id || (task as any)._id || `task-${index}`;\n            return (\n              <TaskItem\n                key={taskId}\n                task={task}\n                onDelete={handleDeleteTask}\n              />\n            );\n          })}\n        </div>\n      )}\n      \n      <TaskForm\n        isOpen={isDialogOpen}\n        onClose={() => setIsDialogOpen(false)}\n        onAdded={(newTask) => {\n          // Thêm task mới vào danh sách\n          if (newTask) {\n            setTasks([newTask, ...tasks]);\n            // Nếu có onRefresh từ parent, gọi để sync data\n            if (onRefresh) {\n              onRefresh();\n            }\n          }\n        }}\n      />\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAdA;;;;;;;;;;;;;;AAwBO,SAAS,SAAS,EACvB,OAAO,SAAS,EAChB,UAAU,YAAY,EACtB,WAAW,aAAa,EACxB,OAAO,SAAS,EAChB,SAAS,EACK,GAAG,CAAC,CAAC;IACnB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,QAAQ,aAAa;IAC3B,MAAM,WAAW,gBAAgB;IACjC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,oBAAoB;IAAK;IAC1E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjE,oCAAoC;IACpC,MAAM,YAAY,iBAAiB;IACnC,MAAM,QAAQ,aAAa;IAE3B,yBAAyB;IACzB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,wBAAwB;YACxB,IAAI;gBACF,oDAAoD;gBACpD,MAAM,cAAc,+IAAA,CAAA,oBAAiB,CAAC,kBAAkB;gBACxD,eAAe;gBAEf,iCAAiC;gBACjC,MAAM,QAAQ,MAAM,+IAAA,CAAA,oBAAiB,CAAC,cAAc;gBACpD,eAAe;YACjB,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,4BAA4B;YAC5C,SAAU;gBACR,wBAAwB;YAC1B;QACF;QAEA;IACF,GAAG,EAAE;IAEL,iCAAiC;IACjC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd;QACF;IACF,GAAG;QAAC;KAAU;IAEd,2BAA2B;IAC3B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;QAAO;QAAY,YAAY,kBAAkB;KAAC;IAEtD,MAAM,YAAY;QAChB,IAAI,WAAW;YACb,yCAAyC;YACzC;QACF,OAAO;YACL,oCAAoC;YACpC,qBAAqB;YACrB,iBAAiB;YACjB,IAAI;gBACF,MAAM,eAAe,MAAM,yIAAA,CAAA,cAAW,CAAC,QAAQ;gBAC/C,SAAS;YACX,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,iBAAiB;YACnB,SAAU;gBACR,qBAAqB;YACvB;QACF;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,WAAW;eAAI;SAAM;QAEzB,iCAAiC;QACjC,IAAI,CAAC,YAAY,kBAAkB,EAAE;YACnC,WAAW,SAAS,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS;QACpD;QAEA,4BAA4B;QAC5B,IAAI,YAAY;YACd,MAAM,kBAAkB,WAAW,WAAW;YAC9C,WAAW,SAAS,MAAM,CAAC,CAAA,OACzB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,oBACjC,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC;QAEjE;QAEA,gDAAgD;QAChD,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAEvF,iBAAiB;IACnB;IAEA,MAAM,4BAA4B;QAChC,wBAAwB;QACxB,IAAI;YACF,MAAM,qBAAqB,MAAM,+IAAA,CAAA,oBAAiB,CAAC,iBAAiB,CAAC;gBACnE,oBAAoB,CAAC,YAAY,kBAAkB;YACrD;YACA,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,wBAAwB;QAC1B;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,6CAA6C;QAC7C,+BAA+B;QAC/B,SAAS,MAAM,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;IACtC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,8OAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS,IAAM,gBAAgB;wBAAO,WAAU;;0CACtD,8OAAC,kNAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,aAAY;gCACZ,WAAU;gCACV,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;kCAIjD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,kIAAA,CAAA,SAAM;oCACL,IAAG;oCACH,SAAS,YAAY,kBAAkB;oCACvC,iBAAiB;oCACjB,UAAU;oCACV,WAAU;;;;;;8CAEZ,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAiB,WAAU;;wCACvC,sCAAwB,8OAAC,iNAAA,CAAA,UAAO;4CAAC,WAAU;;;;;;wCAA+B;;;;;;;;;;;;;;;;;;;;;;;;YAQlF,uBACC,8OAAC,iIAAA,CAAA,QAAK;gBAAC,SAAQ;gBAAc,WAAU;0BACrC,cAAA,8OAAC,iIAAA,CAAA,mBAAgB;oBAAC,WAAU;;sCAC1B,8OAAC;sCAAM;;;;;;sCACP,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;;YAQxC,0BACC,8OAAC;gBAAI,WAAU;0BACZ;oBAAC;oBAAG;oBAAG;iBAAE,CAAC,GAAG,CAAC,CAAC,kBACd,8OAAC;wBAAY,WAAU;kCACrB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,oIAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;sEACpB,8OAAC,oIAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;;;;;;;;sDAGxB,8OAAC,oIAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC,oIAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBAdlB;;;;;;;;;uBAqBZ,cAAc,MAAM,KAAK,kBAC3B,8OAAC;gBAAI,WAAU;gBAAgE,aAAU;0BACvF,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,OAAM;gCACN,OAAM;gCACN,QAAO;gCACP,SAAQ;gCACR,MAAK;gCACL,QAAO;gCACP,aAAY;gCACZ,eAAc;gCACd,gBAAe;gCACf,WAAU;;kDAEV,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;kDACR,8OAAC;wCAAK,GAAE;;;;;;;;;;;;;;;;;sCAGZ,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CACV,aACG,2DACA;;;;;;;;;;;;sCAGR,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS,IAAM,gBAAgB;4BAAO,WAAU;;8CACtD,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;gCAAY;;;;;;;;;;;;;;;;;qCAMxC,8OAAC;gBAAI,WAAU;0BACZ,cAAc,GAAG,CAAC,CAAC,MAAM;oBACxB,MAAM,SAAS,KAAK,EAAE,IAAI,AAAC,KAAa,GAAG,IAAI,CAAC,KAAK,EAAE,OAAO;oBAC9D,qBACE,8OAAC,0IAAA,CAAA,WAAQ;wBAEP,MAAM;wBACN,UAAU;uBAFL;;;;;gBAKX;;;;;;0BAIJ,8OAAC,0IAAA,CAAA,WAAQ;gBACP,QAAQ;gBACR,SAAS,IAAM,gBAAgB;gBAC/B,SAAS,CAAC;oBACR,8BAA8B;oBAC9B,IAAI,SAAS;wBACX,SAAS;4BAAC;+BAAY;yBAAM;wBAC5B,+CAA+C;wBAC/C,IAAI,WAAW;4BACb;wBACF;oBACF;gBACF;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 6443, "column": 0}, "map": {"version": 3, "sources": ["file://D%3A/full/KTMNJS/src/app/tasks/page.tsx"], "sourcesContent": ["'use client';\n\nimport { Layout } from '@/components/layout/layout';\nimport { TaskList } from '@/components/task/task-list';\nimport ProjectSelector from '@/components/project/ProjectSelector';\nimport { useEffect, useState } from 'react';\nimport { Task } from '@/lib/types';\nimport { Project } from '@/lib/types';\nimport { TaskService } from '@/lib/services/task-service';\n\nexport default function TasksPage() {\n  const [tasks, setTasks] = useState<Task[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Load tasks từ API\n  const loadTasks = async () => {\n    try {\n      setIsLoading(true);\n      setError(null);\n      console.log('TasksPage: Đang tải tasks từ API...');\n      const tasksData = await TaskService.getTasks();\n      console.log('TasksPage: Đã tải tasks:', tasksData);\n      setTasks(tasksData);\n    } catch (err: any) {\n      console.error('TasksPage: Lỗi tải tasks:', err);\n      setError(err.message || 'Không thể tải danh sách công việc');\n      // Fallback to localStorage nếu API lỗi\n      const stored = localStorage.getItem('tasks');\n      if (stored) {\n        setTasks(JSON.parse(stored));\n      }\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadTasks();\n  }, []);\n\n  return (\n    <Layout>\n      <div className=\"max-w-4xl mx-auto\">\n        <TaskList\n          tasks={tasks}\n          setTasks={setTasks}\n          isLoading={isLoading}\n          error={error}\n          onRefresh={loadTasks}\n        />\n      </div>\n    </Layout>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AAGA;AARA;;;;;;AAUe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU,EAAE;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,oBAAoB;IACpB,MAAM,YAAY;QAChB,IAAI;YACF,aAAa;YACb,SAAS;YACT,QAAQ,GAAG,CAAC;YACZ,MAAM,YAAY,MAAM,yIAAA,CAAA,cAAW,CAAC,QAAQ;YAC5C,QAAQ,GAAG,CAAC,4BAA4B;YACxC,SAAS;QACX,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,SAAS,IAAI,OAAO,IAAI;YACxB,uCAAuC;YACvC,MAAM,SAAS,aAAa,OAAO,CAAC;YACpC,IAAI,QAAQ;gBACV,SAAS,KAAK,KAAK,CAAC;YACtB;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,8OAAC,sIAAA,CAAA,SAAM;kBACL,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,0IAAA,CAAA,WAAQ;gBACP,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,OAAO;gBACP,WAAW;;;;;;;;;;;;;;;;AAKrB", "debugId": null}}]}