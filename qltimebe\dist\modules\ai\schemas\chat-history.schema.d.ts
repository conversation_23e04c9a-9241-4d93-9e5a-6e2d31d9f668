import { Document, Types } from 'mongoose';
export type ChatHistoryDocument = ChatHistory & Document;
export declare class ChatHistory {
    userId: Types.ObjectId;
    role: 'user' | 'assistant';
    content: string;
    timestamp: Date;
    sessionId?: string;
    metadata?: Record<string, any>;
}
export declare const ChatHistorySchema: import("mongoose").Schema<ChatHistory, import("mongoose").Model<ChatHistory, any, any, any, Document<unknown, any, ChatHistory, any> & ChatHistory & {
    _id: Types.ObjectId;
} & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, ChatHistory, Document<unknown, {}, import("mongoose").FlatRecord<ChatHistory>, {}> & import("mongoose").FlatRecord<ChatHistory> & {
    _id: Types.ObjectId;
} & {
    __v: number;
}>;
