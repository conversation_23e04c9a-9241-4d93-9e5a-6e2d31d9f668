import { Model } from 'mongoose';
import { PublicEmailSubscriptionDocument } from '../schemas/public-email-subscription.schema';
import { EmailService } from './email.service';
import { TasksService } from '../../tasks/tasks.service';
import { UsersService } from '../../users/users.service';
export declare class SchedulerService {
    private publicEmailSubscriptionModel;
    private emailService;
    private tasksService;
    private usersService;
    private readonly logger;
    constructor(publicEmailSubscriptionModel: Model<PublicEmailSubscriptionDocument>, emailService: EmailService, tasksService: TasksService, usersService: UsersService);
    sendDailyTaskReminders(): Promise<void>;
    sendUrgentTaskReminders(): Promise<void>;
    private sendPublicTaskReminders;
    private sendUserTaskReminders;
    private shouldSendReminder;
    testSendReminders(): Promise<any>;
}
