"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CalendarController = void 0;
const common_1 = require("@nestjs/common");
const calendar_service_1 = require("./calendar.service");
const jwt_auth_guard_1 = require("../../common/guards/jwt-auth.guard");
const get_user_decorator_1 = require("../../common/decorators/get-user.decorator");
const swagger_1 = require("@nestjs/swagger");
const calendar_query_dto_1 = require("./dto/calendar-query.dto");
let CalendarController = class CalendarController {
    calendarService;
    constructor(calendarService) {
        this.calendarService = calendarService;
    }
    getEvents(user, query) {
        const start = query.start || new Date().toISOString().split('T')[0];
        const end = query.end || new Date(new Date().setMonth(new Date().getMonth() + 1)).toISOString().split('T')[0];
        return this.calendarService.getCalendarData(user._id.toString(), start, end);
    }
    getDayData(user, date) {
        return this.calendarService.getDayData(user._id.toString(), date);
    }
    getWeekData(user, date) {
        return this.calendarService.getWeekData(user._id.toString(), date);
    }
};
exports.CalendarController = CalendarController;
__decorate([
    (0, common_1.Get)('events'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy danh sách sự kiện lịch' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    (0, swagger_1.ApiQuery)({ name: 'start', required: false, description: 'Ngày bắt đầu (YYYY-MM-DD)' }),
    (0, swagger_1.ApiQuery)({ name: 'end', required: false, description: 'Ngày kết thúc (YYYY-MM-DD)' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, calendar_query_dto_1.CalendarQueryDto]),
    __metadata("design:returntype", void 0)
], CalendarController.prototype, "getEvents", null);
__decorate([
    (0, common_1.Get)('day/:date'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy dữ liệu cho một ngày cụ thể' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], CalendarController.prototype, "getDayData", null);
__decorate([
    (0, common_1.Get)('week/:date'),
    (0, swagger_1.ApiOperation)({ summary: 'Lấy dữ liệu cho một tuần (từ ngày được chỉ định)' }),
    (0, swagger_1.ApiResponse)({ status: 200, description: 'Thành công' }),
    (0, swagger_1.ApiResponse)({ status: 401, description: 'Chưa xác thực' }),
    __param(0, (0, get_user_decorator_1.GetUser)()),
    __param(1, (0, common_1.Param)('date')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object, String]),
    __metadata("design:returntype", void 0)
], CalendarController.prototype, "getWeekData", null);
exports.CalendarController = CalendarController = __decorate([
    (0, swagger_1.ApiTags)('Lịch'),
    (0, common_1.Controller)('api/calendar'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)(),
    __metadata("design:paramtypes", [calendar_service_1.CalendarService])
], CalendarController);
//# sourceMappingURL=calendar.controller.js.map