"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TimeBlocksService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const time_block_schema_1 = require("./schemas/time-block.schema");
let TimeBlocksService = class TimeBlocksService {
    timeBlockModel;
    constructor(timeBlockModel) {
        this.timeBlockModel = timeBlockModel;
    }
    async create(createTimeBlockDto, userId) {
        if (createTimeBlockDto.startTime >= createTimeBlockDto.endTime) {
            throw new common_1.BadRequestException('Thời gian bắt đầu phải trước thời gian kết thúc');
        }
        const newTimeBlock = new this.timeBlockModel({
            ...createTimeBlockDto,
            user: userId,
        });
        return newTimeBlock.save();
    }
    async findAll(userId, date) {
        const query = { user: userId };
        if (date) {
            const targetDate = new Date(date);
            const startOfDay = new Date(targetDate.setHours(0, 0, 0, 0));
            const endOfDay = new Date(targetDate.setHours(23, 59, 59, 999));
            query.startTime = {
                $gte: startOfDay,
                $lte: endOfDay,
            };
        }
        return this.timeBlockModel
            .find(query)
            .sort({ startTime: 1 })
            .exec();
    }
    async findById(id, userId) {
        const timeBlock = await this.timeBlockModel
            .findById(id)
            .exec();
        if (!timeBlock) {
            throw new common_1.NotFoundException(`Không tìm thấy khối thời gian với ID: ${id}`);
        }
        if (timeBlock.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền truy cập khối thời gian này');
        }
        return timeBlock;
    }
    async update(id, updateTimeBlockDto, userId) {
        const timeBlock = await this.timeBlockModel.findById(id);
        if (!timeBlock) {
            throw new common_1.NotFoundException(`Không tìm thấy khối thời gian với ID: ${id}`);
        }
        if (timeBlock.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật khối thời gian này');
        }
        if (updateTimeBlockDto.startTime && updateTimeBlockDto.endTime) {
            if (updateTimeBlockDto.startTime >= updateTimeBlockDto.endTime) {
                throw new common_1.BadRequestException('Thời gian bắt đầu phải trước thời gian kết thúc');
            }
        }
        else if (updateTimeBlockDto.startTime && !updateTimeBlockDto.endTime) {
            if (updateTimeBlockDto.startTime >= timeBlock.endTime) {
                throw new common_1.BadRequestException('Thời gian bắt đầu phải trước thời gian kết thúc');
            }
        }
        else if (!updateTimeBlockDto.startTime && updateTimeBlockDto.endTime) {
            if (timeBlock.startTime >= updateTimeBlockDto.endTime) {
                throw new common_1.BadRequestException('Thời gian bắt đầu phải trước thời gian kết thúc');
            }
        }
        const updatedTimeBlock = await this.timeBlockModel
            .findByIdAndUpdate(id, updateTimeBlockDto, { new: true })
            .exec();
        if (!updatedTimeBlock) {
            throw new common_1.NotFoundException(`Không tìm thấy khối thời gian với ID: ${id}`);
        }
        return updatedTimeBlock;
    }
    async remove(id, userId) {
        const timeBlock = await this.timeBlockModel.findById(id);
        if (!timeBlock) {
            throw new common_1.NotFoundException(`Không tìm thấy khối thời gian với ID: ${id}`);
        }
        if (timeBlock.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền xóa khối thời gian này');
        }
        await this.timeBlockModel.findByIdAndDelete(id).exec();
    }
    async toggleCompletion(id, userId) {
        const timeBlock = await this.timeBlockModel.findById(id);
        if (!timeBlock) {
            throw new common_1.NotFoundException(`Không tìm thấy khối thời gian với ID: ${id}`);
        }
        if (timeBlock.user.toString() !== userId) {
            throw new common_1.ForbiddenException('Bạn không có quyền cập nhật khối thời gian này');
        }
        timeBlock.isCompleted = !timeBlock.isCompleted;
        return timeBlock.save();
    }
};
exports.TimeBlocksService = TimeBlocksService;
exports.TimeBlocksService = TimeBlocksService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(time_block_schema_1.TimeBlock.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], TimeBlocksService);
//# sourceMappingURL=time-blocks.service.js.map