import { ProjectsService } from './projects.service';
import { CreateProjectDto } from './dto/create-project.dto';
import { UpdateProjectDto } from './dto/update-project.dto';
import { UserDocument } from '../users/schemas/user.schema';
export declare class ProjectsController {
    private readonly projectsService;
    constructor(projectsService: ProjectsService);
    create(createProjectDto: CreateProjectDto, user: UserDocument): Promise<import("./schemas/project.schema").Project>;
    findAll(user: UserDocument): Promise<import("./schemas/project.schema").Project[]>;
    findOne(id: string, user: UserDocument): Promise<import("./schemas/project.schema").Project>;
    update(id: string, updateProjectDto: UpdateProjectDto, user: UserDocument): Promise<import("./schemas/project.schema").Project>;
    remove(id: string, user: UserDocument): {
        success: boolean;
    };
    getProjectTasks(id: string, user: UserDocument): Promise<{
        message: string;
        projectId: string;
    }>;
}
